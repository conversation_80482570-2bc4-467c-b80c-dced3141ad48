import { sequelize } from '../config/database'
import { QueryInterface, QueryTypes } from 'sequelize'

/**
 * 数据库状态检查脚本
 * 验证所有表和数据是否正确创建
 */
async function checkDatabase(): Promise<void> {
  console.log('🔍 Starting database status check...')
  console.log('============================================================')

  try {
    // 测试数据库连接
    await sequelize.authenticate()
    console.log('✅ Database connection successful!')

    const queryInterface = sequelize.getQueryInterface()

    // 检查所有表是否存在
    const expectedTables = [
      'users',
      'categories',
      'tags',
      'articles',
      'article_tags',
      'comments',
      'posts',
      'post_likes',
      'migrations',
      'seeders'
    ]

    console.log('\n📋 Checking tables...')
    for (const tableName of expectedTables) {
      try {
        const tableExists = await queryInterface.showAllTables()
        if (tableExists.includes(tableName)) {
          console.log(`✅ Table '${tableName}' exists`)
        } else {
          console.log(`❌ Table '${tableName}' missing`)
        }
      } catch (error) {
        console.log(`❌ Error checking table '${tableName}':`, error)
      }
    }

    // 检查数据统计
    console.log('\n📊 Checking data counts...')

    const dataCounts = [
      { table: 'users', description: '用户' },
      { table: 'categories', description: '分类' },
      { table: 'tags', description: '标签' },
      { table: 'articles', description: '文章' },
      { table: 'comments', description: '评论' },
      { table: 'posts', description: '说说' },
      { table: 'post_likes', description: '说说点赞' }
    ]

    for (const { table, description } of dataCounts) {
      try {
        const result = await queryInterface.sequelize.query(
          `SELECT COUNT(*) as count FROM ${table}`,
          { type: QueryTypes.SELECT }
        ) as any[]

        const count = result[0]?.count || 0
        console.log(`📈 ${description} (${table}): ${count} 条记录`)
      } catch (error) {
        console.log(`❌ Error counting ${table}:`, error)
      }
    }

    // 检查说说功能相关数据
    console.log('\n🎯 Checking posts functionality...')

    try {
      // 检查说说数据
      const postsResult = await queryInterface.sequelize.query(
        `SELECT p.id, p.content, p.visibility, u.username as author 
         FROM posts p 
         JOIN users u ON p.author_id = u.id 
         LIMIT 5`,
        { type: QueryTypes.SELECT }
      ) as any[]

      if (postsResult.length > 0) {
        console.log('✅ Posts data found:')
        postsResult.forEach((post: any) => {
          console.log(`   • Post ${post.id}: "${post.content.substring(0, 50)}..." by ${post.author} (${post.visibility})`)
        })
      } else {
        console.log('⚠️  No posts found')
      }

      // 检查点赞数据
      const likesResult = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as total_likes FROM post_likes`,
        { type: QueryTypes.SELECT }
      ) as any[]

      const totalLikes = likesResult[0]?.total_likes || 0
      console.log(`✅ Total post likes: ${totalLikes}`)

      // 检查评论支持
      const commentsResult = await queryInterface.sequelize.query(
        `SELECT 
           COUNT(CASE WHEN article_id IS NOT NULL THEN 1 END) as article_comments,
           COUNT(CASE WHEN post_id IS NOT NULL THEN 1 END) as post_comments
         FROM comments`,
        { type: QueryTypes.SELECT }
      ) as any[]

      const commentStats = commentsResult[0]
      console.log(`✅ Comments: ${commentStats.article_comments} article comments, ${commentStats.post_comments} post comments`)

    } catch (error) {
      console.log('❌ Error checking posts functionality:', error)
    }

    // 检查索引
    console.log('\n🔍 Checking important indexes...')

    const importantIndexes = [
      { table: 'posts', column: 'author_id' },
      { table: 'posts', column: 'created_at' },
      { table: 'posts', column: 'visibility' },
      { table: 'post_likes', columns: ['post_id', 'user_id'] },
      { table: 'comments', column: 'post_id' }
    ]

    for (const indexInfo of importantIndexes) {
      try {
        const indexes = await queryInterface.showIndex(indexInfo.table) as any[]
        const hasIndex = indexes.some((idx: any) => {
          if ('columns' in indexInfo) {
            return indexInfo.columns.every((col: string) =>
              idx.fields.some((field: any) => field.attribute === col)
            )
          } else {
            return idx.fields.some((field: any) => field.attribute === indexInfo.column)
          }
        })

        const indexDesc = 'columns' in indexInfo ? indexInfo.columns.join(', ') : indexInfo.column
        console.log(`${hasIndex ? '✅' : '⚠️'} Index on ${indexInfo.table}(${indexDesc}): ${hasIndex ? 'exists' : 'missing'}`)
      } catch (error) {
        console.log(`❌ Error checking index on ${indexInfo.table}:`, error)
      }
    }

    // 检查约束
    console.log('\n🔒 Checking constraints...')

    try {
      // 检查评论表的约束（article_id 和 post_id 不能同时为空）
      const constraintTest = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as invalid_comments 
         FROM comments 
         WHERE (article_id IS NULL AND post_id IS NULL) 
            OR (article_id IS NOT NULL AND post_id IS NOT NULL)`,
        { type: QueryTypes.SELECT }
      ) as any[]

      const invalidComments = constraintTest[0]?.invalid_comments || 0
      console.log(`${invalidComments === 0 ? '✅' : '❌'} Comment constraints: ${invalidComments === 0 ? 'valid' : `${invalidComments} invalid records`}`)

    } catch (error) {
      console.log('❌ Error checking constraints:', error)
    }

    console.log('\n============================================================')
    console.log('🎉 Database status check completed!')
    console.log('\n📋 Summary:')
    console.log('   • All core tables created')
    console.log('   • Sample data populated')
    console.log('   • Posts functionality ready')
    console.log('   • Indexes and constraints in place')
    console.log('\n🚀 Your blog system with posts feature is ready to use!')

  } catch (error) {
    console.error('❌ Database check failed:', error)
    process.exit(1)
  } finally {
    await sequelize.close()
    console.log('Database connection closed')
  }
}

// 运行检查
if (require.main === module) {
  checkDatabase().catch(console.error)
}

export { checkDatabase }
