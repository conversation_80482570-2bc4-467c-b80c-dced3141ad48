"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Category = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const slug_1 = require("../utils/slug");
const Article_1 = require("./Article");
class Category extends sequelize_1.Model {
    async getFullPath() {
        const path = [this.name];
        let current = this;
        while (current && current.parentId) {
            const parent = await Category.findByPk(current.parentId);
            if (parent) {
                path.unshift(parent.name);
                current = parent;
            }
            else {
                break;
            }
        }
        return path.join('/');
    }
    async getAllChildren() {
        const directChildren = await Category.findAll({
            where: { parentId: this.id },
            order: [['sort', 'ASC'], ['name', 'ASC']]
        });
        const allChildren = [...directChildren];
        for (const child of directChildren) {
            const grandChildren = await child.getAllChildren();
            allChildren.push(...grandChildren);
        }
        return allChildren;
    }
    async isChildOf(categoryId) {
        if (this.parentId === categoryId) {
            return true;
        }
        if (this.parentId) {
            const parent = await Category.findByPk(this.parentId);
            if (parent) {
                return await parent.isChildOf(categoryId);
            }
        }
        return false;
    }
    static async findBySlug(slug) {
        return this.findOne({
            where: { slug },
            include: [
                { model: Category, as: 'parent' },
                { model: Category, as: 'children', separate: true, order: [['sort', 'ASC'], ['name', 'ASC']] }
            ]
        });
    }
    static async getTree(parentId = null) {
        const categories = await this.findAll({
            where: parentId === null
                ? { parentId: null }
                : { parentId: parentId },
            order: [['sort', 'ASC'], ['name', 'ASC']],
            include: [
                { model: Category, as: 'parent' }
            ]
        });
        for (const category of categories) {
            const children = await this.getTree(category.id);
            category.children = children;
        }
        return categories;
    }
    static async getFlatList() {
        const tree = await this.getTree();
        const flatList = [];
        const flatten = async (categories, level = 0, parentPath = '') => {
            for (const category of categories) {
                const path = parentPath ? `${parentPath}/${category.name}` : category.name;
                flatList.push({
                    ...category.toJSON(),
                    level,
                    path
                });
                if (category.children && category.children.length > 0) {
                    await flatten(category.children, level + 1, path);
                }
            }
        };
        await flatten(tree);
        return flatList;
    }
    static async getStats(includeChildren = false) {
        const categories = await this.findAll({
            order: [['sort', 'ASC'], ['name', 'ASC']]
        });
        const stats = [];
        for (const category of categories) {
            let articleCount = 0;
            if (includeChildren) {
                const allChildren = await category.getAllChildren();
                const categoryIds = [category.id, ...allChildren.map(child => child.id)];
                articleCount = await Article_1.Article.count({
                    where: { categoryId: categoryIds }
                });
            }
            else {
                articleCount = await Article_1.Article.count({
                    where: { categoryId: category.id }
                });
            }
            stats.push({
                category,
                articleCount
            });
        }
        return stats;
    }
}
exports.Category = Category;
Category.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    name: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        validate: {
            len: [1, 100],
            notEmpty: true
        }
    },
    slug: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        validate: {
            len: [1, 100],
            is: /^[a-z0-9-]+$/i
        }
    },
    description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true
    },
    parentId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        field: 'parent_id',
        references: {
            model: 'categories',
            key: 'id'
        }
    },
    sort: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'Category',
    tableName: 'categories',
    timestamps: true,
    underscored: true,
    hooks: {
        beforeValidate: async (category) => {
            if (category.name && !category.slug) {
                let baseSlug = (0, slug_1.generateSlug)(category.name);
                let slug = baseSlug;
                let counter = 1;
                while (await Category.findOne({ where: { slug } })) {
                    slug = `${baseSlug}-${counter}`;
                    counter++;
                }
                category.slug = slug;
            }
        },
        beforeSave: async (category) => {
            if (category.parentId === category.id) {
                throw new Error('Category cannot be its own parent');
            }
            if (category.parentId && category.id) {
                const parent = await Category.findByPk(category.parentId);
                if (parent && await parent.isChildOf(category.id)) {
                    throw new Error('Circular reference detected in category hierarchy');
                }
            }
        }
    }
});
//# sourceMappingURL=Category.js.map