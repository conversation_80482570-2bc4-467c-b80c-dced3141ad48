import{J as u,K as n,L as o,M as p,r as g}from"./index-1ed5a19c.js";const v={async getArticles(t={}){try{const e=await u(()=>n.get("/articles",{params:t}),"正在加载文章列表...");return{success:e.success,articles:e.data.articles,pagination:e.data.pagination}}catch(e){o(e,"获取文章列表失败")}},async getArticle(t){try{return(await u(()=>n.get(`/articles/${t}`),"正在加载文章内容...")).data.article}catch(e){o(e,"获取文章详情失败")}},async createArticle(t){try{return(await u(()=>n.post("/articles",t),"正在创建文章...")).data.article}catch(e){o(e,"创建文章失败")}},async updateArticle(t,e){try{return(await u(()=>n.put(`/articles/${t}`,e),"正在更新文章...")).data.article}catch(h){o(h,"更新文章失败")}},async deleteArticle(t){try{await u(()=>n.delete(`/articles/${t}`),"正在删除文章...")}catch(e){o(e,"删除文章失败")}},async deleteArticles(t){try{await u(()=>n.post("/articles/batch-delete",{ids:t}),"正在批量删除文章...")}catch(e){o(e,"批量删除文章失败")}},async getArticleNavigation(t){try{return(await n.get(`/articles/${t}/navigation`)).data.navigation}catch(e){o(e,"获取文章导航失败")}}},b=p("article",()=>{const t=g([]),e=g(null),h=g(!1),l=g(null),d=g({currentPage:1,totalPages:1,totalItems:0,itemsPerPage:10,hasNextPage:!1,hasPrevPage:!1}),i=g({search:"",status:"",tag:""});return{articles:t,currentArticle:e,loading:h,error:l,pagination:d,filters:i,fetchArticles:async(r=1,a=10,s)=>{l.value=null;try{const c=await v.getArticles({page:r,limit:a,tag:(s==null?void 0:s.tag)||i.value.tag||void 0,search:(s==null?void 0:s.search)||i.value.search||void 0,status:(s==null?void 0:s.status)||i.value.status||void 0});return t.value=c.articles,d.value={currentPage:c.pagination.page,totalPages:c.pagination.totalPages,totalItems:c.pagination.total,itemsPerPage:c.pagination.limit,hasNextPage:c.pagination.page<c.pagination.totalPages,hasPrevPage:c.pagination.page>1},c}catch(c){throw l.value=c.message,c}},fetchArticle:async r=>{l.value=null;try{const a=await v.getArticle(r);return e.value=a,a}catch(a){throw l.value=a.message,a}},createArticle:async r=>{try{const a=await v.createArticle(r);return t.value.unshift(a),a}catch(a){throw l.value=a.message,a}},updateArticle:async(r,a)=>{var s;try{const c=await v.updateArticle(r,a),A=t.value.findIndex(y=>y.id===r);return A!==-1&&(t.value[A]=c),((s=e.value)==null?void 0:s.id)===r&&(e.value=c),c}catch(c){throw l.value=c.message,c}},deleteArticle:async r=>{var a;try{await v.deleteArticle(r),t.value=t.value.filter(s=>s.id!==r),((a=e.value)==null?void 0:a.id)===r&&(e.value=null)}catch(s){throw l.value=s.message,s}},deleteArticles:async r=>{try{await v.deleteArticles(r),t.value=t.value.filter(a=>!r.includes(a.id)),e.value&&r.includes(e.value.id)&&(e.value=null)}catch(a){throw l.value=a.message,a}},setFilters:r=>{i.value={...i.value,...r}},clearFilters:()=>{i.value={search:"",status:"",tag:""}}}});export{v as a,b as u};
