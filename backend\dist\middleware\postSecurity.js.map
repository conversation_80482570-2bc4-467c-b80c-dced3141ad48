{"version": 3, "file": "postSecurity.js", "sourceRoot": "", "sources": ["../../src/middleware/postSecurity.ts"], "names": [], "mappings": ";;;AACA,iDAA4C;AAC5C,sCAAgC;AAiBnB,QAAA,aAAa,GAAG,CAAC,GAAG,EAAE;IAEjC,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAkB,CAAA;IAClD,MAAM,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAA;IAEnC,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC5E,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,IAAI,EAAE,CAAA;QACf,CAAC;QAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,YAAY,GAAG,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAEjD,IAAI,YAAY,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,iBAAiB,EAAE,CAAC;YAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,iBAAiB,GAAG,CAAC,GAAG,GAAG,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,CAAA;YAClF,MAAM,IAAA,0BAAW,EACf,GAAG,EACH,OAAO,aAAa,YAAY,EAChC,qBAAqB,CACtB,CAAA;QACH,CAAC;QAGD,gBAAgB,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;QAGjC,IAAI,gBAAgB,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC;YACjC,MAAM,UAAU,GAAG,GAAG,GAAG,iBAAiB,CAAA;YAC1C,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;gBACpD,IAAI,IAAI,GAAG,UAAU,EAAE,CAAC;oBACtB,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAA;IACR,CAAC,CAAA;AACH,CAAC,CAAC,EAAE,CAAA;AAMG,MAAM,mBAAmB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAC3F,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,CAAA;IAEpC,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,IAAI,EAAE,CAAA;IACf,CAAC;IAGD,MAAM,aAAa,GAAG,qDAAqD,CAAA;IAC3E,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;QAChC,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,eAAe,EAAE,oBAAoB,CAAC,CAAA;IAC/D,CAAC;IAGD,MAAM,oBAAoB,GAAG,yCAAyC,CAAA;IACtE,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;QACvC,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,gBAAgB,EAAE,oBAAoB,CAAC,CAAA;IAChE,CAAC;IAGD,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;QAC1B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,mBAAmB,EAAE,kBAAkB,CAAC,CAAA;IACjE,CAAC;IAGD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;QACpB,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,UAAU,EAAE,eAAe,CAAC,CAAA;IACrD,CAAC;IAGD,IAAI,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QACpC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAAA;QACzD,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,MAAM,EAAE,CAAC;YAC9B,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;gBACrD,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,WAAW,EAAE,mBAAmB,CAAC,CAAA;YAC1D,CAAC;YAGD,IAAI,CAAC;gBACH,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAA;YACnB,CAAC;YAAC,MAAM,CAAC;gBACP,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,WAAW,EAAE,mBAAmB,CAAC,CAAA;YAC1D,CAAC;YAGD,MAAM,gBAAgB,GAAG,oCAAoC,CAAA;YAC7D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACrC,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,+BAA+B,EAAE,0BAA0B,CAAC,CAAA;YACrF,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,EAAE,CAAA;AACR,CAAC,CAAA;AAxDY,QAAA,mBAAmB,uBAwD/B;AAMM,MAAM,mBAAmB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAC3F,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI,CAAA;IAE5B,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,IAAI,EAAE,CAAA;IACf,CAAC;IAGD,MAAM,cAAc,GAAG;QACrB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU;KAErD,CAAA;IAED,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAA;IAC1C,MAAM,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;IAE/E,IAAI,cAAc,EAAE,CAAC;QACnB,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,YAAY,EAAE,uBAAuB,CAAC,CAAA;IAC/D,CAAC;IAED,IAAI,EAAE,CAAA;AACR,CAAC,CAAA;AArBY,QAAA,mBAAmB,uBAqB/B;AAMM,MAAM,eAAe,GAAG,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IACnH,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAA;QACzB,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAA;QAE3B,IAAI,CAAC,EAAE,EAAE,CAAC;YACR,OAAO,IAAI,EAAE,CAAA;QACf,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,aAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;QAE5C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAA;QACnD,CAAC;QAGD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;QAChD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,WAAW,EAAE,eAAe,CAAC,CAAA;QACtD,CAAC;QAGA,GAAW,CAAC,IAAI,GAAG,IAAI,CAAA;QAExB,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAA;IACb,CAAC;AACH,CAAC,CAAA;AA5BY,QAAA,eAAe,mBA4B3B;AAMM,MAAM,kBAAkB,GAAG,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IACtH,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAA;QACzB,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAA;QAE3B,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,MAAM,EAAE,yBAAyB,CAAC,CAAA;QAC3D,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,aAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;QAE5C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAA;QACnD,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;YAC7B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,WAAW,EAAE,oBAAoB,CAAC,CAAA;QAC3D,CAAC;QAGA,GAAW,CAAC,IAAI,GAAG,IAAI,CAAA;QAExB,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAA;IACb,CAAC;AACH,CAAC,CAAA;AA1BY,QAAA,kBAAkB,sBA0B9B;AAQM,MAAM,YAAY,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAEpF,MAAM,WAAW,GAAG,GAAW,EAAE;QAC/B,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;QACnD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;QAEvC,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACrC,OAAO,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,SAAS,CAAA;QACxD,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;YACnD,OAAO,YAAY,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;QAED,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,MAAM,CAAA;QACf,CAAC;QAED,OAAO,GAAG,CAAC,MAAM,EAAE,aAAa,IAAI,SAAS,CAAA;IAC/C,CAAC,CAAA;IAGD,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,WAAW,EAAE,CAAA;IAEjC,IAAI,EAAE,CAAA;AACR,CAAC,CAAA;AAzBY,QAAA,YAAY,gBAyBxB;AAMY,QAAA,sBAAsB,GAAG;IACpC,2BAAmB;IACnB,2BAAmB;IACnB,oBAAY;CACb,CAAA;AAKY,QAAA,kBAAkB,GAAG;IAChC,qBAAa;IACb,GAAG,8BAAsB;CAC1B,CAAA"}