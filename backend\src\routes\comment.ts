import { Router } from 'express'
import {
  getComments,
  getComment,
  createComment,
  updateComment,
  deleteComment,
  updateCommentStatus,
  getArticleComments,
  getUserComments,
  getPendingComments,
  getCommentStats
} from '../controllers/comment'
import { authenticateToken } from '../middleware/auth'
import {
  validateComment,
  validateCommentUpdate,
  validateCommentStatus,
  validateCommentQuery
} from '../middleware/validation'
import { createCommentSecurity, commentSecurityMiddleware } from '../middleware/commentSecurity'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     Comment:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 评论ID
 *         content:
 *           type: string
 *           description: 评论内容
 *         status:
 *           type: string
 *           enum: [pending, approved, rejected]
 *           description: 评论状态
 *         articleId:
 *           type: integer
 *           description: 关联的文章ID
 *         authorId:
 *           type: integer
 *           description: 评论作者ID
 *         parentId:
 *           type: integer
 *           nullable: true
 *           description: 父评论ID（用于回复）
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *         author:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             username:
 *               type: string
 *         article:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             title:
 *               type: string
 *             slug:
 *               type: string
 *         replies:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Comment'
 */

/**
 * @swagger
 * /api/comments:
 *   get:
 *     summary: 获取评论列表
 *     tags: [Comments]
 *     parameters:
 *       - in: query
 *         name: articleId
 *         schema:
 *           type: integer
 *         description: 文章ID（可选）
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, approved, rejected, all]
 *           default: approved
 *         description: 评论状态
 *     responses:
 *       200:
 *         description: 成功获取评论列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     comments:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Comment'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 */
router.get('/', validateCommentQuery, getComments)

/**
 * @swagger
 * /api/comments/user/me:
 *   get:
 *     summary: 获取当前用户的评论列表
 *     tags: [Comments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, approved, rejected]
 *         description: 评论状态
 *     responses:
 *       200:
 *         description: 成功获取用户评论列表
 *       401:
 *         description: 未认证
 */
router.get('/user/me', authenticateToken, getUserComments)

/**
 * @swagger
 * /api/comments/pending:
 *   get:
 *     summary: 获取待审核评论列表（管理员功能）
 *     tags: [Comments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 成功获取待审核评论列表
 *       401:
 *         description: 未认证
 */
router.get('/pending', authenticateToken, getPendingComments)

/**
 * @swagger
 * /api/comments/stats:
 *   get:
 *     summary: 获取评论统计数据（管理员功能）
 *     tags: [Comments]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取评论统计数据
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     stats:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           description: 总评论数
 *                         pending:
 *                           type: integer
 *                           description: 待审核评论数
 *                         approved:
 *                           type: integer
 *                           description: 已批准评论数
 *                         rejected:
 *                           type: integer
 *                           description: 已拒绝评论数
 *       401:
 *         description: 未认证
 */
router.get('/stats', authenticateToken, getCommentStats)

/**
 * @swagger
 * /api/comments/{id}:
 *   get:
 *     summary: 获取单个评论
 *     tags: [Comments]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 评论ID
 *     responses:
 *       200:
 *         description: 成功获取评论
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     comment:
 *                       $ref: '#/components/schemas/Comment'
 *       404:
 *         description: 评论不存在
 */
router.get('/:id', getComment)

/**
 * @swagger
 * /api/comments:
 *   post:
 *     summary: 创建新评论
 *     tags: [Comments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *               - articleId
 *             properties:
 *               content:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 2000
 *                 description: 评论内容
 *               articleId:
 *                 type: integer
 *                 description: 文章ID
 *               parentId:
 *                 type: integer
 *                 description: 父评论ID（用于回复）
 *     responses:
 *       201:
 *         description: 评论创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     comment:
 *                       $ref: '#/components/schemas/Comment'
 *       401:
 *         description: 未认证
 *       404:
 *         description: 文章不存在或父评论不存在
 */
router.post('/', authenticateToken, ...createCommentSecurity, validateComment, createComment)

/**
 * @swagger
 * /api/comments/{id}:
 *   put:
 *     summary: 更新评论内容
 *     tags: [Comments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 评论ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *             properties:
 *               content:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 2000
 *                 description: 评论内容
 *     responses:
 *       200:
 *         description: 评论更新成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 无权限
 *       404:
 *         description: 评论不存在
 */
router.put('/:id', authenticateToken, ...commentSecurityMiddleware, validateCommentUpdate, updateComment)

/**
 * @swagger
 * /api/comments/{id}:
 *   delete:
 *     summary: 删除评论
 *     tags: [Comments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 评论ID
 *     responses:
 *       200:
 *         description: 评论删除成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 无权限
 *       404:
 *         description: 评论不存在
 */
router.delete('/:id', authenticateToken, deleteComment)

/**
 * @swagger
 * /api/comments/{id}/status:
 *   put:
 *     summary: 更新评论状态（管理员功能）
 *     tags: [Comments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 评论ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [pending, approved, rejected]
 *                 description: 评论状态
 *     responses:
 *       200:
 *         description: 状态更新成功
 *       401:
 *         description: 未认证
 *       404:
 *         description: 评论不存在
 */
router.put('/:id/status', authenticateToken, validateCommentStatus, updateCommentStatus)

export default router
