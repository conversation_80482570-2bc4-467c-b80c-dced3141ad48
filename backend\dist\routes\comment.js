"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const comment_1 = require("../controllers/comment");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const commentSecurity_1 = require("../middleware/commentSecurity");
const router = (0, express_1.Router)();
router.get('/', validation_1.validateCommentQuery, comment_1.getComments);
router.get('/user/me', auth_1.authenticateToken, comment_1.getUserComments);
router.get('/pending', auth_1.authenticateToken, comment_1.getPendingComments);
router.get('/stats', auth_1.authenticateToken, comment_1.getCommentStats);
router.get('/:id', comment_1.getComment);
router.post('/', auth_1.authenticateToken, ...commentSecurity_1.createCommentSecurity, validation_1.validateComment, comment_1.createComment);
router.put('/:id', auth_1.authenticateToken, ...commentSecurity_1.commentSecurityMiddleware, validation_1.validateCommentUpdate, comment_1.updateComment);
router.delete('/:id', auth_1.authenticateToken, comment_1.deleteComment);
router.put('/:id/status', auth_1.authenticateToken, validation_1.validateCommentStatus, comment_1.updateCommentStatus);
exports.default = router;
//# sourceMappingURL=comment.js.map