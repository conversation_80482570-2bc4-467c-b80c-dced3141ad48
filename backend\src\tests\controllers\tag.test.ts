import request from 'supertest'
import app from '../../server'
import { User, Article, Tag, ArticleTag } from '../../models'
import { generateToken, JWTPayload } from '../../services/auth'
import { sequelize } from '../../config/database'

describe('Tag Controller', () => {
  let authToken: string
  let user: User
  let article: Article
  let tag: Tag

  beforeAll(async () => {
    // Sync database
    await sequelize.sync({ force: true })

    // Create test user
    user = await User.create({
      username: 'testuser',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword'
    })

    authToken = generateToken({
      id: user.id,
      username: user.username,
      email: user.email
    })

    // Create test article
    article = await Article.create({
      title: 'Test Article',
      slug: 'test-article',
      content: 'Test content',
      excerpt: 'Test excerpt',
      status: 'published',
      authorId: user.id,
      publishedAt: new Date()
    })

    // Create test tag
    tag = await Tag.create({
      name: 'Test Tag',
      slug: 'test-tag'
    })
  })

  afterAll(async () => {
    await ArticleTag.destroy({ where: {} })
    await Article.destroy({ where: {} })
    await Tag.destroy({ where: {} })
    await User.destroy({ where: {} })
    await sequelize.close()
  })

  describe('GET /api/tags', () => {
    it('should get all tags with article counts', async () => {
      const response = await request(app)
        .get('/api/tags')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.tags).toBeInstanceOf(Array)
      expect(response.body.tags[0]).toHaveProperty('articleCount')
    })
  })

  describe('POST /api/tags', () => {
    it('should create a new tag', async () => {
      const tagData = {
        name: 'New Test Tag'
      }

      const response = await request(app)
        .post('/api/tags')
        .set('Authorization', `Bearer ${authToken}`)
        .send(tagData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.tag.name).toBe(tagData.name)
      expect(response.body.tag.slug).toBe('new-test-tag')
    })

    it('should return error for duplicate tag', async () => {
      const tagData = {
        name: 'Test Tag' // Already exists
      }

      const response = await request(app)
        .post('/api/tags')
        .set('Authorization', `Bearer ${authToken}`)
        .send(tagData)
        .expect(409)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('TAG_EXISTS')
    })

    it('should return error for missing name', async () => {
      const response = await request(app)
        .post('/api/tags')
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })

    it('should create a tag with custom slug', async () => {
      const tagData = {
        name: 'Custom Slug Tag',
        slug: 'my-custom-slug'
      }

      const response = await request(app)
        .post('/api/tags')
        .set('Authorization', `Bearer ${authToken}`)
        .send(tagData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.tag.name).toBe(tagData.name)
      expect(response.body.tag.slug).toBe(tagData.slug)
    })

    it('should return error for invalid slug format', async () => {
      const tagData = {
        name: 'Invalid Slug Tag',
        slug: 'Invalid Slug!'
      }

      const response = await request(app)
        .post('/api/tags')
        .set('Authorization', `Bearer ${authToken}`)
        .send(tagData)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
      expect(response.body.error.message).toContain('lowercase letters, numbers and hyphens')
    })

    it('should return error for duplicate slug', async () => {
      // First create a tag with a specific slug
      await request(app)
        .post('/api/tags')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'First Tag',
          slug: 'duplicate-slug'
        })
        .expect(201)

      // Try to create another tag with the same slug
      const response = await request(app)
        .post('/api/tags')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Second Tag',
          slug: 'duplicate-slug'
        })
        .expect(409)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('TAG_EXISTS')
    })
  })

  describe('GET /api/tags/stats', () => {
    it('should get tag statistics', async () => {
      const response = await request(app)
        .get('/api/tags/stats')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.stats).toHaveProperty('totalTags')
      expect(response.body.stats).toHaveProperty('usedTags')
      expect(response.body.stats).toHaveProperty('unusedTags')
      expect(response.body.stats).toHaveProperty('averageArticlesPerTag')
      expect(response.body.tags).toBeInstanceOf(Array)
    })
  })

  describe('GET /api/tags/popular', () => {
    beforeEach(async () => {
      // Associate tag with article for testing
      await ArticleTag.create({
        articleId: article.id,
        tagId: tag.id
      })
    })

    afterEach(async () => {
      await ArticleTag.destroy({ where: {} })
    })

    it('should get popular tags', async () => {
      const response = await request(app)
        .get('/api/tags/popular')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.tags).toBeInstanceOf(Array)
      if (response.body.tags.length > 0) {
        expect(response.body.tags[0]).toHaveProperty('articleCount')
        expect(response.body.tags[0].articleCount).toBeGreaterThan(0)
      }
    })

    it('should limit popular tags', async () => {
      const response = await request(app)
        .get('/api/tags/popular?limit=5')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.tags.length).toBeLessThanOrEqual(5)
    })
  })

  describe('POST /api/tags/article-association', () => {
    it('should add tag to article', async () => {
      const response = await request(app)
        .post('/api/tags/article-association')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          articleId: article.id,
          tagId: tag.id
        })
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Tag added to article successfully')

      // Verify association was created
      const association = await ArticleTag.findOne({
        where: { articleId: article.id, tagId: tag.id }
      })
      expect(association).toBeTruthy()
    })

    it('should return error for duplicate association', async () => {
      // Create association first
      await ArticleTag.create({
        articleId: article.id,
        tagId: tag.id
      })

      const response = await request(app)
        .post('/api/tags/article-association')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          articleId: article.id,
          tagId: tag.id
        })
        .expect(409)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('ASSOCIATION_EXISTS')
    })

    it('should return error for non-existent article', async () => {
      const response = await request(app)
        .post('/api/tags/article-association')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          articleId: 99999,
          tagId: tag.id
        })
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('ARTICLE_NOT_FOUND')
    })
  })

  describe('DELETE /api/tags/article-association/:articleId/:tagId', () => {
    beforeEach(async () => {
      await ArticleTag.create({
        articleId: article.id,
        tagId: tag.id
      })
    })

    it('should remove tag from article', async () => {
      const response = await request(app)
        .delete(`/api/tags/article-association/${article.id}/${tag.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Tag removed from article successfully')

      // Verify association was removed
      const association = await ArticleTag.findOne({
        where: { articleId: article.id, tagId: tag.id }
      })
      expect(association).toBeFalsy()
    })

    it('should return error for non-existent association', async () => {
      // Remove association first
      await ArticleTag.destroy({
        where: { articleId: article.id, tagId: tag.id }
      })

      const response = await request(app)
        .delete(`/api/tags/article-association/${article.id}/${tag.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('ASSOCIATION_NOT_FOUND')
    })
  })

  describe('GET /api/tags/article/:articleId', () => {
    beforeEach(async () => {
      await ArticleTag.create({
        articleId: article.id,
        tagId: tag.id
      })
    })

    afterEach(async () => {
      await ArticleTag.destroy({ where: {} })
    })

    it('should get tags for article', async () => {
      const response = await request(app)
        .get(`/api/tags/article/${article.id}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.tags).toBeInstanceOf(Array)
      expect(response.body.tags.length).toBe(1)
      expect(response.body.tags[0].id).toBe(tag.id)
    })

    it('should return error for non-existent article', async () => {
      const response = await request(app)
        .get('/api/tags/article/99999')
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('ARTICLE_NOT_FOUND')
    })
  })

  describe('PUT /api/tags/:id', () => {
    let tagToUpdate: Tag

    beforeEach(async () => {
      tagToUpdate = await Tag.create({
        name: 'Tag to Update',
        slug: 'tag-to-update'
      })
    })

    afterEach(async () => {
      await Tag.destroy({ where: { id: tagToUpdate.id } })
    })

    it('should update a tag with auto-generated slug', async () => {
      const updateData = {
        name: 'Updated Tag Name'
      }

      const response = await request(app)
        .put(`/api/tags/${tagToUpdate.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.tag.name).toBe(updateData.name)
      expect(response.body.tag.slug).toBe('updated-tag-name')
    })

    it('should update a tag with custom slug', async () => {
      const updateData = {
        name: 'Updated Tag Name',
        slug: 'my-custom-updated-slug'
      }

      const response = await request(app)
        .put(`/api/tags/${tagToUpdate.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.tag.name).toBe(updateData.name)
      expect(response.body.tag.slug).toBe(updateData.slug)
    })

    it('should return error for invalid slug format in update', async () => {
      const updateData = {
        name: 'Updated Tag Name',
        slug: 'Invalid Slug!'
      }

      const response = await request(app)
        .put(`/api/tags/${tagToUpdate.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
      expect(response.body.error.message).toContain('lowercase letters, numbers and hyphens')
    })

    it('should return error for duplicate name or slug in update', async () => {
      // Create another tag first
      const existingTag = await Tag.create({
        name: 'Existing Tag',
        slug: 'existing-tag'
      })

      const updateData = {
        name: 'Existing Tag' // Try to use existing name
      }

      const response = await request(app)
        .put(`/api/tags/${tagToUpdate.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(409)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('TAG_EXISTS')

      // Clean up
      await Tag.destroy({ where: { id: existingTag.id } })
    })

    it('should return error for non-existent tag in update', async () => {
      const updateData = {
        name: 'Updated Name'
      }

      const response = await request(app)
        .put('/api/tags/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('TAG_NOT_FOUND')
    })
  })

  describe('DELETE /api/tags/:id', () => {
    let tagToDelete: Tag

    beforeEach(async () => {
      tagToDelete = await Tag.create({
        name: 'Tag to Delete',
        slug: 'tag-to-delete'
      })
    })

    it('should delete a tag', async () => {
      const response = await request(app)
        .delete(`/api/tags/${tagToDelete.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Tag deleted successfully')

      // Verify tag was deleted
      const deletedTag = await Tag.findByPk(tagToDelete.id)
      expect(deletedTag).toBeFalsy()
    })

    it('should return error for non-existent tag', async () => {
      const response = await request(app)
        .delete('/api/tags/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('TAG_NOT_FOUND')
    })
  })
})