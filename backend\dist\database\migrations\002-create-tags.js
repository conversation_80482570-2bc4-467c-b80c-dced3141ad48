"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('tags', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        name: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            unique: true
        },
        slug: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            unique: true
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    try {
        await queryInterface.addIndex('tags', ['name']);
    }
    catch {
        console.log('Index tags_name already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('tags', ['slug']);
    }
    catch {
        console.log('Index tags_slug already exists, skipping...');
    }
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('tags');
};
exports.down = down;
//# sourceMappingURL=002-create-tags.js.map