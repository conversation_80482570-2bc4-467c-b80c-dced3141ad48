import { Request, Response, NextFunction } from 'express'
import { Category } from '../models/Category'
import { Article } from '../models/Article'
import { User } from '../models/User'
import { Tag } from '../models/Tag'
import { Op } from 'sequelize'


/**
 * 扩展 Express 的 Request 接口，添加用户认证信息
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
  }
}


/**
 * 获取分类列表
 * @param req - Express 请求对象，包含查询参数：tree(是否返回树结构), flat(是否返回扁平列表), stats(是否包含统计信息)
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getCategories = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { tree, flat, stats } = req.query

    let categories: any

    if (tree === 'true') {
      // 返回树结构
      categories = await Category.getTree()
    } else if (flat === 'true') {
      // 返回扁平列表，包含层级信息
      categories = await Category.getFlatList()
    } else if (stats === 'true') {
      // 返回统计信息
      categories = await Category.getStats(true)
    } else {
      // 返回普通列表
      categories = await Category.findAll({
        order: [['sort', 'ASC'], ['name', 'ASC']],
        include: [
          { model: Category, as: 'parent', attributes: ['id', 'name', 'slug'] }
        ]
      })
    }

    res.json({
      success: true,
      data: { categories }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 根据ID或slug获取单个分类
 * @param req - Express 请求对象，包含分类ID或slug参数
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getCategory = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    if (!id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ID',
          message: 'Category ID is required'
        }
      })
      return
    }

    let category: Category | null = null

    // 根据ID是数字还是字符串判断使用哪种查找方式
    if (/^\d+$/.test(id)) {
      category = await Category.findByPk(id, {
        include: [
          { model: Category, as: 'parent', attributes: ['id', 'name', 'slug'] },
          { model: Category, as: 'children', separate: true, order: [['sort', 'ASC'], ['name', 'ASC']] }
        ]
      })
    } else {
      category = await Category.findBySlug(id)
    }

    if (!category) {
      res.status(404).json({
        success: false,
        error: {
          code: 'CATEGORY_NOT_FOUND',
          message: 'Category not found'
        }
      })
      return
    }

    res.json({
      success: true,
      data: { category }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 创建新分类
 * @param req - AuthenticatedRequest 请求对象，包含分类名称、描述等信息以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const createCategory = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { name, description, parentId, sort = 0, slug } = req.body

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    if (!name || !name.trim()) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Category name is required'
        }
      })
      return
    }

    // 验证父分类是否存在
    if (parentId) {
      const parentCategory = await Category.findByPk(parentId)
      if (!parentCategory) {
        res.status(400).json({
          success: false,
          error: {
            code: 'PARENT_NOT_FOUND',
            message: 'Parent category not found'
          }
        })
        return
      }
    }

    const categoryData: any = {
      name: name.trim(),
      description: description?.trim(),
      parentId: parentId || null,
      sort
    }

    // 如果用户提供了slug，则使用用户的slug
    if (slug && slug.trim()) {
      categoryData.slug = slug.trim()
    }

    const category = await Category.create(categoryData)

    const createdCategory = await Category.findByPk(category.id, {
      include: [
        { model: Category, as: 'parent', attributes: ['id', 'name', 'slug'] },
        { model: Category, as: 'children', separate: true, order: [['sort', 'ASC'], ['name', 'ASC']] }
      ]
    })

    res.status(201).json({
      success: true,
      data: { category: createdCategory }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 更新分类
 * @param req - AuthenticatedRequest 请求对象，包含要更新的分类ID和更新数据以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const updateCategory = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params
    const { name, description, parentId, sort, slug } = req.body

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    const category = await Category.findByPk(id)

    if (!category) {
      res.status(404).json({
        success: false,
        error: {
          code: 'CATEGORY_NOT_FOUND',
          message: 'Category not found'
        }
      })
      return
    }

    // 验证父分类是否存在且不会造成循环引用
    if (parentId !== undefined) {
      if (parentId === parseInt(id || '0')) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_PARENT',
            message: 'Category cannot be its own parent'
          }
        })
        return
      }

      if (parentId) {
        const parentCategory = await Category.findByPk(parentId)
        if (!parentCategory) {
          res.status(400).json({
            success: false,
            error: {
              code: 'PARENT_NOT_FOUND',
              message: 'Parent category not found'
            }
          })
          return
        }

        // 检查是否会造成循环引用
        if (await parentCategory.isChildOf(category.id)) {
          res.status(400).json({
            success: false,
            error: {
              code: 'CIRCULAR_REFERENCE',
              message: 'Cannot set parent category that would create a circular reference'
            }
          })
          return
        }
      }
    }

    // 构建更新数据
    const updateData: any = {}
    if (name !== undefined && name.trim()) updateData.name = name.trim()
    if (description !== undefined) updateData.description = description?.trim()
    if (parentId !== undefined) updateData.parentId = parentId || null
    if (sort !== undefined) updateData.sort = sort
    if (slug !== undefined && slug.trim()) updateData.slug = slug.trim()

    await category.update(updateData)

    const updatedCategory = await Category.findByPk(category.id, {
      include: [
        { model: Category, as: 'parent', attributes: ['id', 'name', 'slug'] },
        { model: Category, as: 'children', separate: true, order: [['sort', 'ASC'], ['name', 'ASC']] }
      ]
    })

    res.json({
      success: true,
      data: { category: updatedCategory }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 获取分类下的文章列表
 * @param req - Express 请求对象，包含分类ID和查询参数
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getCategoryArticles = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params
    const {
      page = '1',
      limit = '10',
      status = 'published',
      includeChildren = 'false'
    } = req.query

    const pageNum = parseInt(page as string, 10)
    const limitNum = parseInt(limit as string, 10)
    const offset = (pageNum - 1) * limitNum

    // 验证分类是否存在
    const category = await Category.findByPk(id)
    if (!category) {
      res.status(404).json({
        success: false,
        error: {
          code: 'CATEGORY_NOT_FOUND',
          message: 'Category not found'
        }
      })
      return
    }

    // 构建查询条件
    const whereClause: any = { status }

    if (includeChildren === 'true') {
      // 包含子分类的文章
      const allChildren = await category.getAllChildren()
      const categoryIds = [category.id, ...allChildren.map(child => child.id)]
      whereClause.categoryId = categoryIds
    } else {
      // 只包含当前分类的文章
      whereClause.categoryId = category.id
    }

    const { rows: articles, count } = await Article.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username']
        },
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Tag,
          as: 'tags',
          attributes: ['id', 'name', 'slug'],
          through: { attributes: [] }
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: limitNum,
      offset,
      distinct: true
    })

    const totalPages = Math.ceil(count / limitNum)
    const hasNextPage = pageNum < totalPages
    const hasPrevPage = pageNum > 1

    res.json({
      success: true,
      data: {
        category,
        articles,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: count,
          itemsPerPage: limitNum,
          hasNextPage,
          hasPrevPage
        }
      }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 获取分类统计信息
 * @param req - Express 请求对象
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getCategoryStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { includeChildren = 'true' } = req.query

    const stats = await Category.getStats(includeChildren === 'true')

    res.json({
      success: true,
      data: { stats }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 批量删除分类
 * @param req - AuthenticatedRequest 请求对象，包含要删除的分类ID数组以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const deleteCategories = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { ids } = req.body

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    if (!Array.isArray(ids) || ids.length === 0) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'IDs array is required and cannot be empty'
        }
      })
      return
    }

    const categories = await Category.findAll({
      where: { id: ids }
    })

    if (categories.length === 0) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NO_CATEGORIES_FOUND',
          message: 'No categories found'
        }
      })
      return
    }

    // 检查是否有分类包含子分类或文章
    const validationErrors = []

    for (const category of categories) {
      const childrenCount = await Category.count({
        where: { parentId: category.id }
      })

      const articlesCount = await Article.count({
        where: { categoryId: category.id }
      })

      if (childrenCount > 0) {
        validationErrors.push(`Category "${category.name}" has child categories`)
      }

      if (articlesCount > 0) {
        validationErrors.push(`Category "${category.name}" has articles`)
      }
    }

    if (validationErrors.length > 0) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_FAILED',
          message: 'Cannot delete categories',
          details: validationErrors
        }
      })
      return
    }

    await Category.destroy({
      where: { id: categories.map(category => category.id) }
    })

    res.json({
      success: true,
      message: `${categories.length} categories deleted successfully`,
      data: {
        deletedCount: categories.length,
        deletedIds: categories.map(category => category.id)
      }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 删除分类
 * @param req - AuthenticatedRequest 请求对象，包含要删除的分类ID以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const deleteCategory = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    const category = await Category.findByPk(id)

    if (!category) {
      res.status(404).json({
        success: false,
        error: {
          code: 'CATEGORY_NOT_FOUND',
          message: 'Category not found'
        }
      })
      return
    }

    // 检查是否有子分类
    const childrenCount = await Category.count({
      where: { parentId: category.id }
    })

    if (childrenCount > 0) {
      res.status(400).json({
        success: false,
        error: {
          code: 'HAS_CHILDREN',
          message: 'Cannot delete category that has child categories'
        }
      })
      return
    }

    // 检查是否有文章使用此分类
    const articlesCount = await Article.count({
      where: { categoryId: category.id }
    })

    if (articlesCount > 0) {
      res.status(400).json({
        success: false,
        error: {
          code: 'HAS_ARTICLES',
          message: 'Cannot delete category that has articles'
        }
      })
      return
    }

    await category.destroy()

    res.json({
      success: true,
      message: 'Category deleted successfully'
    })
  } catch (error) {
    next(error)
  }
}
