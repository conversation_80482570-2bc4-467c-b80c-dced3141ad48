"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.addColumn('articles', 'category_id', {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        references: {
            model: 'categories',
            key: 'id'
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE'
    });
    try {
        await queryInterface.addIndex('articles', ['category_id']);
    }
    catch {
        console.log('Index articles_category_id already exists, skipping...');
    }
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.removeIndex('articles', ['category_id']);
    await queryInterface.removeColumn('articles', 'category_id');
};
exports.down = down;
//# sourceMappingURL=006-add-category-to-articles.js.map