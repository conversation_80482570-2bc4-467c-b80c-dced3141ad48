export interface JWTPayload {
    id: number;
    username: string;
    email: string;
}
export interface LoginCredentials {
    username: string;
    password: string;
}
export interface AuthResult {
    success: boolean;
    user?: Omit<JWTPayload, 'password'>;
    token?: string;
    message?: string;
}
export declare const generateToken: (payload: JWTPayload) => string;
export declare class AuthService {
    static generateToken(payload: JWTPayload): string;
    static verifyToken(token: string): JWTPayload;
    static login(credentials: LoginCredentials): Promise<AuthResult>;
    static validateToken(token: string): Promise<AuthResult>;
    static extractTokenFromHeader(authHeader: string | undefined): string | null;
    static refreshToken(token: string): Promise<AuthResult>;
}
//# sourceMappingURL=auth.d.ts.map