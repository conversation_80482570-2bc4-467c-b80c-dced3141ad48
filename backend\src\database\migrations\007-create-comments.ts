import { QueryInterface, DataTypes } from 'sequelize'


/**
 * 创建评论表的数据库迁移
 * 支持嵌套回复、状态管理等功能
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.createTable('comments', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '评论内容'
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected'),
      allowNull: false,
      defaultValue: 'pending',
      comment: '评论状态：pending-待审核，approved-已批准，rejected-已拒绝'
    },
    article_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '关联的文章ID',
      references: {
        model: 'articles',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    author_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '评论作者ID',
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    parent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '父评论ID，用于回复功能',
      references: {
        model: 'comments',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  })

  // 创建索引以提高查询性能
  // 使用 try-catch 来避免重复创建索引的错误
  try {
    await queryInterface.addIndex('comments', ['article_id'], {
      name: 'idx_comments_article_id'
    })
  } catch {
    console.log('Index idx_comments_article_id already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('comments', ['author_id'], {
      name: 'idx_comments_author_id'
    })
  } catch {
    console.log('Index idx_comments_author_id already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('comments', ['parent_id'], {
      name: 'idx_comments_parent_id'
    })
  } catch {
    console.log('Index idx_comments_parent_id already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('comments', ['status'], {
      name: 'idx_comments_status'
    })
  } catch {
    console.log('Index idx_comments_status already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('comments', ['created_at'], {
      name: 'idx_comments_created_at'
    })
  } catch {
    console.log('Index idx_comments_created_at already exists, skipping...')
  }

  // 创建复合索引
  try {
    await queryInterface.addIndex('comments', ['article_id', 'status'], {
      name: 'idx_comments_article_status'
    })
  } catch {
    console.log('Index idx_comments_article_status already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('comments', ['article_id', 'parent_id'], {
      name: 'idx_comments_article_parent'
    })
  } catch {
    console.log('Index idx_comments_article_parent already exists, skipping...')
  }
}


/**
 * 回滚迁移：删除评论表
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('comments')
}
