import { sequelize } from '../config/database'
import { QueryInterface } from 'sequelize'
import * as fs from 'fs'
import * as path from 'path'

interface Seeder {
  up: (queryInterface: QueryInterface) => Promise<void>
  down?: (queryInterface: QueryInterface) => Promise<void>
}

/**
 * SeedRunner 类用于执行数据库种子数据脚本
 * 它会读取 seeders 目录下的所有种子文件，并按顺序执行尚未应用的种子
 */
export class SeedRunner {
  private queryInterface: QueryInterface

  /**
   * 构造函数，初始化 QueryInterface 实例
   */
  constructor() {
    this.queryInterface = sequelize.getQueryInterface()
  }

  /**
   * 执行所有未运行的种子文件
   * 该方法会扫描 seeders 目录中的 .ts 或 .js 文件，按文件名排序后依次执行
   * 并在 seeders 表中记录已执行的种子以避免重复执行
   * @param force 是否强制重新执行所有种子
   * @returns Promise<void> - 种子执行完成的 Promise
   */
  async runSeeders(force: boolean = false): Promise<void> {
    try {
      // 创建种子记录表
      await this.createSeedersTable()

      const seedersDir = path.join(__dirname, '../database/seeders')
      
      // 检查seeders目录是否存在
      if (!fs.existsSync(seedersDir)) {
        console.log('No seeders directory found, skipping seed data...')
        return
      }

      const seederFiles = fs.readdirSync(seedersDir)
        .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
        .sort()

      if (seederFiles.length === 0) {
        console.log('No seeder files found, skipping seed data...')
        return
      }

      // 如果强制执行，清空种子记录表
      if (force) {
        await this.clearSeedersTable()
        console.log('Force mode: cleared all seeder records')
      }

      for (const file of seederFiles) {
        const seederName = path.basename(file, path.extname(file))

        // 检查种子是否已经运行过
        const hasRun = await this.hasSeederRun(seederName)
        if (hasRun && !force) {
          console.log(`Seeder ${seederName} already applied, skipping...`)
          continue
        }

        console.log(`Running seeder: ${seederName}`)

        // 动态导入种子文件
        const seederPath = path.join(seedersDir, file)
        const seeder: Seeder = require(seederPath)

        await seeder.up(this.queryInterface)
        await this.recordSeeder(seederName)

        console.log(`Seeder ${seederName} completed successfully`)
      }

      console.log('All seeders completed successfully')
    } catch (error) {
      console.error('Seeder execution failed:', error)
      throw error
    }
  }

  /**
   * 创建 seeders 表（如果尚未存在）
   * 该表用于记录已经执行过的种子名称和执行时间
   * @returns Promise<void> - 表创建完成的 Promise
   */
  private async createSeedersTable(): Promise<void> {
    await this.queryInterface.createTable('seeders', {
      name: {
        type: 'VARCHAR(255)',
        primaryKey: true,
        allowNull: false
      },
      executed_at: {
        type: 'TIMESTAMP',
        allowNull: false,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP')
      }
    }).catch(() => {
      // 表已存在，忽略错误
    })
  }

  /**
   * 清空种子记录表
   * @returns Promise<void>
   */
  private async clearSeedersTable(): Promise<void> {
    try {
      await this.queryInterface.bulkDelete('seeders', {}, {})
    } catch (error) {
      // 忽略错误
    }
  }

  /**
   * 检查指定名称的种子是否已经执行过
   * 查询 seeders 表中是否存在对应的种子记录
   * @param seederName - 要检查的种子名称
   * @returns Promise<boolean> - 如果种子已执行则返回 true，否则返回 false
   */
  private async hasSeederRun(seederName: string): Promise<boolean> {
    try {
      const result = await this.queryInterface.select(null, 'seeders', {
        where: { name: seederName }
      })
      return result.length > 0
    } catch (error) {
      return false
    }
  }

  /**
   * 记录一个种子的执行信息到 seeders 表中
   * 包括种子名称和当前时间戳
   * @param seederName - 要记录的种子名称
   * @returns Promise<void> - 插入操作完成的 Promise
   */
  private async recordSeeder(seederName: string): Promise<void> {
    await this.queryInterface.insert(null, 'seeders', {
      name: seederName,
      executed_at: new Date()
    })
  }
}
