"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeedRunner = void 0;
const database_1 = require("../config/database");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class SeedRunner {
    constructor() {
        this.queryInterface = database_1.sequelize.getQueryInterface();
    }
    async runSeeders(force = false) {
        try {
            await this.createSeedersTable();
            const seedersDir = path.join(__dirname, '../database/seeders');
            if (!fs.existsSync(seedersDir)) {
                console.log('No seeders directory found, skipping seed data...');
                return;
            }
            const seederFiles = fs.readdirSync(seedersDir)
                .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
                .sort();
            if (seederFiles.length === 0) {
                console.log('No seeder files found, skipping seed data...');
                return;
            }
            if (force) {
                await this.clearSeedersTable();
                console.log('Force mode: cleared all seeder records');
            }
            for (const file of seederFiles) {
                const seederName = path.basename(file, path.extname(file));
                const hasRun = await this.hasSeederRun(seederName);
                if (hasRun && !force) {
                    console.log(`Seeder ${seederName} already applied, skipping...`);
                    continue;
                }
                console.log(`Running seeder: ${seederName}`);
                const seederPath = path.join(seedersDir, file);
                const seeder = require(seederPath);
                await seeder.up(this.queryInterface);
                await this.recordSeeder(seederName);
                console.log(`Seeder ${seederName} completed successfully`);
            }
            console.log('All seeders completed successfully');
        }
        catch (error) {
            console.error('Seeder execution failed:', error);
            throw error;
        }
    }
    async createSeedersTable() {
        await this.queryInterface.createTable('seeders', {
            name: {
                type: 'VARCHAR(255)',
                primaryKey: true,
                allowNull: false
            },
            executed_at: {
                type: 'TIMESTAMP',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP')
            }
        }).catch(() => {
        });
    }
    async clearSeedersTable() {
        try {
            await this.queryInterface.bulkDelete('seeders', {}, {});
        }
        catch (error) {
        }
    }
    async hasSeederRun(seederName) {
        try {
            const result = await this.queryInterface.select(null, 'seeders', {
                where: { name: seederName }
            });
            return result.length > 0;
        }
        catch (error) {
            return false;
        }
    }
    async recordSeeder(seederName) {
        await this.queryInterface.insert(null, 'seeders', {
            name: seederName,
            executed_at: new Date()
        });
    }
}
exports.SeedRunner = SeedRunner;
//# sourceMappingURL=seedRunner.js.map