# 个人博客系统 - 后端

[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)
[![Express](https://img.shields.io/badge/Express-4.18+-lightgrey.svg)](https://expressjs.com/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)](https://www.mysql.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个基于 Node.js + TypeScript + Express + MySQL 构建的现代化博客系统后端API。

## ✨ 特性

- 🔐 **JWT认证系统** - 安全的用户认证和授权
- 📝 **文章管理** - 完整的CRUD操作，支持草稿和发布
- 🏷️ **标签系统** - 灵活的文章标签管理
- 🔍 **搜索功能** - 支持标题和内容全文搜索
- 📄 **分页查询** - 高效的数据分页处理
- 📚 **API文档** - 自动生成的Swagger文档
- 🧪 **完整测试** - 单元测试和集成测试覆盖
- 🛡️ **安全防护** - 输入验证、SQL注入防护、XSS防护
- 🚀 **高性能** - 数据库优化、连接池、索引策略

## 🛠️ 技术栈

- **运行时**: Node.js 18+
- **语言**: TypeScript 5.0+
- **框架**: Express.js 4.18+
- **数据库**: MySQL 8.0+ + Sequelize ORM
- **认证**: JWT + bcryptjs
- **验证**: Joi
- **测试**: Jest + Supertest
- **文档**: Swagger/OpenAPI
- **开发工具**: Nodemon, ESLint, Prettier

## 🚀 快速开始

### 环境要求

- Node.js 18.0+
- MySQL 8.0+
- npm 9.0+

### 安装和运行

```bash
# 1. 安装依赖
npm install

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息

# 3. 初始化数据库
npm run db:init

# 4. 启动开发服务器
npm run dev
```

### 验证安装

- API服务: http://localhost:3001
- API文档: http://localhost:3001/api-docs
- 健康检查: http://localhost:3001/health

## 📋 可用脚本

```bash
# 开发
npm run dev          # 启动开发服务器（热重载）
npm start           # 启动生产服务器

# 数据库
npm run db:init     # 完整数据库初始化
npm run migrate     # 运行数据库迁移
npm run db:clean    # 清理数据库

# 测试
npm test            # 运行所有测试
npm run test:watch  # 监听模式运行测试
npm run test:coverage # 生成测试覆盖率报告

# 代码质量
npm run lint        # 代码规范检查
npm run lint:fix    # 自动修复代码规范问题
npm run format      # 代码格式化

# 构建
npm run build       # 编译TypeScript
npm run clean       # 清理构建文件
```

## 📡 API概览

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/profile` - 获取用户信息
- `POST /api/auth/refresh` - 刷新令牌
- `POST /api/auth/validate` - 验证令牌

### 文章接口
- `GET /api/articles` - 获取文章列表（支持分页、搜索、过滤）
- `GET /api/articles/:id` - 获取单篇文章
- `POST /api/articles` - 创建文章 🔒
- `PUT /api/articles/:id` - 更新文章 🔒
- `DELETE /api/articles/:id` - 删除文章 🔒

### 标签接口
- `GET /api/tags` - 获取所有标签
- `GET /api/tags/:tagName/articles` - 获取标签下的文章
- `GET /api/tags/stats` - 获取标签统计信息
- `GET /api/tags/popular` - 获取热门标签
- `POST /api/tags` - 创建标签 🔒
- `DELETE /api/tags/:id` - 删除标签 🔒

🔒 表示需要认证

## 🗄️ 数据库设计

### 核心表结构

```sql
-- 用户表
users (id, username, email, password_hash, created_at, updated_at)

-- 文章表
articles (id, title, slug, content, excerpt, status, author_id, published_at, created_at, updated_at)

-- 标签表
tags (id, name, slug, created_at)

-- 文章标签关联表
article_tags (article_id, tag_id)
```

### 关系设计
- User ↔ Article: 一对多
- Article ↔ Tag: 多对多（通过article_tags）

## 🔧 配置说明

### 环境变量

```env
# 服务器配置
NODE_ENV=development
PORT=3001

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=person-blog
DB_USER=person-blog
DB_PASSWORD=123456

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# CORS配置
CORS_ORIGIN=http://localhost:3000
```

### 默认数据

系统初始化后会创建：
- **管理员账户**: admin / admin123
- **示例标签**: 25个技术相关标签
- **示例文章**: 3篇技术文章

⚠️ **生产环境请立即修改默认密码**

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
npm test

# 生成覆盖率报告
npm run test:coverage

# 监听模式
npm run test:watch
```

### 测试覆盖

- 单元测试：模型、服务、工具函数
- 集成测试：API端点、数据库操作
- 覆盖率目标：80%+

## 📚 文档

- [完整后端文档](../BACKEND_DOCUMENTATION.md) - 详细的架构和开发指南
- [API使用示例](../API_EXAMPLES.md) - 实用的API调用示例
- [数据库设计文档](../DATABASE_SETUP.md) - 数据库架构和初始化
- [在线API文档](http://localhost:3001/api-docs) - Swagger交互式文档

## 🚀 部署

### 生产环境部署

```bash
# 1. 安装生产依赖
npm ci --only=production

# 2. 编译TypeScript
npm run build

# 3. 运行数据库迁移
npm run migrate

# 4. 启动服务
npm start
```

### Docker部署

```bash
# 构建镜像
docker build -t personal-blog-api .

# 运行容器
docker run -p 3001:3001 personal-blog-api
```

### 使用docker-compose

```bash
docker-compose up -d
```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查MySQL服务
   systemctl status mysql
   
   # 测试连接
   mysql -h localhost -u person-blog -p
   ```

2. **端口被占用**
   ```bash
   # 查看端口使用情况
   netstat -tlnp | grep 3001
   
   # 修改端口
   export PORT=3002
   ```

3. **JWT错误**
   - 检查JWT_SECRET环境变量
   - 确认令牌格式正确
   - 验证令牌是否过期

### 调试模式

```bash
# 启用详细日志
DEBUG=* npm run dev

# 使用Node.js调试器
node --inspect src/server.ts
```

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 开发规范

- 遵循 TypeScript 和 ESLint 规范
- 编写测试用例
- 更新相关文档
- 通过所有测试

## 📄 许可证

本项目采用 [MIT](LICENSE) 许可证。

## 🙏 致谢

感谢以下开源项目：

- [Express.js](https://expressjs.com/) - Web框架
- [Sequelize](https://sequelize.org/) - ORM框架
- [Jest](https://jestjs.io/) - 测试框架
- [Swagger](https://swagger.io/) - API文档

## 📞 支持

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 文档: [项目文档](../BACKEND_DOCUMENTATION.md)

---

**开发团队** | **最后更新**: 2024年7月26日
