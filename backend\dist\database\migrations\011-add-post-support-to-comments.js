"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.addColumn('comments', 'post_id', {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        references: {
            model: 'posts',
            key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
    });
    await queryInterface.changeColumn('comments', 'article_id', {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        references: {
            model: 'articles',
            key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
    });
    try {
        await queryInterface.addIndex('comments', ['post_id']);
    }
    catch {
        console.log('Index comments_post_id already exists, skipping...');
    }
    try {
        await queryInterface.sequelize.query(`
      ALTER TABLE comments 
      ADD CONSTRAINT comments_target_check 
      CHECK (
        (article_id IS NOT NULL AND post_id IS NULL) OR 
        (article_id IS NULL AND post_id IS NOT NULL)
      )
    `);
    }
    catch (error) {
        console.log('Check constraint comments_target_check already exists or not supported, skipping...', error);
    }
};
exports.up = up;
const down = async (queryInterface) => {
    try {
        await queryInterface.sequelize.query(`
      ALTER TABLE comments 
      DROP CONSTRAINT comments_target_check
    `);
    }
    catch {
        console.log('Check constraint comments_target_check does not exist or not supported, skipping...');
    }
    await queryInterface.removeColumn('comments', 'post_id');
    await queryInterface.changeColumn('comments', 'article_id', {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: 'articles',
            key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
    });
};
exports.down = down;
//# sourceMappingURL=011-add-post-support-to-comments.js.map