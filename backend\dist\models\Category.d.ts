import { Model, Optional, Association } from 'sequelize';
import { Article } from './Article';
export interface CategoryAttributes {
    id: number;
    name: string;
    slug: string;
    description?: string;
    parentId?: number;
    sort: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface CategoryCreationAttributes extends Optional<CategoryAttributes, 'id' | 'slug' | 'description' | 'parentId' | 'sort' | 'createdAt' | 'updatedAt'> {
}
export declare class Category extends Model<CategoryAttributes, CategoryCreationAttributes> implements CategoryAttributes {
    id: number;
    name: string;
    slug: string;
    description?: string;
    parentId?: number;
    sort: number;
    createdAt: Date;
    updatedAt: Date;
    readonly articles?: Article[];
    readonly parent?: Category;
    readonly children?: Category[];
    static associations: {
        articles: Association<Category, Article>;
        parent: Association<Category, Category>;
        children: Association<Category, Category>;
    };
    getFullPath(): Promise<string>;
    getAllChildren(): Promise<Category[]>;
    isChildOf(categoryId: number): Promise<boolean>;
    static findBySlug(slug: string): Promise<Category | null>;
    static getTree(parentId?: number | null): Promise<Category[]>;
    static getFlatList(): Promise<Array<Category & {
        level: number;
        path: string;
    }>>;
    static getStats(includeChildren?: boolean): Promise<{
        category: Category;
        articleCount: number;
    }[]>;
}
//# sourceMappingURL=Category.d.ts.map