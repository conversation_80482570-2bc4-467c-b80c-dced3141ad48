#!/usr/bin/env ts-node

import { MigrationRunner } from '../utils/migrationRunner'
import { SeedRunner } from '../utils/seedRunner'
import { dbConnection } from '../database/connection'
import dotenv from 'dotenv'

// 加载环境变量
dotenv.config()

/**
 * 数据库初始化脚本
 * 完整的数据库设置流程：创建数据库 -> 运行迁移 -> 运行种子数据
 */
class InitDbScript {
  private migrationRunner: MigrationRunner
  private seedRunner: SeedRunner

  constructor() {
    this.migrationRunner = new MigrationRunner()
    this.seedRunner = new SeedRunner()
  }

  /**
   * 执行完整的数据库初始化
   * @param options 初始化选项
   */
  async run(options: {
    force?: boolean
    seedOnly?: boolean
    migrateOnly?: boolean
  } = {}): Promise<void> {
    const { force = false, seedOnly = false, migrateOnly = false } = options

    console.log('🚀 Starting database initialization...')
    console.log('=' .repeat(60))

    try {
      // 显示数据库连接信息
      const connectionInfo = dbConnection.getConnectionInfo()
      console.log(`📊 Database: ${connectionInfo.database}`)
      console.log(`🏠 Host: ${connectionInfo.host}:${connectionInfo.port}`)
      console.log(`👤 User: ${connectionInfo.username}`)
      console.log(`🔧 Mode: ${force ? 'FORCE' : 'NORMAL'}`)
      console.log('')

      // 步骤1: 创建数据库（如果不存在）
      if (!seedOnly) {
        console.log('📦 Step 1: Creating database if not exists...')
        const dbCreated = await dbConnection.createDatabaseIfNotExists()
        
        if (!dbCreated) {
          console.error('❌ Failed to create database!')
          process.exit(1)
        }
        console.log('✅ Database ready!')
        console.log('')
      }

      // 步骤2: 测试数据库连接
      console.log('🔍 Step 2: Testing database connection...')
      const isConnected = await dbConnection.testConnection()
      
      if (!isConnected) {
        console.error('❌ Database connection failed!')
        process.exit(1)
      }
      console.log('✅ Database connection successful!')
      console.log('')

      // 步骤3: 运行迁移
      if (!seedOnly) {
        console.log('🏗️  Step 3: Running database migrations...')
        await this.migrationRunner.runMigrations()
        console.log('✅ Migrations completed!')
        console.log('')
      }

      // 步骤4: 运行种子数据
      if (!migrateOnly) {
        console.log('🌱 Step 4: Running seed data...')
        await this.seedRunner.runSeeders(force)
        console.log('✅ Seed data completed!')
        console.log('')
      }

      // 完成
      console.log('=' .repeat(60))
      console.log('🎉 Database initialization completed successfully!')
      console.log('')
      console.log('📋 Summary:')
      console.log(`   • Database: ${connectionInfo.database}`)
      console.log(`   • Migrations: ${seedOnly ? 'Skipped' : 'Completed'}`)
      console.log(`   • Seed data: ${migrateOnly ? 'Skipped' : 'Completed'}`)
      console.log('')
      console.log('🚀 Your blog system is ready to use!')
      
    } catch (error) {
      console.error('')
      console.error('=' .repeat(60))
      console.error('❌ Database initialization failed!')
      console.error('Error details:', error)
      process.exit(1)
    } finally {
      // 关闭数据库连接
      await dbConnection.closeConnection()
    }
  }

  /**
   * 显示帮助信息
   */
  showHelp(): void {
    console.log(`
📚 Database Initialization Tool

Usage:
  npm run db:init [options]         Initialize database with default settings
  ts-node src/scripts/initDb.ts [options]    Run initialization directly

Options:
  --force          Force re-run all operations (recreate seed data)
  --seed-only      Only run seed data (skip migrations)
  --migrate-only   Only run migrations (skip seed data)
  --help, -h       Show this help message

Environment Variables:
  DB_HOST      Database host (default: localhost)
  DB_PORT      Database port (default: 3306)
  DB_NAME      Database name (default: person-blog)
  DB_USER      Database user (default: person-blog)
  DB_PASSWORD  Database password

Examples:
  npm run db:init                    # Full initialization
  npm run db:init -- --force         # Force re-initialization
  npm run db:init -- --seed-only     # Only run seed data
  npm run db:init -- --migrate-only  # Only run migrations
`)
  }
}

/**
 * 解析命令行参数
 */
function parseArgs(args: string[]) {
  return {
    force: args.includes('--force'),
    seedOnly: args.includes('--seed-only'),
    migrateOnly: args.includes('--migrate-only'),
    help: args.includes('--help') || args.includes('-h')
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2)
  const options = parseArgs(args)
  
  const script = new InitDbScript()
  
  // 检查是否请求帮助
  if (options.help) {
    script.showHelp()
    return
  }

  // 验证参数组合
  if (options.seedOnly && options.migrateOnly) {
    console.error('❌ Error: --seed-only and --migrate-only cannot be used together')
    process.exit(1)
  }

  // 执行初始化
  await script.run({
    force: options.force,
    seedOnly: options.seedOnly,
    migrateOnly: options.migrateOnly
  })
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
  process.exit(1)
})

// 执行主函数
if (require.main === module) {
  main().catch((error) => {
    console.error('Script execution failed:', error)
    process.exit(1)
  })
}
