import { DataTypes, Model, Optional } from 'sequelize'
import { sequelize } from '../config/database'
import { generateSlug } from '../utils/slug'


/**
 * 标签模型的属性接口定义
 */
export interface TagAttributes {
  id: number
  name: string
  slug: string
  createdAt: Date
}


/**
 * 标签创建时的属性接口定义，部分属性为可选
 */
export interface TagCreationAttributes extends Optional<TagAttributes, 'id' | 'slug' | 'createdAt'> { }


/**
 * 标签模型类，继承自Sequelize的Model基类
 * 实现了TagAttributes接口，定义了标签的基本属性和数据库操作方法
 */
export class Tag extends Model<TagAttributes, TagCreationAttributes> implements TagAttributes {
  public id!: number
  public name!: string
  public slug!: string
  public createdAt!: Date


  /**
   * 根据slug查找标签
   * @param slug 标签的唯一标识符
   * @returns 返回匹配的标签实例或null
   */
  public static async findBySlug(slug: string): Promise<Tag | null> {
    return (this as any).findOne({ where: { slug } })
  }

  /**
   * 根据名称查找标签
   * @param name 标签名称
   * @returns 返回匹配的标签实例或null
   */
  public static async findByName(name: string): Promise<Tag | null> {
    return (this as any).findOne({ where: { name } })
  }

  /**
   * 根据名称查找或创建标签
   * 如果标签不存在则创建新标签，存在则返回已有标签
   * @param name 标签名称
   * @returns 返回标签实例和是否为新创建的布尔值组成的元组
   */
  public static async findOrCreateByName(name: string): Promise<[Tag, boolean]> {
    const slug = generateSlug(name)
    return (this as any).findOrCreate({
      where: { name },
      defaults: { name, slug }
    })
  }
}


/**
 * 初始化标签模型的数据库映射配置
 * 定义了各字段的类型、验证规则和模型选项
 */
Tag.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        len: [1, 50],
        notEmpty: true
      }
    },
    slug: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        len: [1, 50],
        is: /^[a-z0-9-]+$/i
      }
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  },
  {
    sequelize,
    modelName: 'Tag',
    tableName: 'tags',
    timestamps: true,
    updatedAt: false,
    underscored: true,
    /**
     * 模型钩子函数配置
     * 在验证前自动根据标签名称生成slug
     */
    hooks: {
      beforeValidate: (tag: Tag) => {
        if (tag.name && !tag.slug) {
          tag.slug = generateSlug(tag.name)
        }
      }
    }
  }
)