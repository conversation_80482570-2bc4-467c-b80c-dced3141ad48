"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('posts', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        content: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: false
        },
        images: {
            type: sequelize_1.DataTypes.JSON,
            allowNull: true
        },
        visibility: {
            type: sequelize_1.DataTypes.ENUM('public', 'private'),
            allowNull: false,
            defaultValue: 'public'
        },
        location: {
            type: sequelize_1.DataTypes.STRING(200),
            allowNull: true
        },
        author_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    try {
        await queryInterface.addIndex('posts', ['author_id']);
    }
    catch {
        console.log('Index posts_author_id already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('posts', ['created_at']);
    }
    catch {
        console.log('Index posts_created_at already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('posts', ['visibility']);
    }
    catch {
        console.log('Index posts_visibility already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('posts', ['author_id', 'created_at']);
    }
    catch {
        console.log('Index posts_author_id_created_at already exists, skipping...');
    }
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('posts');
};
exports.down = down;
//# sourceMappingURL=008-create-posts.js.map