"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const article_1 = require("../controllers/article");
const comment_1 = require("../controllers/comment");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const router = (0, express_1.Router)();
router.get('/', article_1.getArticles);
router.get('/:id', article_1.getArticle);
router.post('/', auth_1.authenticateToken, validation_1.validateArticle, article_1.createArticle);
router.post('/batch-delete', auth_1.authenticateToken, article_1.deleteArticles);
router.put('/:id', auth_1.authenticateToken, validation_1.validateArticleUpdate, article_1.updateArticle);
router.delete('/:id', auth_1.authenticateToken, article_1.deleteArticle);
router.post('/:id/publish', auth_1.authenticateToken, article_1.publishArticle);
router.post('/:id/unpublish', auth_1.authenticateToken, article_1.unpublishArticle);
router.get('/:id/navigation', article_1.getArticleNavigation);
router.get('/:articleId/comments', comment_1.getArticleComments);
exports.default = router;
//# sourceMappingURL=article.js.map