import { Comment } from '../../models/Comment'
import { User } from '../../models/User'
import { Article } from '../../models/Article'
import { sequelize } from '../../config/database'

describe('Comment Model', () => {
  let testUser: User
  let testArticle: Article

  beforeAll(async () => {
    // 同步数据库
    await sequelize.sync({ force: true })

    // 创建测试用户
    testUser = await User.create({
      username: 'testuser',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword'
    })

    // 创建测试文章
    testArticle = await Article.create({
      title: 'Test Article',
      content: 'Test content',
      status: 'published',
      authorId: testUser.id
    })
  })

  afterAll(async () => {
    await sequelize.close()
  })

  afterEach(async () => {
    // 清理评论数据
    await Comment.destroy({ where: {}, force: true })
  })

  describe('Comment Creation', () => {
    it('should create a comment with valid data', async () => {
      const commentData = {
        content: 'This is a test comment',
        articleId: testArticle.id,
        authorId: testUser.id
      }

      const comment = await Comment.create(commentData)

      expect(comment).toBeDefined()
      expect(comment.content).toBe(commentData.content)
      expect(comment.articleId).toBe(commentData.articleId)
      expect(comment.authorId).toBe(commentData.authorId)
      expect(comment.status).toBe('pending') // 默认状态
      expect(comment.parentId).toBeNull()
    })

    it('should create a reply comment', async () => {
      // 先创建父评论
      const parentComment = await Comment.create({
        content: 'Parent comment',
        articleId: testArticle.id,
        authorId: testUser.id
      })

      // 创建回复评论
      const replyComment = await Comment.create({
        content: 'Reply comment',
        articleId: testArticle.id,
        authorId: testUser.id,
        parentId: parentComment.id
      })

      expect(replyComment.parentId).toBe(parentComment.id)
      expect(replyComment.isTopLevel()).toBe(false)
    })

    it('should fail to create comment without required fields', async () => {
      await expect(Comment.create({
        // 缺少 content
        articleId: testArticle.id,
        authorId: testUser.id
      } as any)).rejects.toThrow()

      await expect(Comment.create({
        content: 'Test content',
        // 缺少 articleId
        authorId: testUser.id
      } as any)).rejects.toThrow()

      await expect(Comment.create({
        content: 'Test content',
        articleId: testArticle.id
        // 缺少 authorId
      } as any)).rejects.toThrow()
    })

    it('should validate content length', async () => {
      // 测试空内容
      await expect(Comment.create({
        content: '',
        articleId: testArticle.id,
        authorId: testUser.id
      })).rejects.toThrow()

      // 测试过长内容
      const longContent = 'a'.repeat(2001)
      await expect(Comment.create({
        content: longContent,
        articleId: testArticle.id,
        authorId: testUser.id
      })).rejects.toThrow()
    })
  })

  describe('Comment Methods', () => {
    let testComment: Comment

    beforeEach(async () => {
      testComment = await Comment.create({
        content: 'Test comment',
        articleId: testArticle.id,
        authorId: testUser.id
      })
    })

    it('should approve comment', async () => {
      await testComment.approve()
      expect(testComment.status).toBe('approved')
    })

    it('should reject comment', async () => {
      await testComment.reject()
      expect(testComment.status).toBe('rejected')
    })

    it('should check if comment is top level', async () => {
      expect(testComment.isTopLevel()).toBe(true)

      const replyComment = await Comment.create({
        content: 'Reply',
        articleId: testArticle.id,
        authorId: testUser.id,
        parentId: testComment.id
      })

      expect(replyComment.isTopLevel()).toBe(false)
    })

    it('should get plain content', async () => {
      const htmlComment = await Comment.create({
        content: '<p>This is <strong>HTML</strong> content</p>',
        articleId: testArticle.id,
        authorId: testUser.id
      })

      expect(htmlComment.getPlainContent()).toBe('This is HTML content')
    })
  })

  describe('Comment Static Methods', () => {
    beforeEach(async () => {
      // 创建测试评论
      await Comment.create({
        content: 'Approved comment',
        articleId: testArticle.id,
        authorId: testUser.id,
        status: 'approved'
      })

      await Comment.create({
        content: 'Pending comment',
        articleId: testArticle.id,
        authorId: testUser.id,
        status: 'pending'
      })

      await Comment.create({
        content: 'Rejected comment',
        articleId: testArticle.id,
        authorId: testUser.id,
        status: 'rejected'
      })
    })

    it('should find comments by article ID', async () => {
      const result = await Comment.findByArticleId(testArticle.id)

      expect(result.rows).toHaveLength(1) // 只返回已批准的评论
      expect(result.rows[0]?.status).toBe('approved')
    })

    it('should find comments by author ID', async () => {
      const result = await Comment.findByAuthorId(testUser.id)

      expect(result.rows).toHaveLength(3) // 返回用户的所有评论
    })

    it('should find pending comments', async () => {
      const result = await Comment.findPending()

      expect(result.rows).toHaveLength(1)
      expect(result.rows[0]?.status).toBe('pending')
    })

    it('should support pagination', async () => {
      // 创建更多评论
      for (let i = 0; i < 15; i++) {
        await Comment.create({
          content: `Comment ${i}`,
          articleId: testArticle.id,
          authorId: testUser.id,
          status: 'approved'
        })
      }

      const result = await Comment.findByArticleId(testArticle.id, {
        limit: 10,
        offset: 0
      })

      expect(result.rows).toHaveLength(10)
      expect(result.count).toBeGreaterThan(10)
    })
  })

  describe('Comment Hooks', () => {
    it('should remove script tags on create', async () => {
      const maliciousContent = 'Hello <script>alert("xss")</script> world'

      const comment = await Comment.create({
        content: maliciousContent,
        articleId: testArticle.id,
        authorId: testUser.id
      })

      expect(comment.content).toBe('Hello  world')
      expect(comment.content).not.toContain('<script>')
    })

    it('should remove script tags on update', async () => {
      const comment = await Comment.create({
        content: 'Original content',
        articleId: testArticle.id,
        authorId: testUser.id
      })

      const maliciousContent = 'Updated <script>alert("xss")</script> content'
      await comment.update({ content: maliciousContent })

      expect(comment.content).toBe('Updated  content')
      expect(comment.content).not.toContain('<script>')
    })
  })
})
