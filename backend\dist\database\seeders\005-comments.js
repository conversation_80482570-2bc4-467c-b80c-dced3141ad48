"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const up = async (queryInterface) => {
    console.log('Creating sample comments...');
    const users = await queryInterface.select(null, 'users', {});
    if (users.length === 0) {
        console.log('No users found, skipping comment creation...');
        return;
    }
    const articles = await queryInterface.select(null, 'articles', {
        where: { status: 'published' }
    });
    if (articles.length === 0) {
        console.log('No published articles found, skipping comment creation...');
        return;
    }
    const existingComments = await queryInterface.select(null, 'comments', {});
    if (existingComments.length > 0) {
        console.log('Comments already exist, skipping...');
        return;
    }
    const adminUser = users.find((user) => user.username === 'admin');
    const johnUser = users.find((user) => user.username === 'john_doe');
    const janeUser = users.find((user) => user.username === 'jane_smith');
    const techUser = users.find((user) => user.username === 'tech_writer');
    const welcomeArticle = articles.find((article) => article.title.includes('欢迎'));
    const typescriptArticle = articles.find((article) => article.title.includes('TypeScript'));
    const nodejsArticle = articles.find((article) => article.title.includes('Node.js'));
    const comments = [];
    if (welcomeArticle) {
        comments.push({
            content: '欢迎开通博客！期待看到更多精彩的技术分享。',
            status: 'approved',
            article_id: welcomeArticle.id,
            author_id: johnUser?.id || adminUser.id,
            parent_id: null,
            created_at: new Date('2024-01-10 11:30:00'),
            updated_at: new Date('2024-01-10 11:30:00')
        }, {
            content: '博客界面很简洁，技术栈也很现代化，赞！',
            status: 'approved',
            article_id: welcomeArticle.id,
            author_id: janeUser?.id || adminUser.id,
            parent_id: null,
            created_at: new Date('2024-01-10 14:20:00'),
            updated_at: new Date('2024-01-10 14:20:00')
        }, {
            content: '谢谢大家的支持！会持续更新优质内容的。',
            status: 'approved',
            article_id: welcomeArticle.id,
            author_id: adminUser.id,
            parent_id: null,
            created_at: new Date('2024-01-10 16:45:00'),
            updated_at: new Date('2024-01-10 16:45:00')
        });
    }
    if (typescriptArticle) {
        comments.push({
            content: '这篇 TypeScript 最佳实践写得很详细，特别是泛型部分的例子很实用。',
            status: 'approved',
            article_id: typescriptArticle.id,
            author_id: techUser?.id || adminUser.id,
            parent_id: null,
            created_at: new Date('2024-01-12 16:00:00'),
            updated_at: new Date('2024-01-12 16:00:00')
        }, {
            content: '严格模式确实很重要，能避免很多潜在的 bug。',
            status: 'approved',
            article_id: typescriptArticle.id,
            author_id: johnUser?.id || adminUser.id,
            parent_id: null,
            created_at: new Date('2024-01-12 18:30:00'),
            updated_at: new Date('2024-01-12 18:30:00')
        }, {
            content: '能否分享一下在大型项目中使用 TypeScript 的经验？',
            status: 'approved',
            article_id: typescriptArticle.id,
            author_id: janeUser?.id || adminUser.id,
            parent_id: null,
            created_at: new Date('2024-01-13 09:15:00'),
            updated_at: new Date('2024-01-13 09:15:00')
        }, {
            content: '好建议！我会考虑写一篇关于大型 TypeScript 项目实践的文章。',
            status: 'approved',
            article_id: typescriptArticle.id,
            author_id: adminUser.id,
            parent_id: null,
            created_at: new Date('2024-01-13 10:30:00'),
            updated_at: new Date('2024-01-13 10:30:00')
        });
    }
    if (nodejsArticle) {
        comments.push({
            content: 'Node.js 入门指南写得很清楚，对新手很友好。',
            status: 'approved',
            article_id: nodejsArticle.id,
            author_id: janeUser?.id || adminUser.id,
            parent_id: null,
            created_at: new Date('2024-01-15 17:30:00'),
            updated_at: new Date('2024-01-15 17:30:00')
        }, {
            content: 'Express 框架确实很简单易用，是 Node.js 开发的好选择。',
            status: 'approved',
            article_id: nodejsArticle.id,
            author_id: techUser?.id || adminUser.id,
            parent_id: null,
            created_at: new Date('2024-01-15 19:45:00'),
            updated_at: new Date('2024-01-15 19:45:00')
        }, {
            content: '这是垃圾内容，请删除。',
            status: 'rejected',
            article_id: nodejsArticle.id,
            author_id: johnUser?.id || adminUser.id,
            parent_id: null,
            created_at: new Date('2024-01-15 20:00:00'),
            updated_at: new Date('2024-01-15 20:00:00')
        }, {
            content: '期待后续关于 Node.js 性能优化的文章。',
            status: 'pending',
            article_id: nodejsArticle.id,
            author_id: johnUser?.id || adminUser.id,
            parent_id: null,
            created_at: new Date('2024-01-16 08:30:00'),
            updated_at: new Date('2024-01-16 08:30:00')
        });
    }
    if (comments.length === 0) {
        console.log('No comments to create, skipping...');
        return;
    }
    await queryInterface.bulkInsert('comments', comments);
    console.log(`Created ${comments.length} sample comments`);
    const insertedComments = await queryInterface.select(null, 'comments', {});
    if (insertedComments.length > 0) {
        const replies = [];
        const firstComment = insertedComments[0];
        if (firstComment) {
            replies.push({
                content: '感谢支持！',
                status: 'approved',
                article_id: firstComment.article_id,
                author_id: adminUser.id,
                parent_id: firstComment.id,
                created_at: new Date('2024-01-10 12:00:00'),
                updated_at: new Date('2024-01-10 12:00:00')
            });
        }
        const tsComments = insertedComments.filter((comment) => comment.article_id === typescriptArticle?.id);
        if (tsComments.length > 2) {
            const questionComment = tsComments[2];
            replies.push({
                content: '好建议！我会考虑写一篇关于大型 TypeScript 项目实践的文章。',
                status: 'approved',
                article_id: questionComment.article_id,
                author_id: adminUser.id,
                parent_id: questionComment.id,
                created_at: new Date('2024-01-13 10:30:00'),
                updated_at: new Date('2024-01-13 10:30:00')
            });
        }
        if (replies.length > 0) {
            await queryInterface.bulkInsert('comments', replies);
            console.log(`Created ${replies.length} reply comments`);
        }
    }
    console.log('Sample comments and replies created successfully!');
};
exports.up = up;
const down = async (queryInterface) => {
    console.log('Removing sample comments...');
    await queryInterface.bulkDelete('comments', {}, {});
    console.log('Sample comments removed successfully!');
};
exports.down = down;
//# sourceMappingURL=005-comments.js.map