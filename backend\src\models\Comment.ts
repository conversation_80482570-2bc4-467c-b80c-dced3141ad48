import { DataTypes, Model, Optional, Association } from 'sequelize'
import { sequelize } from '../config/database'
import { User } from './User'
import { Article } from './Article'


/**
 * 评论模型的属性接口定义
 */
export interface CommentAttributes {
  id: number
  content: string
  status: 'pending' | 'approved' | 'rejected'
  articleId?: number  // 文章评论时必填
  postId?: number     // 说说评论时必填
  authorId: number
  parentId?: number
  createdAt: Date
  updatedAt: Date
}


/**
 * 评论创建时的属性接口定义，部分字段为可选
 */
export interface CommentCreationAttributes extends Optional<CommentAttributes, 'id' | 'status' | 'articleId' | 'postId' | 'parentId' | 'createdAt' | 'updatedAt'> { }


/**
 * 评论模型类，继承自Sequelize的Model基类
 * 实现了评论的基本操作和关联关系
 */
export class Comment extends Model<CommentAttributes, CommentCreationAttributes> implements CommentAttributes {
  public id!: number
  public content!: string
  public status!: 'pending' | 'approved' | 'rejected'
  public articleId?: number
  public postId?: number
  public authorId!: number
  public parentId?: number
  public createdAt!: Date
  public updatedAt!: Date

  // 关联关系
  public readonly author?: User
  public readonly article?: any  // Article类型，避免循环引用
  public readonly post?: any     // Post类型，避免循环引用
  public readonly parent?: Comment
  public readonly replies?: Comment[]

  public static associations: {
    author: Association<Comment, User>
    article: Association<Comment, any>
    post: Association<Comment, any>
    parent: Association<Comment, Comment>
    replies: Association<Comment, Comment>
  }


  /**
   * 批准评论
   * @returns Promise<void>
   */
  public async approve(): Promise<void> {
    this.status = 'approved'
    await this.save()
  }

  /**
   * 拒绝评论
   * @returns Promise<void>
   */
  public async reject(): Promise<void> {
    this.status = 'rejected'
    await this.save()
  }

  /**
   * 检查是否为顶级评论
   * @returns boolean
   */
  public isTopLevel(): boolean {
    return this.parentId === null || this.parentId === undefined
  }

  /**
   * 获取评论的纯文本内容（去除HTML标签）
   * @returns string
   */
  public getPlainContent(): string {
    return this.content.replace(/<[^>]*>/g, '')
  }

  /**
   * 检查评论是否属于文章
   * @returns boolean
   */
  public isArticleComment(): boolean {
    return !!this.articleId
  }

  /**
   * 检查评论是否属于说说
   * @returns boolean
   */
  public isPostComment(): boolean {
    return !!this.postId
  }

  /**
   * 根据说说ID获取已批准的评论列表，支持分页
   * @param postId 说说ID
   * @param options 查询选项
   * @returns Promise<{ rows: Comment[], count: number }>
   */
  public static async findByPostId(
    postId: number,
    options: {
      limit?: number
      offset?: number
      includeReplies?: boolean
    } = {}
  ): Promise<{ rows: Comment[], count: number }> {
    const { limit = 10, offset = 0, includeReplies = true } = options

    const includeClause: any[] = [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'username']
      }
    ]

    // 如果需要包含回复，添加自关联
    if (includeReplies) {
      includeClause.push({
        model: Comment,
        as: 'replies',
        where: { status: 'approved' },
        required: false,
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['id', 'username']
          }
        ]
      })
    }

    return this.findAndCountAll({
      where: {
        postId,
        status: 'approved',
        parentId: null // 只获取顶级评论
      } as any,
      include: includeClause,
      order: [['createdAt', 'DESC']],
      limit,
      offset,
      distinct: true
    })
  }

  /**
   * 根据文章ID获取已批准的评论列表，支持分页
   * @param articleId 文章ID
   * @param options 查询选项
   * @returns Promise<{ rows: Comment[], count: number }>
   */
  public static async findByArticleId(
    articleId: number,
    options: {
      limit?: number
      offset?: number
      includeReplies?: boolean
    } = {}
  ): Promise<{ rows: Comment[], count: number }> {
    const { limit = 10, offset = 0, includeReplies = true } = options

    const includeClause: any[] = [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'username']
      }
    ]

    // 如果需要包含回复，添加自关联
    if (includeReplies) {
      includeClause.push({
        model: Comment,
        as: 'replies',
        where: { status: 'approved' },
        required: false,
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['id', 'username']
          }
        ]
      })
    }

    return this.findAndCountAll({
      where: {
        articleId,
        status: 'approved',
        parentId: null // 只获取顶级评论
      } as any,
      include: includeClause,
      order: [['createdAt', 'DESC']],
      limit,
      offset,
      distinct: true
    })
  }

  /**
   * 获取用户的评论列表
   * @param authorId 用户ID
   * @param options 查询选项
   * @returns Promise<{ rows: Comment[], count: number }>
   */
  public static async findByAuthorId(
    authorId: number,
    options: {
      limit?: number
      offset?: number
      status?: 'pending' | 'approved' | 'rejected'
    } = {}
  ): Promise<{ rows: Comment[], count: number }> {
    const { limit = 10, offset = 0, status } = options

    const whereClause: any = { authorId }
    if (status) {
      whereClause.status = status
    }

    return this.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'slug']
        },
        {
          model: Comment,
          as: 'parent',
          attributes: ['id', 'content'],
          include: [
            {
              model: User,
              as: 'author',
              attributes: ['id', 'username']
            }
          ]
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset,
      distinct: true
    })
  }

  /**
   * 获取待审核的评论列表
   * @param options 查询选项
   * @returns Promise<{ rows: Comment[], count: number }>
   */
  public static async findPending(
    options: {
      limit?: number
      offset?: number
    } = {}
  ): Promise<{ rows: Comment[], count: number }> {
    const { limit = 10, offset = 0 } = options

    return this.findAndCountAll({
      where: { status: 'pending' },
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username']
        },
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'slug']
        }
      ],
      order: [['createdAt', 'ASC']],
      limit,
      offset,
      distinct: true
    })
  }
}


/**
 * 初始化评论模型的数据库映射配置
 * 包含字段定义、验证规则等
 */
Comment.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 2000] // 限制评论长度
      }
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected'),
      allowNull: false,
      defaultValue: 'pending'
    },
    articleId: {
      type: DataTypes.INTEGER,
      allowNull: true,  // 改为可选，因为可能是说说评论
      field: 'article_id',
      references: {
        model: 'articles',
        key: 'id'
      }
    },
    postId: {
      type: DataTypes.INTEGER,
      allowNull: true,  // 说说评论时必填，文章评论时为空
      field: 'post_id',
      references: {
        model: 'posts',
        key: 'id'
      }
    },
    authorId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'author_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    parentId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'parent_id',
      references: {
        model: 'comments',
        key: 'id'
      }
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  },
  {
    sequelize,
    modelName: 'Comment',
    tableName: 'comments',
    timestamps: true,
    underscored: true,
    /**
     * 模型钩子函数配置
     */
    hooks: {
      /**
       * 在创建前进行内容安全处理和验证
       */
      beforeCreate: (comment: Comment) => {
        // 验证articleId和postId中至少有一个不为空
        if (!comment.articleId && !comment.postId) {
          throw new Error('评论必须关联文章或说说')
        }
        // 验证不能同时关联文章和说说
        if (comment.articleId && comment.postId) {
          throw new Error('评论不能同时关联文章和说说')
        }
        // 基础的XSS防护：移除script标签
        comment.content = comment.content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      },
      /**
       * 在更新前进行内容安全处理和验证
       */
      beforeUpdate: (comment: Comment) => {
        // 验证articleId和postId中至少有一个不为空
        if (!comment.articleId && !comment.postId) {
          throw new Error('评论必须关联文章或说说')
        }
        // 验证不能同时关联文章和说说
        if (comment.articleId && comment.postId) {
          throw new Error('评论不能同时关联文章和说说')
        }
        if (comment.changed('content')) {
          // 基础的XSS防护：移除script标签
          comment.content = comment.content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        }
      }
    }
  }
)
