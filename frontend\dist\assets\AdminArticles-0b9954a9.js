import{d as I,a as o,e,t as r,F as $,f as P,b as m,n as T,s as ee,o as i,_ as z,q as te,r as _,c as D,p as se,k as ae,u,i as C,w as S,v as F,x as le,A as ne,h as w,y as oe,z as ie,B as ce}from"./index-1ed5a19c.js";import{u as re}from"./article-24020b73.js";const ue={class:"modal-header"},de={class:"modal-body"},ve={key:0,class:"details"},_e={class:"modal-footer"},he=I({__name:"ConfirmModal",props:{show:{type:Boolean},title:{},message:{},details:{},confirmText:{default:"确认"},isDanger:{type:Boolean,default:!1}},emits:["confirm","cancel"],setup(L,{emit:b}){const h=b,a=()=>{h("confirm")},d=()=>{h("cancel")},g=()=>{d()};return(v,n)=>v.show?(i(),o("div",{key:0,class:"modal-overlay",onClick:g},[e("div",{class:"modal-content",onClick:n[0]||(n[0]=ee(()=>{},["stop"]))},[e("div",ue,[e("h3",null,r(v.title),1),e("button",{onClick:d,class:"close-btn"},"×")]),e("div",de,[e("p",null,r(v.message),1),v.details?(i(),o("div",ve,[e("ul",null,[(i(!0),o($,null,P(v.details,f=>(i(),o("li",{key:f},r(f),1))),128))])])):m("",!0)]),e("div",_e,[e("button",{onClick:d,class:"cancel-btn"},"取消"),e("button",{onClick:a,class:T(["confirm-btn",{danger:v.isDanger}])},r(v.confirmText),3)])])])):m("",!0)}});const pe=z(he,[["__scopeId","data-v-61161956"]]),me={class:"admin-articles-view"},ge={class:"admin-header"},fe={class:"user-info"},be={key:0},ke={class:"admin-nav"},ye={class:"admin-content"},Ce={class:"articles-header"},we={class:"filters-section"},Ae={class:"search-box"},xe={class:"filter-controls"},Me={key:0,class:"batch-operations"},De={class:"batch-info"},Se={class:"articles-list"},$e={key:0,class:"loading"},Pe={key:1,class:"empty"},Te={key:2},Ve={class:"select-all-row"},Be={class:"checkbox-label"},Fe=["checked","indeterminate"],Ne={class:"article-count"},Ie={class:"article-checkbox"},ze=["value"],Le={class:"article-info"},Ue={class:"article-meta"},Re={class:"date"},qe={key:0,class:"tags"},Ee={key:0,class:"article-excerpt"},Oe={class:"article-actions"},Qe=["onClick"],je={key:1,class:"pagination"},Ge=["disabled"],He={class:"page-numbers"},Je=["onClick"],Ke=["disabled"],We=I({__name:"AdminArticles",setup(L){const b=oe(),h=te(),a=re(),d=_(""),g=_(""),v=_(null),n=_([]),f=_(!1),A=_(""),x=_(""),M=_([]),y=_(null),U=D(()=>d.value.trim()!==""||g.value!==""),R=D(()=>a.articles.length>0&&n.value.length===a.articles.length),q=D(()=>n.value.length>0&&n.value.length<a.articles.length),E=D(()=>{const l=a.pagination.currentPage,t=a.pagination.totalPages,c=[],s=Math.max(1,l-2),p=Math.min(t,l+2);for(let B=s;B<=p;B++)c.push(B);return c}),O=async()=>{await h.logout(),b.push("/login")},Q=l=>new Date(l).toLocaleDateString("zh-CN"),j=()=>{v.value&&clearTimeout(v.value),v.value=setTimeout(()=>{k()},500)},G=()=>{d.value="",k()},H=()=>{k()},J=()=>{d.value="",g.value="",k()},k=async(l=1)=>{try{await a.fetchArticles(l,20,{search:d.value.trim()||void 0,status:g.value||void 0}),n.value=[]}catch(t){console.error("加载文章失败:",t)}},V=l=>{l>=1&&l<=a.pagination.totalPages&&k(l)},K=l=>{l.target.checked?n.value=a.articles.map(c=>c.id):n.value=[]},W=()=>{n.value=[]},X=l=>{A.value="删除文章",x.value="确定要删除这篇文章吗？此操作无法撤销。",M.value=[`文章标题：${l.title}`],y.value=async()=>{await a.deleteArticle(l.id)},f.value=!0},Y=()=>{const l=n.value.length,t=a.articles.filter(c=>n.value.includes(c.id)).map(c=>c.title);A.value="批量删除文章",x.value=`确定要删除选中的 ${l} 篇文章吗？此操作无法撤销。`,M.value=t.map(c=>`• ${c}`),y.value=async()=>{await a.deleteArticles([...n.value]),n.value=[]},f.value=!0},Z=async()=>{if(y.value)try{await y.value()}catch(l){console.error("删除失败:",l)}N()},N=()=>{f.value=!1,A.value="",x.value="",M.value=[],y.value=null};return se(()=>b.currentRoute.value.path,()=>{n.value=[]}),ae(async()=>{if(!h.isAuthenticated){b.push("/login");return}if(!await h.checkAuth()){b.push("/login");return}await k()}),(l,t)=>{const c=ie("router-link");return i(),o("div",me,[e("header",ge,[t[5]||(t[5]=e("h1",null,"文章管理",-1)),e("div",fe,[u(h).user?(i(),o("span",be,"欢迎, "+r(u(h).user.username),1)):m("",!0),e("button",{onClick:O,class:"logout-button"},"退出登录")])]),e("nav",ke,[C(c,{to:"/admin",class:"nav-link"},{default:S(()=>t[6]||(t[6]=[w("仪表板",-1)])),_:1,__:[6]}),C(c,{to:"/admin/articles",class:"nav-link"},{default:S(()=>t[7]||(t[7]=[w("文章管理",-1)])),_:1,__:[7]})]),e("main",ye,[e("div",Ce,[t[9]||(t[9]=e("h2",null,"文章列表",-1)),C(c,{to:"/admin/articles/new",class:"new-article-btn"},{default:S(()=>t[8]||(t[8]=[w("新建文章",-1)])),_:1,__:[8]})]),e("div",we,[e("div",Ae,[F(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>d.value=s),type:"text",placeholder:"搜索文章标题或内容...",class:"search-input",onInput:j},null,544),[[le,d.value]]),d.value?(i(),o("button",{key:0,onClick:G,class:"clear-search-btn"}," × ")):m("",!0)]),e("div",xe,[F(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>g.value=s),onChange:H,class:"status-filter"},t[10]||(t[10]=[e("option",{value:""},"全部状态",-1),e("option",{value:"published"},"已发布",-1),e("option",{value:"draft"},"草稿",-1)]),544),[[ne,g.value]]),e("button",{onClick:J,class:"clear-filters-btn"}," 清除筛选 ")])]),n.value.length>0?(i(),o("div",Me,[e("div",De," 已选择 "+r(n.value.length)+" 篇文章 ",1),e("div",{class:"batch-actions"},[e("button",{onClick:Y,class:"batch-delete-btn"}," 批量删除 "),e("button",{onClick:W,class:"clear-selection-btn"}," 取消选择 ")])])):m("",!0),e("div",Se,[u(a).loading?(i(),o("div",$e,"加载中...")):u(a).articles.length===0?(i(),o("div",Pe,r(U.value?"没有找到符合条件的文章":"暂无文章"),1)):(i(),o("div",Te,[e("div",Ve,[e("label",Be,[e("input",{type:"checkbox",checked:R.value,indeterminate:q.value,onChange:K},null,40,Fe),t[11]||(t[11]=w(" 全选 ",-1))]),e("span",Ne," 共 "+r(u(a).pagination.totalItems)+" 篇文章 ",1)]),(i(!0),o($,null,P(u(a).articles,s=>(i(),o("div",{key:s.id,class:T(["article-item",{selected:n.value.includes(s.id)}])},[e("div",Ie,[F(e("input",{type:"checkbox",value:s.id,"onUpdate:modelValue":t[2]||(t[2]=p=>n.value=p)},null,8,ze),[[ce,n.value]])]),e("div",Le,[e("h3",null,r(s.title),1),e("p",Ue,[e("span",{class:T(["status",s.status])},r(s.status==="published"?"已发布":"草稿"),3),e("span",Re,r(Q(s.updatedAt)),1),s.tags&&s.tags.length>0?(i(),o("span",qe,[(i(!0),o($,null,P(s.tags,p=>(i(),o("span",{key:p.id,class:"tag"},r(p.name),1))),128))])):m("",!0)]),s.excerpt?(i(),o("p",Ee,r(s.excerpt),1)):m("",!0)]),e("div",Oe,[C(c,{to:`/admin/articles/${s.id}/edit`,class:"edit-btn"},{default:S(()=>t[12]||(t[12]=[w(" 编辑 ",-1)])),_:2,__:[12]},1032,["to"]),e("button",{onClick:p=>X(s),class:"delete-btn"}," 删除 ",8,Qe)])],2))),128))]))]),u(a).pagination.totalPages>1?(i(),o("div",je,[e("button",{onClick:t[3]||(t[3]=s=>V(u(a).pagination.currentPage-1)),disabled:!u(a).pagination.hasPrevPage,class:"page-btn"}," 上一页 ",8,Ge),e("div",He,[(i(!0),o($,null,P(E.value,s=>(i(),o("button",{key:s,onClick:p=>V(s),class:T([{active:s===u(a).pagination.currentPage},"page-number"])},r(s),11,Je))),128))]),e("button",{onClick:t[4]||(t[4]=s=>V(u(a).pagination.currentPage+1)),disabled:!u(a).pagination.hasNextPage,class:"page-btn"}," 下一页 ",8,Ke)])):m("",!0)]),C(pe,{show:f.value,title:A.value,message:x.value,details:M.value,"confirm-text":"删除","is-danger":!0,onConfirm:Z,onCancel:N},null,8,["show","title","message","details"])])}}});const Ze=z(We,[["__scopeId","data-v-f7ccbc8c"]]);export{Ze as default};
