import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 执行数据库迁移升级操作，为评论表添加说说支持
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 返回空的Promise，表示异步操作完成
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 添加post_id字段以支持说说评论
  await queryInterface.addColumn('comments', 'post_id', {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'posts',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE'
  })

  // 修改article_id字段为可选，因为现在评论可能属于文章或说说
  await queryInterface.changeColumn('comments', 'article_id', {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'articles',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE'
  })

  // 为post_id字段添加索引以提高查询性能
  try {
    await queryInterface.addIndex('comments', ['post_id'])
  } catch {
    console.log('Index comments_post_id already exists, skipping...')
  }

  // 添加检查约束，确保article_id和post_id中至少有一个不为空
  // 注意：不同数据库的约束语法可能不同，这里使用通用的方法
  try {
    await queryInterface.sequelize.query(`
      ALTER TABLE comments 
      ADD CONSTRAINT comments_target_check 
      CHECK (
        (article_id IS NOT NULL AND post_id IS NULL) OR 
        (article_id IS NULL AND post_id IS NOT NULL)
      )
    `)
  } catch (error) {
    console.log('Check constraint comments_target_check already exists or not supported, skipping...', error)
  }
}

/**
 * 执行数据库迁移降级操作，移除说说评论支持
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 返回空的Promise，表示异步操作完成
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  // 移除检查约束
  try {
    await queryInterface.sequelize.query(`
      ALTER TABLE comments 
      DROP CONSTRAINT comments_target_check
    `)
  } catch {
    console.log('Check constraint comments_target_check does not exist or not supported, skipping...')
  }

  // 移除post_id字段
  await queryInterface.removeColumn('comments', 'post_id')

  // 恢复article_id字段为必填
  await queryInterface.changeColumn('comments', 'article_id', {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'articles',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE'
  })
}
