{"version": 3, "file": "AsyncTaskManager.cjs", "sourceRoot": "", "sources": ["../../src/async-task-manager/AsyncTaskManager.ts"], "names": [], "mappings": ";;AAEA,mIAAmI;AACnI,MAAM,KAAK,GAAG;IACb,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;IAClD,YAAY,EAAE,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;IACtD,cAAc,EAAE,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC;CAC1D,CAAC;AAEF;;GAEG;AACH,MAAqB,gBAAgB;IAC5B,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAClB,YAAY,GAAgD,EAAE,CAAC;IAC/D,gBAAgB,GAAG,CAAC,CAAC;IACrB,aAAa,GAAqB,EAAE,CAAC;IACrC,iBAAiB,GAAuB,EAAE,CAAC;IAC3C,UAAU,GAA4D,IAAI,GAAG,EAAE,CAAC;IAChF,sBAAsB,GAA0B,IAAI,CAAC;IACrD,0BAA0B,GAG7B,EAAE,CAAC;IACA,OAAO,GAAG,KAAK,CAAC;IAChB,SAAS,GAAG,KAAK,CAAC;IAC1B,aAAa,GAA0B,IAAI,CAAC;IAC5C,aAAa,CAAgB;IAE7B;;;;OAIG;IACH,YAAY,YAA2B;QACtC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;IACnC,CAAC;IAED;;;;OAIG;IACI,iBAAiB;QACvB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACtC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK;QACX,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACtC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC1D,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,OAAO;QACb,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACtC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC1D,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,OAAuB;QACxC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC5B,OAAO;QACR,CAAC;QACD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACjC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAChD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACpC,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACjC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;YACvF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,KAAK,EAAE,CAAC,KAAM,CAAC,CAAC;QAClD,CAAC;IACF,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,OAAuB;QACtC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC5B,OAAO;QACR,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YAClB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACpC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC5B,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;YACvF,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;IACF,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,WAA6B;QAClD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAClC,OAAO;QACR,CAAC;QACD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACjC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAChD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACpC,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;YACvF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,KAAK,EAAE,CAAC,KAAM,CAAC,CAAC;QACtD,CAAC;IACF,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAA6B;QAChD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAClC,OAAO;QACR,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC1D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YAClB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACxC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC5B,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;YACvF,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;IACF,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,YAA0C;QAC1D,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,YAAY,EAAE,CAAC;gBAClB,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9B,CAAC;YACD,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CACxC,yGAAyG,CACzG,CAAC;QACH,CAAC;QACD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACjC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAChD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACpC,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC;QACnE,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;YACvF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,KAAK,EAAE,CAAC,KAAM,CAAC,CAAC;QACjD,CAAC;QACD,OAAO,MAAM,CAAC;IACf,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,MAAc;QAC5B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO;QACR,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YACjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC5B,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;YACvF,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC;IACF,CAAC;IAED;;;;OAIG;IACI,YAAY;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACK,SAAS;QACU,IAAI,CAAC,WAAY,CAAC,MAAM,EAAE,CAAC;QACrD,OAAiC,IAAI,CAAC,WAAY,CAAC,MAAM,CAAC;IAC3D,CAAC;IAED;;OAEG;IACK,mBAAmB;QAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;YACzF,OAAO;QACR,CAAC;QAED,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACjC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAChD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACpC,CAAC;QAED,0GAA0G;QAC1G,iGAAiG;QACjG,oFAAoF;QACpF,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE;YACnD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;gBAC5F,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBACxB,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACxC,CAAC;gBACD,MAAM,SAAS,GAAG,IAAI,CAAC,0BAA0B,CAAC;gBAClD,IAAI,CAAC,0BAA0B,GAAG,EAAE,CAAC;gBACrC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;oBAClC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACpB,CAAC;gBACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACtB,CAAC;iBAAM,CAAC;gBACP,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,CAAC;QACF,CAAC,EAAE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,cAAc;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;QACrE,IAAI,CAAC,KAAK,EAAE,sBAAsB,IAAI,KAAK,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;YACxE,OAAO;QACR,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,OAAO;QACR,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE;YAC1C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAE1B,IAAI,YAAY,GAAG,8DAClB,IAAI,CAAC,UAAU,CAAC,IACjB,QACC,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GACnC,kEAAkE,CAAC;YAEnE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;gBACtD,MAAM,IAAI,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;gBACxD,YAAY,IAAI,GAAG,IAAI,KAAK,GAAG,2CAA2C,KAAK;qBAC7E,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;qBACrB,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC;YACrC,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;YAEtC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBACxD,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC,EAAE,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACK,QAAQ,CAAC,OAAgB;QAChC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACjD,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QAEvC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;QAE5B,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACjC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAChD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACpC,CAAC;QAED,KAAK,MAAM,SAAS,IAAI,iBAAiB,EAAE,CAAC;YAC3C,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QACjC,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;YACnC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;QAED,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAC7C,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QAED,+DAA+D;QAC/D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACtC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACJ,CAAC;;AAnUF,mCAoUC"}