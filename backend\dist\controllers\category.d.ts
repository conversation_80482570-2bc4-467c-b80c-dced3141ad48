import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: number;
        username: string;
    };
}
export declare const getCategories: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getCategory: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const createCategory: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const updateCategory: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getCategoryArticles: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getCategoryStats: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const deleteCategories: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const deleteCategory: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export {};
//# sourceMappingURL=category.d.ts.map