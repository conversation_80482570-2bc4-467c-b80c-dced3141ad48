import { DataTypes, Model, Optional, Association } from 'sequelize'
import { sequelize } from '../config/database'
import { generateSlug } from '../utils/slug'
import { Article } from './Article'


/**
 * 分类模型的属性接口定义
 */
export interface CategoryAttributes {
  id: number
  name: string
  slug: string
  description?: string
  parentId?: number
  sort: number
  createdAt: Date
  updatedAt: Date
}


/**
 * 分类创建时的属性接口定义，部分字段为可选
 */
export interface CategoryCreationAttributes extends Optional<CategoryAttributes, 'id' | 'slug' | 'description' | 'parentId' | 'sort' | 'createdAt' | 'updatedAt'> { }


/**
 * 分类模型类，继承自Sequelize的Model基类
 * 实现了分类的基本操作和层次化关系
 */
export class Category extends Model<CategoryAttributes, CategoryCreationAttributes> implements CategoryAttributes {
  public id!: number
  public name!: string
  public slug!: string
  public description?: string
  public parentId?: number
  public sort!: number
  public createdAt!: Date
  public updatedAt!: Date

  // 关联关系
  public readonly articles?: Article[]
  public readonly parent?: Category
  public readonly children?: Category[]

  public static associations: {
    articles: Association<Category, Article>
    parent: Association<Category, Category>
    children: Association<Category, Category>
  }


  /**
   * 获取分类的完整路径（包含父分类）
   * @returns Promise<string> 分类路径，如 "技术/前端/JavaScript"
   */
  public async getFullPath(): Promise<string> {
    const path: string[] = [this.name]
    let current: Category | null = this

    while (current && current.parentId) {
      const parent: Category | null = await Category.findByPk(current.parentId)
      if (parent) {
        path.unshift(parent.name)
        current = parent
      } else {
        break
      }
    }

    return path.join('/')
  }

  /**
   * 获取分类下的所有子分类（递归）
   * @returns Promise<Category[]> 所有子分类数组
   */
  public async getAllChildren(): Promise<Category[]> {
    const directChildren = await Category.findAll({
      where: { parentId: this.id },
      order: [['sort', 'ASC'], ['name', 'ASC']]
    })

    const allChildren: Category[] = [...directChildren]

    for (const child of directChildren) {
      const grandChildren = await child.getAllChildren()
      allChildren.push(...grandChildren)
    }

    return allChildren
  }

  /**
   * 检查是否为指定分类的子分类
   * @param categoryId 要检查的父分类ID
   * @returns Promise<boolean> 是否为子分类
   */
  public async isChildOf(categoryId: number): Promise<boolean> {
    if (this.parentId === categoryId) {
      return true
    }

    if (this.parentId) {
      const parent = await Category.findByPk(this.parentId)
      if (parent) {
        return await parent.isChildOf(categoryId)
      }
    }

    return false
  }

  /**
   * 根据slug查找分类
   * @param slug 分类的唯一标识符
   * @returns Promise<Category | null> 找到的分类对象或null
   */
  public static async findBySlug(slug: string): Promise<Category | null> {
    return this.findOne({
      where: { slug },
      include: [
        { model: Category, as: 'parent' },
        { model: Category, as: 'children', separate: true, order: [['sort', 'ASC'], ['name', 'ASC']] }
      ]
    })
  }

  /**
   * 获取分类树结构
   * @param parentId 父分类ID，null表示获取根分类
   * @returns Promise<Category[]> 分类树数组
   */
  public static async getTree(parentId: number | null = null): Promise<Category[]> {
    const categories = await this.findAll({
      where: parentId === null
        ? { parentId: null } as any
        : { parentId: parentId },
      order: [['sort', 'ASC'], ['name', 'ASC']],
      include: [
        { model: Category, as: 'parent' }
      ]
    })

    // 递归获取子分类
    for (const category of categories) {
      const children = await this.getTree(category.id)
        ; (category as any).children = children
    }

    return categories
  }

  /**
   * 获取扁平化的分类列表，包含层级信息
   * @returns Promise<Array<Category & { level: number, path: string }>> 包含层级信息的分类列表
   */
  public static async getFlatList(): Promise<Array<Category & { level: number, path: string }>> {
    const tree = await this.getTree()
    const flatList: Array<Category & { level: number, path: string }> = []

    const flatten = async (categories: Category[], level: number = 0, parentPath: string = '') => {
      for (const category of categories) {
        const path = parentPath ? `${parentPath}/${category.name}` : category.name
        flatList.push({
          ...category.toJSON(),
          level,
          path
        } as Category & { level: number, path: string })

        if ((category as any).children && (category as any).children.length > 0) {
          await flatten((category as any).children, level + 1, path)
        }
      }
    }

    await flatten(tree)
    return flatList
  }

  /**
   * 获取分类统计信息（包含文章数量）
   * @param includeChildren 是否包含子分类的文章数量
   * @returns Promise<{ category: Category, articleCount: number }[]> 分类统计信息
   */
  public static async getStats(includeChildren: boolean = false): Promise<{ category: Category, articleCount: number }[]> {
    const categories = await this.findAll({
      order: [['sort', 'ASC'], ['name', 'ASC']]
    })

    const stats = []

    for (const category of categories) {
      let articleCount = 0

      if (includeChildren) {
        // 获取当前分类及所有子分类的文章数量
        const allChildren = await category.getAllChildren()
        const categoryIds = [category.id, ...allChildren.map(child => child.id)]

        articleCount = await Article.count({
          where: { categoryId: categoryIds }
        })
      } else {
        // 只获取当前分类的文章数量
        articleCount = await Article.count({
          where: { categoryId: category.id }
        })
      }

      stats.push({
        category,
        articleCount
      })
    }

    return stats
  }
}


/**
 * 初始化分类模型的数据库映射配置
 * 包含字段定义、验证规则、钩子函数等
 */
Category.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [1, 100],
        notEmpty: true
      }
    },
    slug: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        len: [1, 100],
        is: /^[a-z0-9-]+$/i
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    parentId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'parent_id',
      references: {
        model: 'categories',
        key: 'id'
      }
    },
    sort: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  },
  {
    sequelize,
    modelName: 'Category',
    tableName: 'categories',
    timestamps: true,
    underscored: true,
    /**
     * 模型钩子函数配置
     */
    hooks: {
      /**
       * 在验证前生成唯一slug
       */
      beforeValidate: async (category: Category) => {
        if (category.name && !category.slug) {
          let baseSlug = generateSlug(category.name)
          let slug = baseSlug
          let counter = 1

          while (await Category.findOne({ where: { slug } })) {
            slug = `${baseSlug}-${counter}`
            counter++
          }

          category.slug = slug
        }
      },
      /**
       * 在创建或更新前验证父分类关系
       */
      beforeSave: async (category: Category) => {
        // 防止自己成为自己的父分类
        if (category.parentId === category.id) {
          throw new Error('Category cannot be its own parent')
        }

        // 防止循环引用
        if (category.parentId && category.id) {
          const parent = await Category.findByPk(category.parentId)
          if (parent && await parent.isChildOf(category.id)) {
            throw new Error('Circular reference detected in category hierarchy')
          }
        }
      }
    }
  }
)
