import { Request, Response } from 'express'
import { Post, PostLike, User, Comment } from '../models'
import { Op } from 'sequelize'

/**
 * 扩展Request接口，添加用户信息
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
    email: string
  }
}

/**
 * 说说控制器类
 * 处理说说相关的所有HTTP请求
 */
export class PostController {
  /**
   * 获取说说列表
   * 支持分页、过滤和排序
   */
  public static async getPosts(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 10,
        visibility = 'public',
        authorId,
        search
      } = req.query

      const offset = (Number(page) - 1) * Number(limit)
      const whereClause: any = {}

      // 可见性过滤
      if (visibility) {
        whereClause.visibility = visibility
      }

      // 作者过滤
      if (authorId) {
        whereClause.authorId = Number(authorId)
      }

      // 搜索过滤
      if (search) {
        whereClause.content = {
          [Op.like]: `%${search}%`
        }
      }

      const { rows: posts, count } = await Post.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['id', 'username', 'email']
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: Number(limit),
        offset,
        distinct: true
      })

      // 获取每个说说的完整信息（点赞数、评论数等）
      const authReq = req as AuthenticatedRequest
      const userId = authReq.user?.id

      const postsWithInfo = await Promise.all(
        posts.map(async (post) => {
          const fullInfo = await post.getFullInfo(userId)
          return fullInfo
        })
      )

      res.json({
        success: true,
        data: {
          posts: postsWithInfo,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: count,
            totalPages: Math.ceil(count / Number(limit))
          }
        }
      })
    } catch (error) {
      console.error('获取说说列表失败:', error)
      res.status(500).json({
        success: false,
        message: '获取说说列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 获取单个说说详情
   */
  public static async getPost(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params
      const authReq = req as AuthenticatedRequest
      const userId = authReq.user?.id

      const post = await Post.findByPk(Number(id), {
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['id', 'username', 'email']
          }
        ]
      })

      if (!post) {
        res.status(404).json({
          success: false,
          message: '说说不存在'
        })
        return
      }

      // 检查权限
      const canView = await post.canBeViewedBy(userId)
      if (!canView) {
        res.status(403).json({
          success: false,
          message: '没有权限查看此说说'
        })
        return
      }

      const fullInfo = await post.getFullInfo(userId)

      res.json({
        success: true,
        data: fullInfo
      })
    } catch (error) {
      console.error('获取说说详情失败:', error)
      res.status(500).json({
        success: false,
        message: '获取说说详情失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 创建新说说
   */
  public static async createPost(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { content, images, visibility = 'public', location } = req.body
      const userId = req.user!.id

      // 验证必填字段
      if (!content || content.trim().length === 0) {
        res.status(400).json({
          success: false,
          message: '说说内容不能为空'
        })
        return
      }

      const post = await Post.create({
        content: content.trim(),
        images: images || [],
        visibility,
        location,
        authorId: userId
      })

      // 获取完整的说说信息
      const createdPost = await Post.findByPk(post.id, {
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['id', 'username', 'email']
          }
        ]
      })

      const fullInfo = await createdPost!.getFullInfo(userId)

      res.status(201).json({
        success: true,
        message: '说说发布成功',
        data: fullInfo
      })
    } catch (error) {
      console.error('创建说说失败:', error)
      res.status(500).json({
        success: false,
        message: '创建说说失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 更新说说
   */
  public static async updatePost(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params
      const { content, images, visibility, location } = req.body
      const userId = req.user!.id

      const post = await Post.findByPk(Number(id))

      if (!post) {
        res.status(404).json({
          success: false,
          message: '说说不存在'
        })
        return
      }

      // 检查权限：只有作者可以编辑
      if (post.authorId !== userId) {
        res.status(403).json({
          success: false,
          message: '只能编辑自己的说说'
        })
        return
      }

      // 更新字段
      if (content !== undefined) post.content = content.trim()
      if (images !== undefined) post.images = images
      if (visibility !== undefined) post.visibility = visibility
      if (location !== undefined) post.location = location

      await post.save()

      // 获取更新后的完整信息
      const updatedPost = await Post.findByPk(post.id, {
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['id', 'username', 'email']
          }
        ]
      })

      const fullInfo = await updatedPost!.getFullInfo(userId)

      res.json({
        success: true,
        message: '说说更新成功',
        data: fullInfo
      })
    } catch (error) {
      console.error('更新说说失败:', error)
      res.status(500).json({
        success: false,
        message: '更新说说失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 删除说说
   */
  public static async deletePost(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params
      const userId = req.user!.id

      const post = await Post.findByPk(Number(id))

      if (!post) {
        res.status(404).json({
          success: false,
          message: '说说不存在'
        })
        return
      }

      // 检查权限：只有作者可以删除
      if (post.authorId !== userId) {
        res.status(403).json({
          success: false,
          message: '只能删除自己的说说'
        })
        return
      }

      // 删除相关的点赞和评论
      await PostLike.deleteByPostId(Number(id))
      await Comment.destroy({ where: { postId: Number(id) } })

      // 删除说说
      await post.destroy()

      res.json({
        success: true,
        message: '说说删除成功'
      })
    } catch (error) {
      console.error('删除说说失败:', error)
      res.status(500).json({
        success: false,
        message: '删除说说失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 切换点赞状态
   */
  public static async toggleLike(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params
      const userId = req.user!.id

      const post = await Post.findByPk(Number(id))

      if (!post) {
        res.status(404).json({
          success: false,
          message: '说说不存在'
        })
        return
      }

      // 检查权限
      const canView = await post.canBeViewedBy(userId)
      if (!canView) {
        res.status(403).json({
          success: false,
          message: '没有权限操作此说说'
        })
        return
      }

      const result = await PostLike.toggleLike(Number(id), userId)

      res.json({
        success: true,
        message: result.action === 'liked' ? '点赞成功' : '取消点赞成功',
        data: {
          action: result.action,
          likeCount: result.likeCount
        }
      })
    } catch (error) {
      console.error('切换点赞状态失败:', error)
      res.status(500).json({
        success: false,
        message: '操作失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 获取说说的点赞用户列表
   */
  public static async getLikes(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params
      const { page = 1, limit = 20 } = req.query

      const post = await Post.findByPk(Number(id))

      if (!post) {
        res.status(404).json({
          success: false,
          message: '说说不存在'
        })
        return
      }

      const offset = (Number(page) - 1) * Number(limit)
      const result = await PostLike.getLikeUsers(Number(id), Number(limit), offset)

      res.json({
        success: true,
        data: {
          likes: result.rows,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: result.count,
            totalPages: Math.ceil(result.count / Number(limit))
          }
        }
      })
    } catch (error) {
      console.error('获取点赞列表失败:', error)
      res.status(500).json({
        success: false,
        message: '获取点赞列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }
}
