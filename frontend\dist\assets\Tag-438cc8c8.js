import{d as h,c as s,p as v,k as f,a as k,i as r,e as a,h as d,t as _,w as b,u as w,m as x,o as y,H as T,R as A,_ as C}from"./index-1ed5a19c.js";import{A as N}from"./ArticleList-95a2fca7.js";import{u as R}from"./article-24020b73.js";import"./Loading-e63c58bf.js";const B={class:"tag-page"},S={class:"main-content"},V={class:"container"},H={class:"tag-header"},I={class:"tag-title"},L={class:"tag-name"},P={class:"tag-description"},D={class:"back-to-home"},E=h({__name:"Tag",setup(F){const i=x(),e=R(),c=s(()=>i.params.name),p=s(()=>e.articles),u=s(()=>e.loading),g=s(()=>e.error),l=s(()=>e.pagination),o=async(n=1)=>{try{await e.fetchArticles(n,10,{tag:c.value,status:"published"})}catch(t){console.error("Failed to load tag articles:",t)}},m=n=>{o(n),window.scrollTo({top:0,behavior:"smooth"})};return v(()=>i.params.name,()=>{o()},{immediate:!0}),f(()=>{o()}),(n,t)=>(y(),k("div",B,[r(T),a("main",S,[a("div",V,[a("div",H,[a("h1",I,[t[0]||(t[0]=d(" 标签：",-1)),a("span",L,_(c.value),1)]),a("p",P," 共找到 "+_(l.value.totalItems)+" 篇相关文章 ",1)]),r(N,{articles:p.value,loading:u.value,error:g.value,pagination:l.value,onPageChange:m,onRetry:o},null,8,["articles","loading","error","pagination"]),a("div",D,[r(w(A),{to:"/",class:"back-link"},{default:b(()=>t[1]||(t[1]=[d("← 返回首页",-1)])),_:1,__:[1]})])])])]))}});const G=C(E,[["__scopeId","data-v-86b40585"]]);export{G as default};
