"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../controllers/auth");
const auth_2 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.post('/login', auth_1.login);
router.post('/logout', auth_1.logout);
router.get('/profile', auth_2.authenticateToken, auth_1.getProfile);
router.post('/refresh', auth_1.refreshToken);
router.post('/validate', auth_1.validateToken);
exports.default = router;
//# sourceMappingURL=auth.js.map