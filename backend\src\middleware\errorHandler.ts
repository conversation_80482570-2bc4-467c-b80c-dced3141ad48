import { Request, Response, NextFunction } from 'express'

/**
 * 扩展Error接口，添加HTTP状态码和错误代码属性
 */
export interface ApiError extends Error {
  statusCode?: number
  code?: string
}

/**
 * 统一错误处理中间件
 * 捕获并处理API错误，返回标准化的错误响应
 * @param err - ApiError错误对象
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const errorHandler = (
  err: ApiError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const statusCode = err.statusCode || 500
  const code = err.code || 'INTERNAL_SERVER_ERROR'
  const message = err.message || 'Internal server error'

  // 在开发环境中记录详细的错误信息
  if (process.env.NODE_ENV === 'development') {
    console.error('Error:', {
      statusCode,
      code,
      message,
      stack: err.stack,
      url: req.url,
      method: req.method
    })
  }

  res.status(statusCode).json({
    success: false,
    error: {
      code,
      message,
      ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    }
  })
}

/**
 * 创建自定义API错误对象
 * @param statusCode - HTTP状态码
 * @param message - 错误消息
 * @param code - 错误代码（可选）
 * @returns 包含状态码和错误代码的Error对象
 */
export const createError = (statusCode: number, message: string, code?: string): ApiError => {
  const error = new Error(message) as ApiError
  error.statusCode = statusCode
  if (code) {
    error.code = code
  }
  return error
}

/**
 * 异步路由处理函数包装器
 * 捕获异步函数中的错误并传递给错误处理中间件
 * @param fn - 需要包装的异步处理函数
 * @returns Express路由处理函数
 */
export const asyncHandler = (fn: Function) => (req: Request, res: Response, next: NextFunction) => {
  Promise.resolve(fn(req, res, next)).catch(next)
}