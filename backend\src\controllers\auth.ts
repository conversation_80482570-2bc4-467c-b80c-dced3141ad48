import { Request, Response, NextFunction } from 'express'
import { AuthService } from '../services/auth'
import { createError } from '../middleware/errorHandler'
import Joi from 'joi'


/**
 * 登录请求参数的校验规则
 * - username: 字符串类型，仅允许字母数字，长度3-50位，必填
 * - password: 字符串类型，长度6-100位，必填
 */
const loginSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(50).required(),
  password: Joi.string().min(6).max(100).required()
})

/**
 * 刷新Token请求参数的校验规则
 * - token: 字符串类型，必填
 */
const refreshTokenSchema = Joi.object({
  token: Joi.string().required()
})


/**
 * 用户登录接口
 * @param req - Express请求对象，包含登录所需的用户名和密码
 * @param res - Express响应对象，用于返回登录结果
 * @param next - Express中间件的next函数，用于传递错误
 * @returns 无返回值，通过res.json返回登录结果或next传递错误
 */
export const login = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {

    // 校验请求体参数
    const { error, value } = loginSchema.validate(req.body)
    if (error) {
      throw createError(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR')
    }

    const { username, password } = value


    // 调用认证服务进行登录
    const result = await AuthService.login({ username, password })

    if (!result.success) {
      throw createError(401, result.message || 'Authentication failed', 'AUTH_FAILED')
    }


    // 返回登录成功信息及用户数据和token
    res.json({
      success: true,
      data: {
        user: result.user,
        token: result.token
      },
      message: 'Login successful'
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 用户登出接口
 * @param req - Express请求对象
 * @param res - Express响应对象，用于返回登出结果
 * @param next - Express中间件的next函数，用于传递错误
 * @returns 无返回值，通过res.json返回登出结果或next传递错误
 */
export const logout = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {

    res.json({
      success: true,
      message: 'Logout successful'
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 获取当前用户信息接口
 * @param req - Express请求对象，应包含已解析的用户信息
 * @param res - Express响应对象，用于返回用户信息
 * @param next - Express中间件的next函数，用于传递错误
 * @returns 无返回值，通过res.json返回用户信息或next传递错误
 */
export const getProfile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {

    // 从请求中获取用户信息
    const user = (req as any).user

    if (!user) {
      throw createError(401, 'User not authenticated', 'UNAUTHORIZED')
    }

    res.json({
      success: true,
      data: {
        user
      }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 刷新访问令牌接口
 * @param req - Express请求对象，包含旧的刷新令牌
 * @param res - Express响应对象，用于返回新的用户信息和访问令牌
 * @param next - Express中间件的next函数，用于传递错误
 * @returns 无返回值，通过res.json返回刷新结果或next传递错误
 */
export const refreshToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {

    // 校验请求体中的token参数
    const { error, value } = refreshTokenSchema.validate(req.body)
    if (error) {
      throw createError(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR')
    }

    const { token } = value


    // 调用认证服务刷新token
    const result = await AuthService.refreshToken(token)

    if (!result.success) {
      throw createError(401, result.message || 'Token refresh failed', 'TOKEN_REFRESH_FAILED')
    }

    res.json({
      success: true,
      data: {
        user: result.user,
        token: result.token
      },
      message: 'Token refreshed successfully'
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 验证访问令牌有效性接口
 * @param req - Express请求对象，包含Authorization头部
 * @param res - Express响应对象，用于返回验证结果
 * @param next - Express中间件的next函数，用于传递错误
 * @returns 无返回值，通过res.json返回验证结果或next传递错误
 */
export const validateToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // 从请求头中提取token
    const authHeader = req.headers['authorization']
    const token = AuthService.extractTokenFromHeader(authHeader)

    if (!token) {
      throw createError(400, 'Token is required', 'TOKEN_REQUIRED')
    }

    // 调用认证服务验证token
    const result = await AuthService.validateToken(token)

    if (!result.success) {
      throw createError(401, result.message || 'Token validation failed', 'TOKEN_INVALID')
    }

    res.json({
      success: true,
      data: {
        user: result.user,
        valid: true
      },
      message: 'Token is valid'
    })
  } catch (error) {
    next(error)
  }
}