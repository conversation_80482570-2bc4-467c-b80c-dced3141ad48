import{d as C,r as T,c as _,a as c,t as p,b as $,e as r,F as A,f as S,o as i,g as b,w as L,h as O,n as V,u as m,R as B,_ as k,i as w,j as z,k as M,l as x,H as R}from"./index-1ed5a19c.js";import{A as D}from"./ArticleList-95a2fca7.js";import{E as H}from"./ErrorPage-e0ba7a8a.js";import{u as I}from"./article-24020b73.js";import{u as N}from"./tag-4001fcd0.js";import"./Loading-e63c58bf.js";const P={class:"tag-cloud"},F={key:0,class:"tag-cloud-title"},U={key:1,class:"loading"},W={key:2,class:"error"},j={key:3,class:"empty"},G={key:4,class:"tags-container"},q={key:5,class:"view-all"},J=C({__name:"TagCloud",props:{tags:{},loading:{type:Boolean,default:!1},error:{default:null},maxTags:{default:20},showTitle:{type:Boolean,default:!0},title:{default:"标签云"},showViewAll:{type:Boolean,default:!0},minSize:{default:1},maxSize:{default:5}},emits:["retry"],setup(d){const e=d,n=T(!1),g=_(()=>[...e.tags].sort((s,a)=>(a.articleCount||0)-(s.articleCount||0))),v=_(()=>n.value||!e.showViewAll?g.value:g.value.slice(0,e.maxTags)),l=_(()=>Math.max(...e.tags.map(s=>s.articleCount||0))),t=_(()=>Math.min(...e.tags.map(s=>s.articleCount||0))),o=s=>{if(l.value===t.value)return Math.floor((e.minSize+e.maxSize)/2);const u=((s.articleCount||0)-t.value)/(l.value-t.value),h=e.minSize+u*(e.maxSize-e.minSize);return Math.max(e.minSize,Math.min(e.maxSize,Math.round(h)))};return(s,a)=>(i(),c("div",P,[s.showTitle?(i(),c("h3",F,p(s.title),1)):$("",!0),s.loading?(i(),c("div",U,a[2]||(a[2]=[r("div",{class:"loading-spinner"},null,-1),r("span",null,"加载标签中...",-1)]))):s.error?(i(),c("div",W,[r("p",null,p(s.error),1),r("button",{onClick:a[0]||(a[0]=u=>s.$emit("retry")),class:"retry-btn"},"重试")])):v.value.length===0?(i(),c("div",j,a[3]||(a[3]=[r("p",null,"暂无标签",-1)]))):(i(),c("div",G,[(i(!0),c(A,null,S(v.value,u=>(i(),b(m(B),{key:u.id,to:`/tag/${u.slug}`,class:V(["tag-item",`size-${o(u)}`]),title:`${u.name} (${u.articleCount||0}篇文章)`},{default:L(()=>[O(p(u.name),1)]),_:2},1032,["to","class","title"]))),128))])),s.showViewAll&&s.tags.length>s.maxTags?(i(),c("div",q,[r("button",{onClick:a[1]||(a[1]=u=>n.value=!n.value),class:"view-all-btn"},p(n.value?"收起":`查看全部 ${s.tags.length} 个标签`),1)])):$("",!0)]))}});const K=k(J,[["__scopeId","data-v-ab54160a"]]),Q={class:"tag-list"},X={key:0,class:"tag-list-title"},Y={key:1,class:"loading"},Z={key:2,class:"error"},ee={key:3,class:"empty"},te={key:4,class:"tags-container"},se={class:"tag-name"},ae={key:0,class:"tag-count"},oe={key:5,class:"view-all"},ne=C({__name:"TagList",props:{tags:{},loading:{type:Boolean,default:!1},error:{default:null},maxTags:{default:10},showTitle:{type:Boolean,default:!0},title:{default:"标签列表"},showViewAll:{type:Boolean,default:!0},showCount:{type:Boolean,default:!0},sortBy:{default:"count"},sortOrder:{default:"desc"}},emits:["retry"],setup(d){const e=d,n=T(!1),g=_(()=>[...e.tags].sort((t,o)=>{switch(e.sortBy){case"name":return e.sortOrder==="asc"?t.name.localeCompare(o.name):o.name.localeCompare(t.name);case"count":const s=t.articleCount||0,a=o.articleCount||0;return e.sortOrder==="asc"?s-a:a-s;case"date":return e.sortOrder==="asc"?new Date(t.createdAt).getTime()-new Date(o.createdAt).getTime():new Date(o.createdAt).getTime()-new Date(t.createdAt).getTime();default:return 0}})),v=_(()=>n.value||!e.showViewAll?g.value:g.value.slice(0,e.maxTags));return(l,t)=>(i(),c("div",Q,[l.showTitle?(i(),c("h3",X,p(l.title),1)):$("",!0),l.loading?(i(),c("div",Y,t[2]||(t[2]=[r("div",{class:"loading-spinner"},null,-1),r("span",null,"加载标签中...",-1)]))):l.error?(i(),c("div",Z,[r("p",null,p(l.error),1),r("button",{onClick:t[0]||(t[0]=o=>l.$emit("retry")),class:"retry-btn"},"重试")])):v.value.length===0?(i(),c("div",ee,t[3]||(t[3]=[r("p",null,"暂无标签",-1)]))):(i(),c("div",te,[(i(!0),c(A,null,S(v.value,o=>(i(),c("div",{key:o.id,class:"tag-item"},[w(m(B),{to:`/tag/${o.slug}`,class:"tag-link"},{default:L(()=>[r("span",se,p(o.name),1),l.showCount?(i(),c("span",ae,"("+p(o.articleCount||0)+")",1)):$("",!0)]),_:2},1032,["to"])]))),128))])),l.showViewAll&&l.tags.length>l.maxTags?(i(),c("div",oe,[r("button",{onClick:t[1]||(t[1]=o=>n.value=!n.value),class:"view-all-btn"},p(n.value?"收起":`查看全部 ${l.tags.length} 个标签`),1)])):$("",!0)]))}});const re=k(ne,[["__scopeId","data-v-fad0c572"]]);function le(){const d=z(),e=T(!1),n=T(null),g=async(l,t)=>{const{loadingMessage:o,successMessage:s,errorMessage:a,showGlobalLoading:u=!1}=t||{};e.value=!0,n.value=null;let h;u&&o&&(h=d.startLoading(o));try{const f=await l();return s&&d.showSuccess(s),f}catch(f){const y=f.message||"操作失败";return n.value=y,a?d.showError(`${a}: ${y}`):d.showError(y),null}finally{e.value=!1,h&&d.stopLoading(h)}},v=async(l,t)=>{const{maxRetries:o=3,retryDelay:s=1e3,...a}=t||{};let u;for(let f=0;f<=o;f++)try{return await g(l,a)}catch(y){u=y,f<o&&await new Promise(E=>setTimeout(E,s*(f+1)))}const h=()=>v(l,t);throw d.showError(`操作失败，已重试 ${o} 次`,"error",h),u};return{isLoading:e,error:n,execute:g,executeWithRetry:v}}function ie(){const d=T(navigator.onLine),e=z(),n=()=>{d.value=!0,e.showSuccess("网络连接已恢复")},g=()=>{d.value=!1,e.showWarning("网络连接已断开，请检查网络设置")};return M(()=>{window.addEventListener("online",n),window.addEventListener("offline",g)}),x(()=>{window.removeEventListener("online",n),window.removeEventListener("offline",g)}),{isOnline:d}}const ue={class:"home"},ce={class:"main-content"},de={class:"content-wrapper"},ge={class:"container"},me={class:"main-content-grid"},ve={class:"articles-section"},pe={key:0,class:"offline-notice"},fe={class:"sidebar"},he=C({__name:"Home",setup(d){const e=I(),n=N(),{execute:g}=le(),{isOnline:v}=ie(),l=_(()=>e.articles.filter(a=>a.status==="published")),t=async(a=1)=>{await g(()=>e.fetchArticles(a,10,{status:"published"}),{errorMessage:"加载文章列表失败"})},o=async()=>{await g(()=>n.fetchTags(),{errorMessage:"加载标签失败"})},s=a=>{t(a),window.scrollTo({top:0,behavior:"smooth"})};return M(()=>{t(),o()}),(a,u)=>(i(),c("div",ue,[w(R),r("main",ce,[u[1]||(u[1]=r("div",{class:"hero-section"},[r("div",{class:"container"},[r("h1",{class:"hero-title"},"欢迎来到个人博客"),r("p",{class:"hero-subtitle"},"分享技术见解，记录成长历程")])],-1)),r("div",de,[r("div",ge,[r("div",me,[r("section",ve,[u[0]||(u[0]=r("h2",{class:"section-title"},"最新文章",-1)),m(v)?(i(),b(D,{key:1,articles:l.value,loading:m(e).loading,error:m(e).error,pagination:m(e).pagination,onPageChange:s,onRetry:t},null,8,["articles","loading","error","pagination"])):(i(),c("div",pe,[w(H,{type:"network",title:"网络连接断开",message:"请检查您的网络连接后重试","retry-action":()=>t()},null,8,["retry-action"])]))]),r("aside",fe,[w(K,{tags:m(n).tags,loading:m(n).loading,error:m(n).error,onRetry:o,title:"热门标签","max-tags":15},null,8,["tags","loading","error"]),w(re,{tags:m(n).tags,loading:m(n).loading,error:m(n).error,onRetry:o,title:"标签列表","max-tags":8,"sort-by":"count"},null,8,["tags","loading","error"])])])])])])]))}});const ke=k(he,[["__scopeId","data-v-024bb7a3"]]);export{ke as default};
