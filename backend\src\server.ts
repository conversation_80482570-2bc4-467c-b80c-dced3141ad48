import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import dotenv from 'dotenv'
import path from 'path'
import { sequelize } from './config/database'
import { errorHandler } from './middleware/errorHandler'
import { setupSwagger } from './config/swagger'
import './models'


// 加载环境变量配置
dotenv.config()

// 创建 Express 应用实例
const app = express()
// 设置服务器监听端口，默认为 3001
const PORT = process.env.PORT || 3001


// 使用 helmet 中间件增强应用安全性
app.use(helmet())
// 配置 CORS 跨域资源共享策略，允许来自前端 URL 或本地开发地址的请求，并支持凭证传输
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}))
// 使用 morgan 记录 HTTP 请求日志
app.use(morgan('combined'))
// 解析 JSON 格式的请求体，限制大小为 10MB
app.use(express.json({ limit: '10mb' }))
// 解析 URL 编码格式的请求体
app.use(express.urlencoded({ extended: true }))


// 初始化并配置 Swagger API 文档
setupSwagger(app)

// 配置静态文件服务，用于提供上传的图片
app.use('/uploads', express.static(path.join(process.cwd(), 'uploads')))


/**
 * 健康检查接口
 * @route GET /health
 * @returns {Object} 返回包含状态、时间戳和运行环境信息的对象
 */
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  })
})


// 导入并注册 API 路由，所有以 /api 开头的路径将被处理
import apiRoutes from './routes'
app.use('/api', apiRoutes)


// 注册全局错误处理中间件
app.use(errorHandler)


/**
 * 处理未匹配到任何路由的情况（404）
 * @param req - Express 请求对象
 * @param res - Express 响应对象
 */
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: 'Endpoint not found'
    }
  })
})


/**
 * 启动服务器函数
 * 包括数据库连接验证、模型同步以及启动 HTTP 服务
 */
const startServer = async () => {
  try {

    // 测试数据库连接是否正常
    await sequelize.authenticate()
    console.log('Database connection established successfully.')


    // 在非生产环境下自动同步数据库模型结构
    if (process.env.NODE_ENV !== 'production') {
      await sequelize.sync({ alter: true })
      console.log('Database models synchronized.')
    }


    // 启动 Express 服务并监听指定端口
    app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`)
      console.log(`API documentation available at http://localhost:${PORT}/api-docs`)
    })
  } catch (error) {
    console.error('Unable to start server:', error)
    process.exit(1)
  }
}


/**
 * 监听 SIGTERM 信号，用于优雅关闭服务器
 * 关闭数据库连接后退出进程
 */
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully...')
  await sequelize.close()
  process.exit(0)
})

/**
 * 监听 SIGINT 信号（如 Ctrl+C），用于优雅关闭服务器
 * 关闭数据库连接后退出进程
 */
process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully...')
  await sequelize.close()
  process.exit(0)
})

// 执行服务器启动函数
startServer()

// 导出 Express 应用实例供外部使用
export default app