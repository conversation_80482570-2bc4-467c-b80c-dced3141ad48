"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArticleTag = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
class ArticleTag extends sequelize_1.Model {
}
exports.ArticleTag = ArticleTag;
ArticleTag.init({
    articleId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'article_id',
        references: {
            model: 'articles',
            key: 'id'
        }
    },
    tagId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'tag_id',
        references: {
            model: 'tags',
            key: 'id'
        }
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'ArticleTag',
    tableName: 'article_tags',
    timestamps: false,
    underscored: true
});
//# sourceMappingURL=ArticleTag.js.map