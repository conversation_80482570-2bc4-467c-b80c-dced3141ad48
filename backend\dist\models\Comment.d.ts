import { Model, Optional, Association } from 'sequelize';
import { User } from './User';
export interface CommentAttributes {
    id: number;
    content: string;
    status: 'pending' | 'approved' | 'rejected';
    articleId?: number;
    postId?: number;
    authorId: number;
    parentId?: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface CommentCreationAttributes extends Optional<CommentAttributes, 'id' | 'status' | 'articleId' | 'postId' | 'parentId' | 'createdAt' | 'updatedAt'> {
}
export declare class Comment extends Model<CommentAttributes, CommentCreationAttributes> implements CommentAttributes {
    id: number;
    content: string;
    status: 'pending' | 'approved' | 'rejected';
    articleId?: number;
    postId?: number;
    authorId: number;
    parentId?: number;
    createdAt: Date;
    updatedAt: Date;
    readonly author?: User;
    readonly article?: any;
    readonly post?: any;
    readonly parent?: Comment;
    readonly replies?: Comment[];
    static associations: {
        author: Association<Comment, User>;
        article: Association<Comment, any>;
        post: Association<Comment, any>;
        parent: Association<Comment, Comment>;
        replies: Association<Comment, Comment>;
    };
    approve(): Promise<void>;
    reject(): Promise<void>;
    isTopLevel(): boolean;
    getPlainContent(): string;
    isArticleComment(): boolean;
    isPostComment(): boolean;
    static findByPostId(postId: number, options?: {
        limit?: number;
        offset?: number;
        includeReplies?: boolean;
    }): Promise<{
        rows: Comment[];
        count: number;
    }>;
    static findByArticleId(articleId: number, options?: {
        limit?: number;
        offset?: number;
        includeReplies?: boolean;
    }): Promise<{
        rows: Comment[];
        count: number;
    }>;
    static findByAuthorId(authorId: number, options?: {
        limit?: number;
        offset?: number;
        status?: 'pending' | 'approved' | 'rejected';
    }): Promise<{
        rows: Comment[];
        count: number;
    }>;
    static findPending(options?: {
        limit?: number;
        offset?: number;
    }): Promise<{
        rows: Comment[];
        count: number;
    }>;
}
//# sourceMappingURL=Comment.d.ts.map