"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const post_1 = require("../controllers/post");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const postSecurity_1 = require("../middleware/postSecurity");
const router = (0, express_1.Router)();
router.get('/', post_1.PostController.getPosts);
router.get('/:id', postSecurity_1.checkPostAccess, post_1.PostController.getPost);
router.get('/:id/likes', post_1.PostController.getLikes);
router.post('/', auth_1.authenticateToken, validation_1.validatePostCreation, postSecurity_1.createPostSecurity, post_1.PostController.createPost);
router.put('/:id', auth_1.authenticateToken, postSecurity_1.checkPostOwnership, validation_1.validatePostUpdate, postSecurity_1.postSecurityMiddleware, post_1.PostController.updatePost);
router.delete('/:id', auth_1.authenticateToken, postSecurity_1.checkPostOwnership, post_1.PostController.deletePost);
router.post('/:id/like', auth_1.authenticateToken, postSecurity_1.checkPostAccess, post_1.PostController.toggleLike);
exports.default = router;
//# sourceMappingURL=post.js.map