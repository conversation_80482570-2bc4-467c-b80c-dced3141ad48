"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateToken = exports.refreshToken = exports.getProfile = exports.logout = exports.login = void 0;
const auth_1 = require("../services/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const joi_1 = __importDefault(require("joi"));
const loginSchema = joi_1.default.object({
    username: joi_1.default.string().alphanum().min(3).max(50).required(),
    password: joi_1.default.string().min(6).max(100).required()
});
const refreshTokenSchema = joi_1.default.object({
    token: joi_1.default.string().required()
});
const login = async (req, res, next) => {
    try {
        const { error, value } = loginSchema.validate(req.body);
        if (error) {
            throw (0, errorHandler_1.createError)(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR');
        }
        const { username, password } = value;
        const result = await auth_1.AuthService.login({ username, password });
        if (!result.success) {
            throw (0, errorHandler_1.createError)(401, result.message || 'Authentication failed', 'AUTH_FAILED');
        }
        res.json({
            success: true,
            data: {
                user: result.user,
                token: result.token
            },
            message: 'Login successful'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.login = login;
const logout = async (req, res, next) => {
    try {
        res.json({
            success: true,
            message: 'Logout successful'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.logout = logout;
const getProfile = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user) {
            throw (0, errorHandler_1.createError)(401, 'User not authenticated', 'UNAUTHORIZED');
        }
        res.json({
            success: true,
            data: {
                user
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getProfile = getProfile;
const refreshToken = async (req, res, next) => {
    try {
        const { error, value } = refreshTokenSchema.validate(req.body);
        if (error) {
            throw (0, errorHandler_1.createError)(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR');
        }
        const { token } = value;
        const result = await auth_1.AuthService.refreshToken(token);
        if (!result.success) {
            throw (0, errorHandler_1.createError)(401, result.message || 'Token refresh failed', 'TOKEN_REFRESH_FAILED');
        }
        res.json({
            success: true,
            data: {
                user: result.user,
                token: result.token
            },
            message: 'Token refreshed successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.refreshToken = refreshToken;
const validateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = auth_1.AuthService.extractTokenFromHeader(authHeader);
        if (!token) {
            throw (0, errorHandler_1.createError)(400, 'Token is required', 'TOKEN_REQUIRED');
        }
        const result = await auth_1.AuthService.validateToken(token);
        if (!result.success) {
            throw (0, errorHandler_1.createError)(401, result.message || 'Token validation failed', 'TOKEN_INVALID');
        }
        res.json({
            success: true,
            data: {
                user: result.user,
                valid: true
            },
            message: 'Token is valid'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.validateToken = validateToken;
//# sourceMappingURL=auth.js.map