"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ensureUniqueSlug = exports.generateSlug = void 0;
const generateSlug = (text) => {
    const chineseMap = {
        '前端开发': 'frontend-dev',
        '后端开发': 'backend-dev',
        '全栈开发': 'fullstack-dev',
        'Web开发': 'web-dev',
        '数据库': 'database',
        '云计算': 'cloud-computing',
        '微服务': 'microservices',
        'API设计': 'api-design',
        '性能优化': 'performance-optimization',
        '代码规范': 'code-standards',
        '项目管理': 'project-management',
        '敏捷开发': 'agile-development',
        '测试驱动开发': 'test-driven-development',
        '持续集成': 'continuous-integration',
        '开源项目': 'open-source-projects',
        'JavaScript': 'javascript',
        'TypeScript': 'typescript',
        'Vue.js': 'vuejs',
        'React': 'react',
        'Angular': 'angular',
        'Node.js': 'nodejs',
        'Python': 'python',
        'Java': 'java',
        'Go': 'golang',
        'Rust': 'rust',
        'C++': 'cpp',
        'C#': 'csharp',
        'PHP': 'php',
        'Ruby': 'ruby',
        'Swift': 'swift',
        'Kotlin': 'kotlin',
        '移动开发': 'mobile-dev',
        'iOS开发': 'ios-dev',
        'Android开发': 'android-dev',
        '小程序': 'miniprogram',
        '人工智能': 'ai',
        '机器学习': 'machine-learning',
        '深度学习': 'deep-learning',
        '区块链': 'blockchain',
        '物联网': 'iot',
        '大数据': 'big-data',
        '算法': 'algorithm',
        '数据结构': 'data-structure',
        '网络安全': 'cybersecurity',
        '运维': 'devops',
        '容器化': 'containerization',
        'Docker': 'docker',
        'Kubernetes': 'kubernetes',
        'Linux': 'linux',
        'Windows': 'windows',
        'macOS': 'macos',
        '开发工具': 'dev-tools',
        '编辑器': 'editor',
        '版本控制': 'version-control',
        'Git': 'git',
        '测试': 'testing',
        '单元测试': 'unit-testing',
        '集成测试': 'integration-testing',
        '自动化测试': 'automation-testing',
        '前端框架': 'frontend-framework',
        '后端框架': 'backend-framework',
        '设计模式': 'design-pattern',
        '架构设计': 'architecture-design',
        '系统设计': 'system-design',
        '技术分享': 'tech-sharing',
        '学习笔记': 'study-notes',
        '教程': 'tutorial',
        '最佳实践': 'best-practices'
    };
    if (chineseMap[text]) {
        return chineseMap[text];
    }
    const hasChinese = /[\u4e00-\u9fff]/.test(text);
    if (hasChinese) {
        let slug = text
            .replace(/[\u4e00-\u9fff]/g, '')
            .toLowerCase()
            .trim()
            .replace(/[\s\W-]+/g, '-')
            .replace(/^-+|-+$/g, '');
        if (!slug || slug === '') {
            slug = `chinese-tag-${Date.now()}`;
        }
        return slug;
    }
    let slug = text
        .toLowerCase()
        .trim()
        .replace(/[\s\W-]+/g, '-')
        .replace(/^-+|-+$/g, '');
    if (!slug || slug === '') {
        slug = `tag-${Date.now()}`;
    }
    return slug;
};
exports.generateSlug = generateSlug;
const ensureUniqueSlug = async (baseSlug, checkExists) => {
    let slug = baseSlug;
    let counter = 1;
    while (await checkExists(slug)) {
        slug = `${baseSlug}-${counter}`;
        counter++;
    }
    return slug;
};
exports.ensureUniqueSlug = ensureUniqueSlug;
//# sourceMappingURL=slug.js.map