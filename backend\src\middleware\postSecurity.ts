import { Request, Response, NextFunction } from 'express'
import { createError } from './errorHandler'
import { Post } from '../models'

/**
 * 扩展 Express 的 Request 接口，添加用户认证信息
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
    email: string
  }
}

/**
 * 说说发布频率限制中间件
 * 防止用户短时间内发布过多说说
 */
export const postRateLimit = (() => {
  // 存储用户最后发布时间的内存缓存
  const userLastPostTime = new Map<number, number>()
  const RATE_LIMIT_WINDOW = 60 * 1000 // 60秒

  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next()
    }

    const userId = req.user.id
    const now = Date.now()
    const lastPostTime = userLastPostTime.get(userId)

    if (lastPostTime && (now - lastPostTime) < RATE_LIMIT_WINDOW) {
      const remainingTime = Math.ceil((RATE_LIMIT_WINDOW - (now - lastPostTime)) / 1000)
      throw createError(
        429,
        `请等待 ${remainingTime} 秒后再发布新的说说`,
        'RATE_LIMIT_EXCEEDED'
      )
    }

    // 更新用户最后发布时间
    userLastPostTime.set(userId, now)

    // 清理过期的记录（简单的内存管理）
    if (userLastPostTime.size > 1000) {
      const cutoffTime = now - RATE_LIMIT_WINDOW
      for (const [id, time] of userLastPostTime.entries()) {
        if (time < cutoffTime) {
          userLastPostTime.delete(id)
        }
      }
    }

    next()
  }
})()

/**
 * 说说内容安全检查中间件
 * 检查说说内容是否包含恶意内容
 */
export const postContentSecurity = (req: Request, res: Response, next: NextFunction): void => {
  const { content, images } = req.body

  if (!content) {
    return next()
  }

  // 检查是否包含恶意脚本
  const scriptPattern = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi
  if (scriptPattern.test(content)) {
    throw createError(400, '说说内容包含禁止的脚本标签', 'SECURITY_VIOLATION')
  }

  // 检查是否包含恶意链接
  const maliciousLinkPattern = /javascript:|data:|vbscript:|on\w+\s*=/gi
  if (maliciousLinkPattern.test(content)) {
    throw createError(400, '说说内容包含禁止的链接或事件', 'SECURITY_VIOLATION')
  }

  // 检查说说长度（额外的服务器端验证）
  if (content.length > 1000) {
    throw createError(400, '说说内容过长（最多1000个字符）', 'CONTENT_TOO_LONG')
  }

  // 检查是否为空内容或只包含空白字符
  if (!content.trim()) {
    throw createError(400, '说说内容不能为空', 'EMPTY_CONTENT')
  }

  // 验证图片URL
  if (images && Array.isArray(images)) {
    if (images.length > 9) {
      throw createError(400, '最多只能上传9张图片', 'TOO_MANY_IMAGES')
    }

    for (const imageUrl of images) {
      if (typeof imageUrl !== 'string' || !imageUrl.trim()) {
        throw createError(400, '图片URL格式无效', 'INVALID_IMAGE_URL')
      }

      // 简单的URL格式验证
      try {
        new URL(imageUrl)
      } catch {
        throw createError(400, '图片URL格式无效', 'INVALID_IMAGE_URL')
      }

      // 检查是否为支持的图片格式
      const supportedFormats = /\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i
      if (!supportedFormats.test(imageUrl)) {
        throw createError(400, '不支持的图片格式，仅支持 jpg、png、gif、webp', 'UNSUPPORTED_IMAGE_FORMAT')
      }
    }
  }

  next()
}

/**
 * 敏感词过滤中间件
 * 检查说说内容是否包含敏感词汇
 */
export const postProfanityFilter = (req: Request, res: Response, next: NextFunction): void => {
  const { content } = req.body

  if (!content) {
    return next()
  }

  // 简单的敏感词列表（实际应用中应该从数据库或配置文件加载）
  const profanityWords = [
    'spam', 'scam', 'fraud', 'hack', 'virus', 'phishing',
    // 可以根据需要添加更多敏感词
  ]

  const contentLower = content.toLowerCase()
  const foundProfanity = profanityWords.find(word => contentLower.includes(word))

  if (foundProfanity) {
    throw createError(400, '说说内容包含不当词汇', 'INAPPROPRIATE_CONTENT')
  }

  next()
}

/**
 * 说说权限检查中间件
 * 检查用户是否有权限查看或操作特定说说
 */
export const checkPostAccess = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params
    const userId = req.user?.id

    if (!id) {
      return next()
    }

    const post = await Post.findByPk(Number(id))

    if (!post) {
      throw createError(404, '说说不存在', 'POST_NOT_FOUND')
    }

    // 检查查看权限
    const canView = await post.canBeViewedBy(userId)
    if (!canView) {
      throw createError(403, '没有权限查看此说说', 'ACCESS_DENIED')
    }

    // 将说说信息添加到请求对象中，供后续中间件使用
    (req as any).post = post

    next()
  } catch (error) {
    next(error)
  }
}

/**
 * 说说所有权检查中间件
 * 检查用户是否为说说的作者
 */
export const checkPostOwnership = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params
    const userId = req.user?.id

    if (!userId) {
      throw createError(401, '需要登录', 'AUTHENTICATION_REQUIRED')
    }

    const post = await Post.findByPk(Number(id))

    if (!post) {
      throw createError(404, '说说不存在', 'POST_NOT_FOUND')
    }

    if (post.authorId !== userId) {
      throw createError(403, '只能操作自己的说说', 'OWNERSHIP_REQUIRED')
    }

    // 将说说信息添加到请求对象中，供后续中间件使用
    (req as any).post = post

    next()
  } catch (error) {
    next(error)
  }
}



/**
 * IP地址记录中间件
 * 记录操作者的IP地址用于安全审计
 */
export const recordPostIP = (req: Request, res: Response, next: NextFunction): void => {
  // 获取真实IP地址（考虑代理和负载均衡器）
  const getClientIP = (): string => {
    const forwardedFor = req.headers['x-forwarded-for']
    const realIP = req.headers['x-real-ip']

    if (typeof forwardedFor === 'string') {
      return forwardedFor.split(',')[0]?.trim() || 'unknown'
    }

    if (Array.isArray(forwardedFor) && forwardedFor[0]) {
      return forwardedFor[0]
    }

    if (typeof realIP === 'string') {
      return realIP
    }

    return req.socket?.remoteAddress || 'unknown'
  }

  // 将IP地址添加到请求体中，供控制器使用
  req.body.clientIP = getClientIP()

  next()
}

/**
 * 组合的说说安全中间件
 * 将多个安全检查组合在一起
 */
export const postSecurityMiddleware = [
  postContentSecurity,
  postProfanityFilter,
  recordPostIP
]

/**
 * 创建说说时的完整安全中间件链
 */
export const createPostSecurity = [
  postRateLimit,
  ...postSecurityMiddleware
]


