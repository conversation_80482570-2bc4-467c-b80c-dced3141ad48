"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.syncModels = exports.PostLike = exports.Post = exports.Comment = exports.Category = exports.ArticleTag = exports.Tag = exports.Article = exports.User = void 0;
const User_1 = require("./User");
Object.defineProperty(exports, "User", { enumerable: true, get: function () { return User_1.User; } });
const Article_1 = require("./Article");
Object.defineProperty(exports, "Article", { enumerable: true, get: function () { return Article_1.Article; } });
const Tag_1 = require("./Tag");
Object.defineProperty(exports, "Tag", { enumerable: true, get: function () { return Tag_1.Tag; } });
const ArticleTag_1 = require("./ArticleTag");
Object.defineProperty(exports, "ArticleTag", { enumerable: true, get: function () { return ArticleTag_1.ArticleTag; } });
const Category_1 = require("./Category");
Object.defineProperty(exports, "Category", { enumerable: true, get: function () { return Category_1.Category; } });
const Comment_1 = require("./Comment");
Object.defineProperty(exports, "Comment", { enumerable: true, get: function () { return Comment_1.Comment; } });
const Post_1 = require("./Post");
Object.defineProperty(exports, "Post", { enumerable: true, get: function () { return Post_1.Post; } });
const PostLike_1 = require("./PostLike");
Object.defineProperty(exports, "PostLike", { enumerable: true, get: function () { return PostLike_1.PostLike; } });
User_1.User.hasMany(Article_1.Article, {
    foreignKey: 'authorId',
    as: 'articles'
});
Article_1.Article.belongsTo(User_1.User, {
    foreignKey: 'authorId',
    as: 'author'
});
Category_1.Category.hasMany(Article_1.Article, {
    foreignKey: 'categoryId',
    as: 'articles'
});
Article_1.Article.belongsTo(Category_1.Category, {
    foreignKey: 'categoryId',
    as: 'category'
});
Category_1.Category.hasMany(Category_1.Category, {
    foreignKey: 'parentId',
    as: 'children'
});
Category_1.Category.belongsTo(Category_1.Category, {
    foreignKey: 'parentId',
    as: 'parent'
});
Article_1.Article.belongsToMany(Tag_1.Tag, {
    through: ArticleTag_1.ArticleTag,
    foreignKey: 'articleId',
    otherKey: 'tagId',
    as: 'tags'
});
Tag_1.Tag.belongsToMany(Article_1.Article, {
    through: ArticleTag_1.ArticleTag,
    foreignKey: 'tagId',
    otherKey: 'articleId',
    as: 'articles'
});
User_1.User.hasMany(Comment_1.Comment, {
    foreignKey: 'authorId',
    as: 'comments'
});
Comment_1.Comment.belongsTo(User_1.User, {
    foreignKey: 'authorId',
    as: 'author'
});
Article_1.Article.hasMany(Comment_1.Comment, {
    foreignKey: 'articleId',
    as: 'comments'
});
Comment_1.Comment.belongsTo(Article_1.Article, {
    foreignKey: 'articleId',
    as: 'article'
});
Comment_1.Comment.hasMany(Comment_1.Comment, {
    foreignKey: 'parentId',
    as: 'replies'
});
Comment_1.Comment.belongsTo(Comment_1.Comment, {
    foreignKey: 'parentId',
    as: 'parent'
});
User_1.User.hasMany(Post_1.Post, {
    foreignKey: 'authorId',
    as: 'posts'
});
Post_1.Post.belongsTo(User_1.User, {
    foreignKey: 'authorId',
    as: 'author'
});
Post_1.Post.hasMany(Comment_1.Comment, {
    foreignKey: 'postId',
    as: 'comments'
});
Comment_1.Comment.belongsTo(Post_1.Post, {
    foreignKey: 'postId',
    as: 'post'
});
Post_1.Post.hasMany(PostLike_1.PostLike, {
    foreignKey: 'postId',
    as: 'likes'
});
PostLike_1.PostLike.belongsTo(Post_1.Post, {
    foreignKey: 'postId',
    as: 'post'
});
User_1.User.hasMany(PostLike_1.PostLike, {
    foreignKey: 'userId',
    as: 'postLikes'
});
PostLike_1.PostLike.belongsTo(User_1.User, {
    foreignKey: 'userId',
    as: 'user'
});
const syncModels = async (force = false) => {
    try {
        await User_1.User.sync({ force });
        await Category_1.Category.sync({ force });
        await Tag_1.Tag.sync({ force });
        await Article_1.Article.sync({ force });
        await ArticleTag_1.ArticleTag.sync({ force });
        await Post_1.Post.sync({ force });
        await PostLike_1.PostLike.sync({ force });
        await Comment_1.Comment.sync({ force });
        console.log('All models synchronized successfully');
    }
    catch (error) {
        console.error('Error synchronizing models:', error);
        throw error;
    }
};
exports.syncModels = syncModels;
//# sourceMappingURL=index.js.map