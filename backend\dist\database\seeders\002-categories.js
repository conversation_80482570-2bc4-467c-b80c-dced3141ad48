"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const up = async (queryInterface) => {
    console.log('Creating sample categories...');
    const existingCategories = await queryInterface.select(null, 'categories', {});
    if (existingCategories.length > 0) {
        console.log('Categories already exist, skipping...');
        return;
    }
    await queryInterface.bulkInsert('categories', [
        {
            id: 1,
            name: '技术',
            slug: 'tech',
            description: '技术相关文章分类',
            parent_id: null,
            sort: 1,
            created_at: new Date('2024-01-05 09:00:00'),
            updated_at: new Date('2024-01-05 09:00:00')
        },
        {
            id: 2,
            name: '生活',
            slug: 'life',
            description: '生活感悟和日常分享',
            parent_id: null,
            sort: 2,
            created_at: new Date('2024-01-05 09:01:00'),
            updated_at: new Date('2024-01-05 09:01:00')
        },
        {
            id: 3,
            name: '学习',
            slug: 'study',
            description: '学习笔记和心得体会',
            parent_id: null,
            sort: 3,
            created_at: new Date('2024-01-05 09:02:00'),
            updated_at: new Date('2024-01-05 09:02:00')
        },
        {
            id: 4,
            name: '随笔',
            slug: 'essay',
            description: '随想随写，记录点滴',
            parent_id: null,
            sort: 4,
            created_at: new Date('2024-01-05 09:03:00'),
            updated_at: new Date('2024-01-05 09:03:00')
        },
        {
            id: 5,
            name: '前端开发',
            slug: 'frontend',
            description: '前端开发技术和经验分享',
            parent_id: 1,
            sort: 1,
            created_at: new Date('2024-01-05 09:10:00'),
            updated_at: new Date('2024-01-05 09:10:00')
        },
        {
            id: 6,
            name: '后端开发',
            slug: 'backend',
            description: '后端开发技术和架构设计',
            parent_id: 1,
            sort: 2,
            created_at: new Date('2024-01-05 09:11:00'),
            updated_at: new Date('2024-01-05 09:11:00')
        },
        {
            id: 7,
            name: '数据库',
            slug: 'database',
            description: '数据库设计和优化技巧',
            parent_id: 1,
            sort: 3,
            created_at: new Date('2024-01-05 09:12:00'),
            updated_at: new Date('2024-01-05 09:12:00')
        },
        {
            id: 8,
            name: '运维部署',
            slug: 'devops',
            description: '运维和部署相关技术',
            parent_id: 1,
            sort: 4,
            created_at: new Date('2024-01-05 09:13:00'),
            updated_at: new Date('2024-01-05 09:13:00')
        },
        {
            id: 9,
            name: 'JavaScript',
            slug: 'javascript',
            description: 'JavaScript语言和相关技术',
            parent_id: 5,
            sort: 1,
            created_at: new Date('2024-01-05 09:20:00'),
            updated_at: new Date('2024-01-05 09:20:00')
        },
        {
            id: 10,
            name: 'Vue.js',
            slug: 'vuejs',
            description: 'Vue.js框架开发经验',
            parent_id: 5,
            sort: 2,
            created_at: new Date('2024-01-05 09:21:00'),
            updated_at: new Date('2024-01-05 09:21:00')
        },
        {
            id: 11,
            name: 'React',
            slug: 'react',
            description: 'React框架开发技巧',
            parent_id: 5,
            sort: 3,
            created_at: new Date('2024-01-05 09:22:00'),
            updated_at: new Date('2024-01-05 09:22:00')
        },
        {
            id: 12,
            name: 'CSS',
            slug: 'css',
            description: 'CSS样式和布局技术',
            parent_id: 5,
            sort: 4,
            created_at: new Date('2024-01-05 09:23:00'),
            updated_at: new Date('2024-01-05 09:23:00')
        },
        {
            id: 13,
            name: 'Node.js',
            slug: 'nodejs',
            description: 'Node.js服务端开发',
            parent_id: 6,
            sort: 1,
            created_at: new Date('2024-01-05 09:30:00'),
            updated_at: new Date('2024-01-05 09:30:00')
        },
        {
            id: 14,
            name: 'Python',
            slug: 'python',
            description: 'Python编程和应用开发',
            parent_id: 6,
            sort: 2,
            created_at: new Date('2024-01-05 09:31:00'),
            updated_at: new Date('2024-01-05 09:31:00')
        },
        {
            id: 15,
            name: 'Java',
            slug: 'java',
            description: 'Java企业级应用开发',
            parent_id: 6,
            sort: 3,
            created_at: new Date('2024-01-05 09:32:00'),
            updated_at: new Date('2024-01-05 09:32:00')
        },
        {
            id: 16,
            name: 'API设计',
            slug: 'api-design',
            description: 'RESTful API和GraphQL设计',
            parent_id: 6,
            sort: 4,
            created_at: new Date('2024-01-05 09:33:00'),
            updated_at: new Date('2024-01-05 09:33:00')
        },
        {
            id: 17,
            name: '旅行',
            slug: 'travel',
            description: '旅行见闻和攻略分享',
            parent_id: 2,
            sort: 1,
            created_at: new Date('2024-01-05 09:40:00'),
            updated_at: new Date('2024-01-05 09:40:00')
        },
        {
            id: 18,
            name: '美食',
            slug: 'food',
            description: '美食探索和烹饪心得',
            parent_id: 2,
            sort: 2,
            created_at: new Date('2024-01-05 09:41:00'),
            updated_at: new Date('2024-01-05 09:41:00')
        },
        {
            id: 19,
            name: '健康',
            slug: 'health',
            description: '健康生活和运动心得',
            parent_id: 2,
            sort: 3,
            created_at: new Date('2024-01-05 09:42:00'),
            updated_at: new Date('2024-01-05 09:42:00')
        },
        {
            id: 20,
            name: '读书笔记',
            slug: 'reading',
            description: '读书心得和知识总结',
            parent_id: 3,
            sort: 1,
            created_at: new Date('2024-01-05 09:50:00'),
            updated_at: new Date('2024-01-05 09:50:00')
        },
        {
            id: 21,
            name: '在线课程',
            slug: 'online-course',
            description: '在线课程学习记录',
            parent_id: 3,
            sort: 2,
            created_at: new Date('2024-01-05 09:51:00'),
            updated_at: new Date('2024-01-05 09:51:00')
        },
        {
            id: 22,
            name: '技能提升',
            slug: 'skill-improvement',
            description: '个人技能提升和成长',
            parent_id: 3,
            sort: 3,
            created_at: new Date('2024-01-05 09:52:00'),
            updated_at: new Date('2024-01-05 09:52:00')
        }
    ]);
    console.log('Sample categories seeded successfully');
};
exports.up = up;
const down = async (queryInterface) => {
    console.log('Removing sample categories...');
    await queryInterface.bulkDelete('categories', {}, {});
    console.log('Sample categories removed successfully');
};
exports.down = down;
//# sourceMappingURL=002-categories.js.map