import { sequelize } from '../config/database'
import { QueryInterface } from 'sequelize'
import * as fs from 'fs'
import * as path from 'path'

interface Migration {
  up: (queryInterface: QueryInterface) => Promise<void>
  down: (queryInterface: QueryInterface) => Promise<void>
}

/**
 * MigrationRunner 类用于执行数据库迁移脚本
 * 它会读取 migrations 目录下的所有迁移文件，并按顺序执行尚未应用的迁移
 */
export class MigrationRunner {
  private queryInterface: QueryInterface

  /**
   * 构造函数，初始化 QueryInterface 实例
   */
  constructor() {
    this.queryInterface = sequelize.getQueryInterface()
  }

  /**
   * 执行所有未运行的迁移文件
   * 该方法会扫描 migrations 目录中的 .ts 或 .js 文件，按文件名排序后依次执行
   * 并在 migrations 表中记录已执行的迁移以避免重复执行
   * @returns Promise<void> - 迁移执行完成的 Promise
   */
  async runMigrations(): Promise<void> {
    try {

      await this.createMigrationsTable()

      const migrationsDir = path.join(__dirname, '../database/migrations')
      const migrationFiles = fs.readdirSync(migrationsDir)
        .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
        .sort()

      for (const file of migrationFiles) {
        const migrationName = path.basename(file, path.extname(file))


        const hasRun = await this.hasMigrationRun(migrationName)
        if (hasRun) {
          console.log(`Migration ${migrationName} already applied, skipping...`)
          continue
        }

        console.log(`Running migration: ${migrationName}`)


        const migrationPath = path.join(migrationsDir, file)
        const migration: Migration = require(migrationPath)

        await migration.up(this.queryInterface)
        await this.recordMigration(migrationName)

        console.log(`Migration ${migrationName} completed successfully`)
      }

      console.log('All migrations completed successfully')
    } catch (error) {
      console.error('Migration failed:', error)
      throw error
    }
  }

  /**
   * 创建 migrations 表（如果尚未存在）
   * 该表用于记录已经执行过的迁移名称和执行时间
   * @returns Promise<void> - 表创建完成的 Promise
   */
  private async createMigrationsTable(): Promise<void> {
    await this.queryInterface.createTable('migrations', {
      name: {
        type: 'VARCHAR(255)',
        primaryKey: true,
        allowNull: false
      },
      executed_at: {
        type: 'TIMESTAMP',
        allowNull: false,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP')
      }
    }).catch(() => {

    })
  }

  /**
   * 检查指定名称的迁移是否已经执行过
   * 查询 migrations 表中是否存在对应的迁移记录
   * @param migrationName - 要检查的迁移名称
   * @returns Promise<boolean> - 如果迁移已执行则返回 true，否则返回 false
   */
  private async hasMigrationRun(migrationName: string): Promise<boolean> {
    try {
      const result = await this.queryInterface.select(null, 'migrations', {
        where: { name: migrationName }
      })
      return result.length > 0
    } catch (error) {
      return false
    }
  }

  /**
   * 记录一个迁移的执行信息到 migrations 表中
   * 包括迁移名称和当前时间戳
   * @param migrationName - 要记录的迁移名称
   * @returns Promise<void> - 插入操作完成的 Promise
   */
  private async recordMigration(migrationName: string): Promise<void> {
    await this.queryInterface.insert(null, 'migrations', {
      name: migrationName,
      executed_at: new Date()
    })
  }
}