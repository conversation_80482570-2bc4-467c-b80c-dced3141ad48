"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Article = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const slug_1 = require("../utils/slug");
const User_1 = require("./User");
const Tag_1 = require("./Tag");
const Category_1 = require("./Category");
class Article extends sequelize_1.Model {
    async publish() {
        this.status = 'published';
        this.publishedAt = new Date();
        await this.save();
    }
    async unpublish() {
        this.status = 'draft';
        this.publishedAt = null;
        await this.save();
    }
    generateExcerpt(length = 200) {
        if (this.excerpt)
            return this.excerpt;
        const plainText = this.content.replace(/<[^>]*>/g, '');
        return plainText.length > length
            ? plainText.substring(0, length) + '...'
            : plainText;
    }
    static async findBySlug(slug) {
        return this.findOne({
            where: { slug },
            include: [
                { model: User_1.User, as: 'author', attributes: ['id', 'username'] },
                { model: Tag_1.Tag, as: 'tags' },
                { model: Category_1.Category, as: 'category', attributes: ['id', 'name', 'slug'] }
            ]
        });
    }
    static async findPublished(options = {}) {
        const { limit = 10, offset = 0, tagId, categoryId } = options;
        const whereClause = { status: 'published' };
        const includeClause = [
            { model: User_1.User, as: 'author', attributes: ['id', 'username'] },
            { model: Tag_1.Tag, as: 'tags' },
            { model: Category_1.Category, as: 'category', attributes: ['id', 'name', 'slug'] }
        ];
        if (categoryId) {
            whereClause.categoryId = categoryId;
        }
        if (tagId) {
            includeClause[1].where = { id: tagId };
            includeClause[1].required = true;
        }
        return this.findAndCountAll({
            where: whereClause,
            include: includeClause,
            order: [['publishedAt', 'DESC']],
            limit,
            offset,
            distinct: true
        });
    }
}
exports.Article = Article;
Article.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    title: {
        type: sequelize_1.DataTypes.STRING(200),
        allowNull: false,
        validate: {
            len: [1, 200],
            notEmpty: true
        }
    },
    slug: {
        type: sequelize_1.DataTypes.STRING(200),
        allowNull: false,
        unique: true,
        validate: {
            len: [1, 200],
            is: /^[a-z0-9-]+$/i
        }
    },
    content: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: false,
        validate: {
            notEmpty: true
        }
    },
    excerpt: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true
    },
    status: {
        type: sequelize_1.DataTypes.ENUM('draft', 'published'),
        allowNull: false,
        defaultValue: 'draft'
    },
    authorId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'author_id',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    categoryId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        field: 'category_id',
        references: {
            model: 'categories',
            key: 'id'
        }
    },
    publishedAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        field: 'published_at'
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'Article',
    tableName: 'articles',
    timestamps: true,
    underscored: true,
    hooks: {
        beforeValidate: async (article) => {
            if (article.title && !article.slug) {
                let baseSlug = (0, slug_1.generateSlug)(article.title);
                let slug = baseSlug;
                let counter = 1;
                while (await Article.findOne({ where: { slug } })) {
                    slug = `${baseSlug}-${counter}`;
                    counter++;
                }
                article.slug = slug;
            }
        },
        beforeUpdate: (article) => {
            if (article.changed('status') && article.status === 'published' && !article.publishedAt) {
                article.publishedAt = new Date();
            }
        }
    }
});
//# sourceMappingURL=Article.js.map