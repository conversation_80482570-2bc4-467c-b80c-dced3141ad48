{"version": 3, "file": "DetachedBrowserPage.cjs", "sourceRoot": "", "sources": ["../../../src/browser/detached-browser/DetachedBrowserPage.ts"], "names": [], "mappings": ";;;;;AAAA,sGAA2E;AAC3E,wFAA6D;AAE7D,wFAA6D;AAK7D,+FAAoE;AAEpE,qGAA0E;AAG1E,oEAAyC;AAEzC;;GAEG;AACH,MAAqB,mBAAmB;IACvB,qBAAqB,GAAG,IAAI,kCAAqB,EAAE,CAAC;IACpD,SAAS,CAAuB;IAChC,OAAO,CAAyB;IAChC,OAAO,CAAU;IACjB,QAAQ,GAAyB,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,uCAA0B,CAAC,CAAC;IAC/E,MAAM,GAAY,KAAK,CAAC;IAExC;;;;OAIG;IACH,YAAY,OAA+B;QAC1C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,2BAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACzF,IAAI,CAAC,SAAS,GAAG,IAAI,iCAAoB,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QAChB,OAA+B,+BAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACH,IAAW,OAAO,CAAC,OAAO;QACzB,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACH,IAAW,GAAG;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACH,IAAW,GAAG,CAAC,GAAG;QACjB,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,KAAK;QACX,4DAA4D;QAC5D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACtC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YAC7B,+BAAkB,CAAC,SAAS,CAAC,IAAI,CAAC;iBAChC,IAAI,CAAC,GAAG,EAAE;gBACV,yHAAyH;gBACzH,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBAC/B,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACrD,CAAC;qBAAM,CAAC;oBACP,OAAO,EAAE,CAAC;gBACX,CAAC;YACF,CAAC,CAAC;iBACD,KAAK,CAAC,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,iBAAiB;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,iBAAiB;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,KAAK;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,MAAuB;QACtC,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,QAAsC;QACxD,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACvC,IACC,gBAAgB,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK;YAC9C,gBAAgB,CAAC,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM;YAChD,gBAAgB,CAAC,gBAAgB,KAAK,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EACnE,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,kBAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1D,CAAC;IACF,CAAC;IAED;;;;;;OAMG;IACI,IAAI,CAAC,GAAW,EAAE,OAAsB;QAC9C,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,OAAsB;QACnC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,OAAsB;QACtC,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;;OAMG;IACI,OAAO,CAAC,KAAc,EAAE,OAAsB;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,OAAwB;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;CACD;AAjLD,sCAiLC"}