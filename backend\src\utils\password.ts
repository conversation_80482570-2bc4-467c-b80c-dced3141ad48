import bcrypt from 'bcryptjs'

/**
 * 密码加密的盐值轮数
 * 数值越高安全性越高，但加密时间越长
 */
const SALT_ROUNDS = 12

/**
 * 对明文密码进行哈希加密
 * @param password - 需要加密的明文密码
 * @returns 返回加密后的密码哈希值
 */
export const hashPassword = async (password: string): Promise<string> => {
  return await bcrypt.hash(password, SALT_ROUNDS)
}

/**
 * 比较明文密码与已哈希密码是否匹配
 * @param password - 需要验证的明文密码
 * @param hashedPassword - 已存储的哈希密码
 * @returns 返回密码是否匹配的布尔值
 */
export const comparePassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  return await bcrypt.compare(password, hashedPassword)
}