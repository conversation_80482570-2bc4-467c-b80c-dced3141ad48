"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadController = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const uuid_1 = require("uuid");
const uploadConfig = {
    maxFileSize: 5 * 1024 * 1024,
    allowedMimeTypes: [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp'
    ],
    uploadDir: path_1.default.join(process.cwd(), 'uploads', 'images'),
    maxFiles: 9
};
const ensureUploadDir = () => {
    if (!fs_1.default.existsSync(uploadConfig.uploadDir)) {
        fs_1.default.mkdirSync(uploadConfig.uploadDir, { recursive: true });
    }
};
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        ensureUploadDir();
        cb(null, uploadConfig.uploadDir);
    },
    filename: (req, file, cb) => {
        const ext = path_1.default.extname(file.originalname);
        const filename = `${(0, uuid_1.v4)()}${ext}`;
        cb(null, filename);
    }
});
const fileFilter = (req, file, cb) => {
    if (uploadConfig.allowedMimeTypes.includes(file.mimetype)) {
        cb(null, true);
    }
    else {
        cb(new Error(`不支持的文件类型: ${file.mimetype}`));
    }
};
const upload = (0, multer_1.default)({
    storage,
    fileFilter,
    limits: {
        fileSize: uploadConfig.maxFileSize,
        files: uploadConfig.maxFiles
    }
});
class UploadController {
    static async handleSingleUpload(req, res) {
        try {
            if (!req.file) {
                res.status(400).json({
                    success: false,
                    message: '没有上传文件'
                });
                return;
            }
            const baseUrl = `${req.protocol}://${req.get('host')}`;
            const imageUrl = `${baseUrl}/uploads/images/${req.file.filename}`;
            res.json({
                success: true,
                message: '图片上传成功',
                data: {
                    filename: req.file.filename,
                    originalName: req.file.originalname,
                    size: req.file.size,
                    url: imageUrl
                }
            });
        }
        catch (error) {
            console.error('单个图片上传失败:', error);
            res.status(500).json({
                success: false,
                message: '图片上传失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async handleMultipleUpload(req, res) {
        try {
            if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
                res.status(400).json({
                    success: false,
                    message: '没有上传文件'
                });
                return;
            }
            const baseUrl = `${req.protocol}://${req.get('host')}`;
            const uploadedFiles = req.files.map(file => ({
                filename: file.filename,
                originalName: file.originalname,
                size: file.size,
                url: `${baseUrl}/uploads/images/${file.filename}`
            }));
            res.json({
                success: true,
                message: `成功上传 ${uploadedFiles.length} 张图片`,
                data: {
                    files: uploadedFiles,
                    count: uploadedFiles.length
                }
            });
        }
        catch (error) {
            console.error('多个图片上传失败:', error);
            res.status(500).json({
                success: false,
                message: '图片上传失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async deleteImage(req, res) {
        try {
            const { filename } = req.params;
            if (!filename) {
                res.status(400).json({
                    success: false,
                    message: '缺少文件名参数'
                });
                return;
            }
            const safeFilename = path_1.default.basename(filename);
            if (safeFilename !== filename) {
                res.status(400).json({
                    success: false,
                    message: '无效的文件名'
                });
                return;
            }
            const filePath = path_1.default.join(uploadConfig.uploadDir, safeFilename);
            if (!fs_1.default.existsSync(filePath)) {
                res.status(404).json({
                    success: false,
                    message: '文件不存在'
                });
                return;
            }
            fs_1.default.unlinkSync(filePath);
            res.json({
                success: true,
                message: '图片删除成功'
            });
        }
        catch (error) {
            console.error('删除图片失败:', error);
            res.status(500).json({
                success: false,
                message: '删除图片失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async getImageInfo(req, res) {
        try {
            const { filename } = req.params;
            if (!filename) {
                res.status(400).json({
                    success: false,
                    message: '缺少文件名参数'
                });
                return;
            }
            const safeFilename = path_1.default.basename(filename);
            if (safeFilename !== filename) {
                res.status(400).json({
                    success: false,
                    message: '无效的文件名'
                });
                return;
            }
            const filePath = path_1.default.join(uploadConfig.uploadDir, safeFilename);
            if (!fs_1.default.existsSync(filePath)) {
                res.status(404).json({
                    success: false,
                    message: '文件不存在'
                });
                return;
            }
            const stats = fs_1.default.statSync(filePath);
            const baseUrl = `${req.protocol}://${req.get('host')}`;
            res.json({
                success: true,
                data: {
                    filename: safeFilename,
                    size: stats.size,
                    createdAt: stats.birthtime,
                    modifiedAt: stats.mtime,
                    url: `${baseUrl}/uploads/images/${safeFilename}`
                }
            });
        }
        catch (error) {
            console.error('获取图片信息失败:', error);
            res.status(500).json({
                success: false,
                message: '获取图片信息失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static getUploadConfig(req, res) {
        res.json({
            success: true,
            data: {
                maxFileSize: uploadConfig.maxFileSize,
                maxFiles: uploadConfig.maxFiles,
                allowedTypes: uploadConfig.allowedMimeTypes,
                maxFileSizeMB: Math.round(uploadConfig.maxFileSize / (1024 * 1024))
            }
        });
    }
}
exports.UploadController = UploadController;
UploadController.uploadSingle = upload.single('image');
UploadController.uploadMultiple = upload.array('images', uploadConfig.maxFiles);
//# sourceMappingURL=upload.js.map