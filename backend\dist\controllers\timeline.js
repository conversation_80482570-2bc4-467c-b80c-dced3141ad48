"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimelineController = void 0;
const models_1 = require("../models");
const sequelize_1 = require("sequelize");
class TimelineController {
    static async getPersonalTimeline(req, res) {
        try {
            const userId = req.user.id;
            const { page = 1, limit = 10 } = req.query;
            const offset = (Number(page) - 1) * Number(limit);
            const whereClause = {
                authorId: userId
            };
            const { rows: posts, count } = await models_1.Post.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: models_1.User,
                        as: 'author',
                        attributes: ['id', 'username', 'email']
                    }
                ],
                order: [['createdAt', 'DESC']],
                limit: Number(limit),
                offset,
                distinct: true
            });
            const postsWithInfo = await Promise.all(posts.map(async (post) => {
                const fullInfo = await post.getFullInfo(userId);
                return fullInfo;
            }));
            res.json({
                success: true,
                data: {
                    posts: postsWithInfo,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit),
                        total: count,
                        totalPages: Math.ceil(count / Number(limit))
                    }
                }
            });
        }
        catch (error) {
            console.error('获取个人时间线失败:', error);
            res.status(500).json({
                success: false,
                message: '获取个人时间线失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async getPublicTimeline(req, res) {
        try {
            const { page = 1, limit = 10, search } = req.query;
            const authReq = req;
            const userId = authReq.user?.id;
            const offset = (Number(page) - 1) * Number(limit);
            const whereClause = {
                visibility: 'public'
            };
            if (search) {
                whereClause.content = {
                    [sequelize_1.Op.like]: `%${search}%`
                };
            }
            const { rows: posts, count } = await models_1.Post.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: models_1.User,
                        as: 'author',
                        attributes: ['id', 'username', 'email']
                    }
                ],
                order: [['createdAt', 'DESC']],
                limit: Number(limit),
                offset,
                distinct: true
            });
            const postsWithInfo = await Promise.all(posts.map(async (post) => {
                const fullInfo = await post.getFullInfo(userId);
                return fullInfo;
            }));
            res.json({
                success: true,
                data: {
                    posts: postsWithInfo,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit),
                        total: count,
                        totalPages: Math.ceil(count / Number(limit))
                    }
                }
            });
        }
        catch (error) {
            console.error('获取公开时间线失败:', error);
            res.status(500).json({
                success: false,
                message: '获取公开时间线失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async getUserTimeline(req, res) {
        try {
            const { userId: targetUserId } = req.params;
            const { page = 1, limit = 10 } = req.query;
            const authReq = req;
            const currentUserId = authReq.user?.id;
            const offset = (Number(page) - 1) * Number(limit);
            const targetUser = await models_1.User.findByPk(Number(targetUserId));
            if (!targetUser) {
                res.status(404).json({
                    success: false,
                    message: '用户不存在'
                });
                return;
            }
            let visibilityConditions = [{ visibility: 'public' }];
            if (currentUserId) {
                if (currentUserId === Number(targetUserId)) {
                    visibilityConditions = [
                        { visibility: 'public' },
                        { visibility: 'private' }
                    ];
                }
            }
            const whereClause = {
                authorId: Number(targetUserId),
                [sequelize_1.Op.or]: visibilityConditions
            };
            const { rows: posts, count } = await models_1.Post.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: models_1.User,
                        as: 'author',
                        attributes: ['id', 'username', 'email']
                    }
                ],
                order: [['createdAt', 'DESC']],
                limit: Number(limit),
                offset,
                distinct: true
            });
            const postsWithInfo = await Promise.all(posts.map(async (post) => {
                const fullInfo = await post.getFullInfo(currentUserId);
                return fullInfo;
            }));
            res.json({
                success: true,
                data: {
                    user: {
                        id: targetUser.id,
                        username: targetUser.username,
                        email: targetUser.email
                    },
                    posts: postsWithInfo,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit),
                        total: count,
                        totalPages: Math.ceil(count / Number(limit))
                    }
                }
            });
        }
        catch (error) {
            console.error('获取用户时间线失败:', error);
            res.status(500).json({
                success: false,
                message: '获取用户时间线失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async getTrendingPosts(req, res) {
        try {
            const { page = 1, limit = 10, timeRange = '7d' } = req.query;
            const authReq = req;
            const userId = authReq.user?.id;
            const offset = (Number(page) - 1) * Number(limit);
            const now = new Date();
            let startDate;
            switch (timeRange) {
                case '1d':
                    startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    break;
                case '3d':
                    startDate = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
                    break;
                case '7d':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case '30d':
                    startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    break;
                default:
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            }
            const whereClause = {
                visibility: 'public',
                createdAt: {
                    [sequelize_1.Op.gte]: startDate
                }
            };
            const { rows: posts, count } = await models_1.Post.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: models_1.User,
                        as: 'author',
                        attributes: ['id', 'username', 'email']
                    }
                ],
                order: [['createdAt', 'DESC']],
                limit: Number(limit),
                offset,
                distinct: true
            });
            const postsWithInfo = await Promise.all(posts.map(async (post) => {
                const fullInfo = await post.getFullInfo(userId);
                return fullInfo;
            }));
            postsWithInfo.sort((a, b) => {
                const scoreA = (a.likeCount || 0) * 2 + (a.commentCount || 0);
                const scoreB = (b.likeCount || 0) * 2 + (b.commentCount || 0);
                return scoreB - scoreA;
            });
            res.json({
                success: true,
                data: {
                    posts: postsWithInfo,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit),
                        total: count,
                        totalPages: Math.ceil(count / Number(limit))
                    },
                    timeRange
                }
            });
        }
        catch (error) {
            console.error('获取热门动态失败:', error);
            res.status(500).json({
                success: false,
                message: '获取热门动态失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
}
exports.TimelineController = TimelineController;
//# sourceMappingURL=timeline.js.map