"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrationRunner = void 0;
const database_1 = require("../config/database");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class MigrationRunner {
    constructor() {
        this.queryInterface = database_1.sequelize.getQueryInterface();
    }
    async runMigrations() {
        try {
            await this.createMigrationsTable();
            const migrationsDir = path.join(__dirname, '../database/migrations');
            const migrationFiles = fs.readdirSync(migrationsDir)
                .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
                .sort();
            for (const file of migrationFiles) {
                const migrationName = path.basename(file, path.extname(file));
                const hasRun = await this.hasMigrationRun(migrationName);
                if (hasRun) {
                    console.log(`Migration ${migrationName} already applied, skipping...`);
                    continue;
                }
                console.log(`Running migration: ${migrationName}`);
                const migrationPath = path.join(migrationsDir, file);
                const migration = require(migrationPath);
                await migration.up(this.queryInterface);
                await this.recordMigration(migrationName);
                console.log(`Migration ${migrationName} completed successfully`);
            }
            console.log('All migrations completed successfully');
        }
        catch (error) {
            console.error('Migration failed:', error);
            throw error;
        }
    }
    async createMigrationsTable() {
        await this.queryInterface.createTable('migrations', {
            name: {
                type: 'VARCHAR(255)',
                primaryKey: true,
                allowNull: false
            },
            executed_at: {
                type: 'TIMESTAMP',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP')
            }
        }).catch(() => {
        });
    }
    async hasMigrationRun(migrationName) {
        try {
            const result = await this.queryInterface.select(null, 'migrations', {
                where: { name: migrationName }
            });
            return result.length > 0;
        }
        catch (error) {
            return false;
        }
    }
    async recordMigration(migrationName) {
        await this.queryInterface.insert(null, 'migrations', {
            name: migrationName,
            executed_at: new Date()
        });
    }
}
exports.MigrationRunner = MigrationRunner;
//# sourceMappingURL=migrationRunner.js.map