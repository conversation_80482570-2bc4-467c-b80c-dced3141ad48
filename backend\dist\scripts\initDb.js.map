{"version": 3, "file": "initDb.js", "sourceRoot": "", "sources": ["../../src/scripts/initDb.ts"], "names": [], "mappings": ";;;;;;AAEA,8DAA0D;AAC1D,oDAAgD;AAChD,uDAAqD;AACrD,oDAA2B;AAG3B,gBAAM,CAAC,MAAM,EAAE,CAAA;AAMf,MAAM,YAAY;IAIhB;QACE,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAA;QAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,uBAAU,EAAE,CAAA;IACpC,CAAC;IAMD,KAAK,CAAC,GAAG,CAAC,UAIN,EAAE;QACJ,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,QAAQ,GAAG,KAAK,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,OAAO,CAAA;QAExE,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAA;QACrD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;QAE5B,IAAI,CAAC;YAEH,MAAM,cAAc,GAAG,yBAAY,CAAC,iBAAiB,EAAE,CAAA;YACvD,OAAO,CAAC,GAAG,CAAC,gBAAgB,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAA;YACtD,OAAO,CAAC,GAAG,CAAC,YAAY,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC,CAAA;YACrE,OAAO,CAAC,GAAG,CAAC,YAAY,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAA;YAClD,OAAO,CAAC,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;YACrD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YAGf,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAA;gBAC5D,MAAM,SAAS,GAAG,MAAM,yBAAY,CAAC,yBAAyB,EAAE,CAAA;gBAEhE,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAA;oBAC7C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBACjB,CAAC;gBACD,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAA;gBAChC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACjB,CAAC;YAGD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAA;YACxD,MAAM,WAAW,GAAG,MAAM,yBAAY,CAAC,cAAc,EAAE,CAAA;YAEvD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAA;gBAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACjB,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAChD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YAGf,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAA;gBAC1D,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAA;gBAC1C,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAA;gBACtC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACjB,CAAC;YAGD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAA;gBAC9C,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;gBACvC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAA;gBACrC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACjB,CAAC;YAGD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;YAC5B,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAA;YACjE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACf,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;YAC1B,OAAO,CAAC,GAAG,CAAC,kBAAkB,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAA;YACxD,OAAO,CAAC,GAAG,CAAC,oBAAoB,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAA;YACrE,OAAO,CAAC,GAAG,CAAC,mBAAmB,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAA;YACvE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACf,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAA;QAErD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;YACjB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;YAC9B,OAAO,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAA;YAClD,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAA;YACtC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACjB,CAAC;gBAAS,CAAC;YAET,MAAM,yBAAY,CAAC,eAAe,EAAE,CAAA;QACtC,CAAC;IACH,CAAC;IAKD,QAAQ;QACN,OAAO,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;CAyBf,CAAC,CAAA;IACA,CAAC;CACF;AAKD,SAAS,SAAS,CAAC,IAAc;IAC/B,OAAO;QACL,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;QACtC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QAC5C,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;KACrD,CAAA;AACH,CAAC;AAKD,KAAK,UAAU,IAAI;IACjB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IAClC,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,CAAA;IAE/B,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE,CAAA;IAGjC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QACjB,MAAM,CAAC,QAAQ,EAAE,CAAA;QACjB,OAAM;IACR,CAAC;IAGD,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;QAC5C,OAAO,CAAC,KAAK,CAAC,iEAAiE,CAAC,CAAA;QAChF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC;IAGD,MAAM,MAAM,CAAC,GAAG,CAAC;QACf,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,WAAW,EAAE,OAAO,CAAC,WAAW;KACjC,CAAC,CAAA;AACJ,CAAC;AAGD,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;IACnD,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAA;IACpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC,CAAC,CAAA;AAEF,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;IACxC,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAA;IAC3C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC,CAAC,CAAA;AAGF,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACrB,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC,CAAC,CAAA;AACJ,CAAC"}