"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const category_1 = require("../controllers/category");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const router = (0, express_1.Router)();
router.get('/', category_1.getCategories);
router.get('/stats', category_1.getCategoryStats);
router.get('/:id', category_1.getCategory);
router.get('/:id/articles', category_1.getCategoryArticles);
router.post('/', auth_1.authenticateToken, validation_1.validateCategory, category_1.createCategory);
router.put('/:id', auth_1.authenticateToken, validation_1.validateCategoryUpdate, category_1.updateCategory);
router.delete('/:id', auth_1.authenticateToken, category_1.deleteCategory);
router.delete('/batch', auth_1.authenticateToken, category_1.deleteCategories);
exports.default = router;
//# sourceMappingURL=category.js.map