import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 执行数据库迁移，创建分类表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 无返回值的异步函数
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 创建分类表，支持层次化分类结构
  await queryInterface.createTable('categories', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    slug: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    parent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'categories',
        key: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    },
    sort: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  })

  // 为常用查询字段创建索引以提高查询性能
  // 使用 try-catch 来避免重复创建索引的错误
  try {
    await queryInterface.addIndex('categories', ['slug'], {
      name: 'categories_slug'
    })
  } catch {
    // 索引已存在，忽略错误
    console.log('Index categories_slug already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('categories', ['parent_id'], {
      name: 'categories_parent_id'
    })
  } catch {
    console.log('Index categories_parent_id already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('categories', ['sort'], {
      name: 'categories_sort'
    })
  } catch {
    console.log('Index categories_sort already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('categories', ['name'], {
      name: 'categories_name'
    })
  } catch {
    console.log('Index categories_name already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('categories', ['created_at'], {
      name: 'categories_created_at'
    })
  } catch {
    console.log('Index categories_created_at already exists, skipping...')
  }
}

/**
 * 回滚数据库迁移，删除分类表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 无返回值的异步函数
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('categories')
}
