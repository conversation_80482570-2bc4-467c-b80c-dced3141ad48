"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('users', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        username: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            unique: true
        },
        email: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            unique: true
        },
        password_hash: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: false
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    try {
        await queryInterface.addIndex('users', ['username']);
    }
    catch {
        console.log('Index users_username already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('users', ['email']);
    }
    catch {
        console.log('Index users_email already exists, skipping...');
    }
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('users');
};
exports.down = down;
//# sourceMappingURL=001-create-users.js.map