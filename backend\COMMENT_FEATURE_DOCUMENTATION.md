# 评论功能实现文档

## 概述

本文档描述了博客系统评论功能的完整实现，包括数据模型、API接口、安全措施和测试用例。

## 功能特性

### 核心功能
- ✅ 评论的增删改查操作
- ✅ 嵌套回复功能（支持多层回复）
- ✅ 评论状态管理（待审核、已批准、已拒绝）
- ✅ 分页查询支持
- ✅ 用户权限控制
- ✅ 内容安全防护

### 安全特性
- ✅ XSS防护（移除恶意脚本标签）
- ✅ 评论频率限制（防刷评论）
- ✅ 内容长度验证
- ✅ 敏感词过滤
- ✅ 用户权限验证
- ✅ IP地址记录

## 数据模型

### Comment 模型

```typescript
interface CommentAttributes {
  id: number                    // 评论ID
  content: string              // 评论内容（最大2000字符）
  status: 'pending' | 'approved' | 'rejected'  // 评论状态
  articleId: number            // 关联的文章ID
  authorId: number             // 评论作者ID
  parentId?: number            // 父评论ID（用于回复功能）
  createdAt: Date              // 创建时间
  updatedAt: Date              // 更新时间
}
```

### 关系设计
- **Comment ↔ User**: 多对一关系（一个用户可以发表多个评论）
- **Comment ↔ Article**: 多对一关系（一篇文章可以有多个评论）
- **Comment ↔ Comment**: 自关联一对多关系（支持回复功能）

## API 接口

### 评论管理接口

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | `/api/comments` | 获取评论列表 | 否 |
| GET | `/api/comments/:id` | 获取单个评论 | 否 |
| POST | `/api/comments` | 创建新评论 | 是 |
| PUT | `/api/comments/:id` | 更新评论内容 | 是 |
| DELETE | `/api/comments/:id` | 删除评论 | 是 |
| PUT | `/api/comments/:id/status` | 更新评论状态（管理员） | 是 |

### 特殊接口

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | `/api/articles/:articleId/comments` | 获取文章评论 | 否 |
| GET | `/api/comments/user/me` | 获取当前用户评论 | 是 |
| GET | `/api/comments/pending` | 获取待审核评论（管理员） | 是 |

### 请求示例

#### 创建评论
```json
POST /api/comments
Authorization: Bearer <token>

{
  "content": "这是一条评论",
  "articleId": 1,
  "parentId": null  // 可选，用于回复
}
```

#### 更新评论
```json
PUT /api/comments/1
Authorization: Bearer <token>

{
  "content": "更新后的评论内容"
}
```

#### 更新评论状态
```json
PUT /api/comments/1/status
Authorization: Bearer <token>

{
  "status": "approved"
}
```

## 安全措施

### 1. 内容安全
- **XSS防护**: 自动移除 `<script>` 标签
- **内容验证**: 限制评论长度（1-2000字符）
- **敏感词过滤**: 检查并拒绝包含敏感词的评论

### 2. 访问控制
- **认证要求**: 发表、编辑、删除评论需要登录
- **权限检查**: 用户只能编辑/删除自己的评论
- **状态管理**: 只有管理员可以审核评论状态

### 3. 防滥用
- **频率限制**: 30秒内只能发表一条评论
- **IP记录**: 记录评论者IP地址用于安全审计

## 数据库设计

### 评论表结构
```sql
CREATE TABLE comments (
  id INT PRIMARY KEY AUTO_INCREMENT,
  content TEXT NOT NULL,
  status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
  article_id INT NOT NULL,
  author_id INT NOT NULL,
  parent_id INT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
  FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
  
  INDEX idx_comments_article_id (article_id),
  INDEX idx_comments_author_id (author_id),
  INDEX idx_comments_parent_id (parent_id),
  INDEX idx_comments_status (status),
  INDEX idx_comments_created_at (created_at),
  INDEX idx_comments_article_status (article_id, status),
  INDEX idx_comments_article_parent (article_id, parent_id)
);
```

## 文件结构

```
backend/src/
├── models/
│   └── Comment.ts                    # 评论模型定义
├── controllers/
│   └── comment.ts                    # 评论控制器
├── routes/
│   └── comment.ts                    # 评论路由
├── middleware/
│   ├── validation.ts                 # 验证中间件（新增评论验证）
│   └── commentSecurity.ts            # 评论安全中间件
├── database/migrations/
│   └── 007-create-comments.ts        # 评论表迁移
└── tests/
    ├── models/
    │   └── Comment.test.ts            # 评论模型测试
    └── controllers/
        └── comment.test.ts            # 评论控制器测试
```

## 使用示例

### 前端集成示例

```javascript
// 获取文章评论
const getArticleComments = async (articleId, page = 1) => {
  const response = await fetch(`/api/articles/${articleId}/comments?page=${page}`)
  return response.json()
}

// 发表评论
const createComment = async (content, articleId, parentId = null) => {
  const response = await fetch('/api/comments', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ content, articleId, parentId })
  })
  return response.json()
}

// 回复评论
const replyToComment = async (content, articleId, parentId) => {
  return createComment(content, articleId, parentId)
}
```

## 测试覆盖

### 模型测试
- ✅ 评论创建和验证
- ✅ 回复功能测试
- ✅ 状态管理方法
- ✅ 静态查询方法
- ✅ 安全钩子函数

### 控制器测试
- ✅ CRUD操作测试
- ✅ 权限验证测试
- ✅ 输入验证测试
- ✅ 错误处理测试

## 性能优化

### 数据库优化
- **索引策略**: 为常用查询字段添加索引
- **分页查询**: 支持分页避免大量数据加载
- **关联查询**: 使用 include 减少查询次数

### 缓存策略
- **内存缓存**: 频率限制使用内存缓存
- **查询优化**: 只查询已批准的评论用于公开显示

## 部署注意事项

1. **环境变量**: 确保 JWT_SECRET 已正确配置
2. **数据库迁移**: 运行评论表迁移脚本
3. **权限配置**: 配置管理员角色用于评论审核
4. **监控**: 监控评论创建频率和内容质量

## 后续扩展

### 可能的功能扩展
- 评论点赞/点踩功能
- 评论举报功能
- 富文本评论支持
- 评论通知系统
- 评论搜索功能
- 评论导出功能

### 性能优化
- Redis 缓存热门评论
- 评论数据分表策略
- CDN 加速评论加载
- 异步评论处理队列
