import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: number;
        username: string;
    };
}
export declare const getComments: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getComment: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const createComment: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const updateComment: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const deleteComment: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const updateCommentStatus: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getArticleComments: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getUserComments: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getPendingComments: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getCommentStats: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export {};
//# sourceMappingURL=comment.d.ts.map