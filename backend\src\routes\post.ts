import { Router } from 'express'
import { PostController } from '../controllers/post'
import { authenticateToken } from '../middleware/auth'
import { validatePostCreation, validatePostUpdate } from '../middleware/validation'
import {
    createPostSecurity,
    postSecurityMiddleware,
    checkPostAccess,
    checkPostOwnership
} from '../middleware/postSecurity'

const router = Router()

/**
 * 说说相关路由配置
 * 所有路由都以 /api/posts 为前缀
 */

// 公开路由（不需要认证）
/**
 * GET /api/posts
 * 获取说说列表
 * 查询参数：
 * - page: 页码（默认1）
 * - limit: 每页数量（默认10）
 * - visibility: 可见性过滤（public/private）
 * - authorId: 作者ID过滤
 * - search: 搜索关键词
 */
router.get('/', PostController.getPosts)

/**
 * GET /api/posts/:id
 * 获取单个说说详情
 * 参数：
 * - id: 说说ID
 */
router.get('/:id', checkPostAccess, PostController.getPost)

/**
 * GET /api/posts/:id/likes
 * 获取说说的点赞用户列表
 * 参数：
 * - id: 说说ID
 * 查询参数：
 * - page: 页码（默认1）
 * - limit: 每页数量（默认20）
 */
router.get('/:id/likes', PostController.getLikes)

// 需要认证的路由
/**
 * POST /api/posts
 * 创建新说说
 * 需要认证
 * 请求体：
 * - content: 说说内容（必填）
 * - images: 图片URL数组（可选）
 * - visibility: 可见性（public/private，默认public）
 * - location: 位置信息（可选）
 */
router.post('/', authenticateToken, validatePostCreation, createPostSecurity, PostController.createPost)

/**
 * PUT /api/posts/:id
 * 更新说说
 * 需要认证，只能更新自己的说说
 * 参数：
 * - id: 说说ID
 * 请求体：
 * - content: 说说内容（可选）
 * - images: 图片URL数组（可选）
 * - visibility: 可见性（可选）
 * - location: 位置信息（可选）
 */
router.put('/:id', authenticateToken, checkPostOwnership, validatePostUpdate, postSecurityMiddleware, PostController.updatePost)

/**
 * DELETE /api/posts/:id
 * 删除说说
 * 需要认证，只能删除自己的说说
 * 参数：
 * - id: 说说ID
 */
router.delete('/:id', authenticateToken, checkPostOwnership, PostController.deletePost)

/**
 * POST /api/posts/:id/like
 * 切换点赞状态（点赞/取消点赞）
 * 需要认证
 * 参数：
 * - id: 说说ID
 */
router.post('/:id/like', authenticateToken, checkPostAccess, PostController.toggleLike)

export default router
