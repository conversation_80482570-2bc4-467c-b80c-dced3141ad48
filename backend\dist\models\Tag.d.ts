import { Model, Optional } from 'sequelize';
export interface TagAttributes {
    id: number;
    name: string;
    slug: string;
    createdAt: Date;
}
export interface TagCreationAttributes extends Optional<TagAttributes, 'id' | 'slug' | 'createdAt'> {
}
export declare class Tag extends Model<TagAttributes, TagCreationAttributes> implements TagAttributes {
    id: number;
    name: string;
    slug: string;
    createdAt: Date;
    static findBySlug(slug: string): Promise<Tag | null>;
    static findByName(name: string): Promise<Tag | null>;
    static findOrCreateByName(name: string): Promise<[Tag, boolean]>;
}
//# sourceMappingURL=Tag.d.ts.map