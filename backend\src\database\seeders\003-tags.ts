import { QueryInterface } from 'sequelize'
import { generateSlug } from '../../utils/slug'

/**
 * 创建示例标签种子数据
 * 为博客系统创建一些常用的技术标签
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('Creating sample tags...')

  // 定义示例标签
  const sampleTags = [
    'JavaScript',
    'TypeScript', 
    'React',
    'Vue.js',
    'Node.js',
    'Express',
    'MySQL',
    'MongoDB',
    'Docker',
    'AWS',
    '前端开发',
    '后端开发',
    '全栈开发',
    'Web开发',
    '数据库',
    '云计算',
    '微服务',
    'API设计',
    '性能优化',
    '代码规范',
    '项目管理',
    '敏捷开发',
    '测试驱动开发',
    '持续集成',
    '开源项目',
    'CSS',
    'HTML',
    'Webpack',
    'Vite',
    'Git'
  ]

  // 检查已存在的标签
  const existingTags = await queryInterface.select(null, 'tags', {})
  const existingTagNames = existingTags.map((tag: any) => tag.name)

  // 过滤出需要创建的新标签
  const newTags = sampleTags.filter(tagName => !existingTagNames.includes(tagName))

  if (newTags.length === 0) {
    console.log('All sample tags already exist, skipping...')
    return
  }

  // 准备插入数据
  const tagsToInsert = newTags.map((tagName, index) => ({
    name: tagName,
    slug: generateSlug(tagName),
    created_at: new Date(`2024-01-06 ${String(9 + Math.floor(index / 10)).padStart(2, '0')}:${String((index % 10) * 6).padStart(2, '0')}:00`)
  }))

  // 批量插入标签
  await queryInterface.bulkInsert('tags', tagsToInsert)

  console.log(`Created ${newTags.length} sample tags:`)
  newTags.forEach(tag => console.log(`  • ${tag}`))
}

/**
 * 回滚种子数据
 * 删除创建的示例标签
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('Removing sample tags...')

  const sampleTagNames = [
    'JavaScript',
    'TypeScript', 
    'React',
    'Vue.js',
    'Node.js',
    'Express',
    'MySQL',
    'MongoDB',
    'Docker',
    'AWS',
    '前端开发',
    '后端开发',
    '全栈开发',
    'Web开发',
    '数据库',
    '云计算',
    '微服务',
    'API设计',
    '性能优化',
    '代码规范',
    '项目管理',
    '敏捷开发',
    '测试驱动开发',
    '持续集成',
    '开源项目',
    'CSS',
    'HTML',
    'Webpack',
    'Vite',
    'Git'
  ]
  
  await queryInterface.bulkDelete('tags', {
    name: sampleTagNames
  }, {})
  
  console.log('Sample tags removed successfully!')
}
