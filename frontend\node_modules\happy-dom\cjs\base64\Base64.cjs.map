{"version": 3, "file": "Base64.cjs", "sourceRoot": "", "sources": ["../../src/base64/Base64.ts"], "names": [], "mappings": ";;;;;AAAA,mFAAwD;AACxD,mGAAwE;AAExE,MAAM,YAAY,GAAG,mEAAmE,CAAC;AAEzF;;GAEG;AACH,MAAqB,MAAM;IAC1B;;;;;;OAMG;IACI,MAAM,CAAC,IAAI,CAAC,IAAa;QAC/B,MAAM,GAAG,GAAY,IAAK,CAAC,QAAQ,EAAE,CAAC;QACtC,IAAI,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,yBAAY,CACrB,iHAAiH,EACjH,iCAAoB,CAAC,qBAAqB,CAC1C,CAAC;QACH,CAAC;QAED,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACX,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,CAAC;QACN,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACX,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;oBACpB,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;oBACxB,CAAC,IAAI,CAAC,CAAC;gBACR,CAAC;qBAAM,CAAC;oBACP,CAAC,GAAG,CAAC,CAAC;gBACP,CAAC;gBACD,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBACjC,CAAC,IAAI,CAAC,CAAC;YACR,CAAC;YACD,CAAC,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACrD,CAAC,IAAI,CAAC,CAAC;YACP,CAAC,IAAI,CAAC,CAAC;QACR,CAAC;QACD,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,IAAI,CAAC,IAAa;QAC/B,MAAM,GAAG,GAAY,IAAK,CAAC,QAAQ,EAAE,CAAC;QAEtC,IAAI,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,yBAAY,CACrB,iHAAiH,EACjH,iCAAoB,CAAC,qBAAqB,CAC1C,CAAC;QACH,CAAC;QAED,IAAI,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACvD,MAAM,IAAI,yBAAY,CACrB,0FAA0F,EAC1F,iCAAoB,CAAC,qBAAqB,CAC1C,CAAC;QACH,CAAC;QAED,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACX,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC;QACN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,IAAI,CAAC,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;gBACnD,SAAS;YACV,CAAC;YACD,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YACxB,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;gBACnB,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;oBACd,CAAC,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBAC7B,CAAC;gBACD,CAAC,IAAI,EAAE,CAAC;gBACR,CAAC,IAAI,CAAC,CAAC;YACR,CAAC;QACF,CAAC;QACD,OAAO,CAAC,CAAC;IACV,CAAC;CACD;AAxFD,yBAwFC"}