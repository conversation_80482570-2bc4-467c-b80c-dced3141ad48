import{d as h,y as f,r as m,c,o as r,a as s,e as t,t as _,b as v,_ as k}from"./index-1ed5a19c.js";const b={class:"error-page"},w={class:"error-page__content"},C={class:"error-page__icon"},E={key:0},R={key:1},A={key:2},B={key:3},P={class:"error-page__title"},N={class:"error-page__message"},V={class:"error-page__actions"},x=["disabled"],D=h({__name:"ErrorPage",props:{type:{default:"generic"},title:{default:""},message:{default:""},retryAction:{}},setup(i){const e=i,n=f(),o=m(!1),l=c(()=>!!e.retryAction),u=c(()=>{if(e.title)return e.title;switch(e.type){case"404":return"页面未找到";case"500":return"服务器错误";case"network":return"网络连接错误";default:return"出现错误"}}),p=c(()=>{if(e.message)return e.message;switch(e.type){case"404":return"抱歉，您访问的页面不存在或已被删除。";case"500":return"服务器遇到了一些问题，请稍后再试。";case"network":return"网络连接失败，请检查您的网络连接后重试。";default:return"系统遇到了一些问题，请稍后再试。"}}),d=async()=>{if(!(!e.retryAction||o.value)){o.value=!0;try{await e.retryAction()}catch(a){console.error("Retry failed:",a)}finally{o.value=!1}}},y=()=>{n.push("/")},g=()=>{window.history.length>1?n.go(-1):n.push("/")};return(a,H)=>(r(),s("div",b,[t("div",w,[t("div",C,[a.type==="404"?(r(),s("span",E,"🔍")):a.type==="500"?(r(),s("span",R,"⚠️")):a.type==="network"?(r(),s("span",A,"🌐")):(r(),s("span",B,"❌"))]),t("h1",P,_(u.value),1),t("p",N,_(p.value),1),t("div",V,[l.value?(r(),s("button",{key:0,onClick:d,disabled:o.value,class:"error-page__button error-page__button--primary"},_(o.value?"重试中...":"重试"),9,x)):v("",!0),t("button",{onClick:y,class:"error-page__button error-page__button--secondary"}," 返回首页 "),t("button",{onClick:g,class:"error-page__button error-page__button--secondary"}," 返回上页 ")])])]))}});const S=k(D,[["__scopeId","data-v-52c3dc18"]]);export{S as E};
