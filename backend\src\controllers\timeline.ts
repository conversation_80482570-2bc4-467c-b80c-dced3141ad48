import { Request, Response } from 'express'
import { Post, User } from '../models'
import { Op } from 'sequelize'

/**
 * 扩展Request接口，添加用户信息
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
    email: string
  }
}

/**
 * 时间线控制器类
 * 处理时间线相关的所有HTTP请求
 */
export class TimelineController {
  /**
   * 获取个人时间线（用户自己的动态）
   * 显示用户自己的所有动态（包括私密动态）
   */
  public static async getPersonalTimeline(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id
      const { page = 1, limit = 10 } = req.query

      const offset = (Number(page) - 1) * Number(limit)

      // 查询条件：只显示用户自己的动态
      const whereClause = {
        authorId: userId
      }

      const { rows: posts, count } = await Post.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['id', 'username', 'email']
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: Number(limit),
        offset,
        distinct: true
      })

      // 获取每个说说的完整信息
      const postsWithInfo = await Promise.all(
        posts.map(async (post) => {
          const fullInfo = await post.getFullInfo(userId)
          return fullInfo
        })
      )

      res.json({
        success: true,
        data: {
          posts: postsWithInfo,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: count,
            totalPages: Math.ceil(count / Number(limit))
          }
        }
      })
    } catch (error) {
      console.error('获取个人时间线失败:', error)
      res.status(500).json({
        success: false,
        message: '获取个人时间线失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 获取公开时间线
   * 显示所有公开的动态
   */
  public static async getPublicTimeline(req: Request, res: Response): Promise<void> {
    try {
      const { page = 1, limit = 10, search } = req.query
      const authReq = req as AuthenticatedRequest
      const userId = authReq.user?.id

      const offset = (Number(page) - 1) * Number(limit)

      const whereClause: any = {
        visibility: 'public'
      }

      // 搜索功能
      if (search) {
        whereClause.content = {
          [Op.like]: `%${search}%`
        }
      }

      const { rows: posts, count } = await Post.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['id', 'username', 'email']
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: Number(limit),
        offset,
        distinct: true
      })

      // 获取每个说说的完整信息
      const postsWithInfo = await Promise.all(
        posts.map(async (post) => {
          const fullInfo = await post.getFullInfo(userId)
          return fullInfo
        })
      )

      res.json({
        success: true,
        data: {
          posts: postsWithInfo,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: count,
            totalPages: Math.ceil(count / Number(limit))
          }
        }
      })
    } catch (error) {
      console.error('获取公开时间线失败:', error)
      res.status(500).json({
        success: false,
        message: '获取公开时间线失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 获取用户个人动态页面
   * 显示指定用户的动态（根据权限过滤）
   */
  public static async getUserTimeline(req: Request, res: Response): Promise<void> {
    try {
      const { userId: targetUserId } = req.params
      const { page = 1, limit = 10 } = req.query
      const authReq = req as AuthenticatedRequest
      const currentUserId = authReq.user?.id

      const offset = (Number(page) - 1) * Number(limit)

      // 检查目标用户是否存在
      const targetUser = await User.findByPk(Number(targetUserId))
      if (!targetUser) {
        res.status(404).json({
          success: false,
          message: '用户不存在'
        })
        return
      }

      // 确定可见性权限
      let visibilityConditions: any[] = [{ visibility: 'public' }]

      if (currentUserId) {
        // 如果是自己，可以看到所有动态（包括私密动态）
        if (currentUserId === Number(targetUserId)) {
          visibilityConditions = [
            { visibility: 'public' },
            { visibility: 'private' }
          ]
        }
        // 如果不是自己，只能看到公开动态
      }

      const whereClause = {
        authorId: Number(targetUserId),
        [Op.or]: visibilityConditions
      }

      const { rows: posts, count } = await Post.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['id', 'username', 'email']
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: Number(limit),
        offset,
        distinct: true
      })

      // 获取每个说说的完整信息
      const postsWithInfo = await Promise.all(
        posts.map(async (post) => {
          const fullInfo = await post.getFullInfo(currentUserId)
          return fullInfo
        })
      )

      res.json({
        success: true,
        data: {
          user: {
            id: targetUser.id,
            username: targetUser.username,
            email: targetUser.email
          },
          posts: postsWithInfo,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: count,
            totalPages: Math.ceil(count / Number(limit))
          }
        }
      })
    } catch (error) {
      console.error('获取用户时间线失败:', error)
      res.status(500).json({
        success: false,
        message: '获取用户时间线失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 获取热门动态
   * 根据点赞数和评论数排序
   */
  public static async getTrendingPosts(req: Request, res: Response): Promise<void> {
    try {
      const { page = 1, limit = 10, timeRange = '7d' } = req.query
      const authReq = req as AuthenticatedRequest
      const userId = authReq.user?.id

      const offset = (Number(page) - 1) * Number(limit)

      // 计算时间范围
      const now = new Date()
      let startDate: Date

      switch (timeRange) {
        case '1d':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          break
        case '3d':
          startDate = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000)
          break
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        default:
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      }

      const whereClause = {
        visibility: 'public',
        createdAt: {
          [Op.gte]: startDate
        }
      }

      // 这里简化处理，实际应该根据点赞数和评论数排序
      // 在生产环境中，可能需要使用更复杂的查询或缓存机制
      const { rows: posts, count } = await Post.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['id', 'username', 'email']
          }
        ],
        order: [['createdAt', 'DESC']], // 暂时按时间排序，后续可以优化为按热度排序
        limit: Number(limit),
        offset,
        distinct: true
      })

      // 获取每个说说的完整信息并按热度排序
      const postsWithInfo = await Promise.all(
        posts.map(async (post) => {
          const fullInfo = await post.getFullInfo(userId)
          return fullInfo
        })
      )

      // 简单的热度计算：点赞数 * 2 + 评论数
      postsWithInfo.sort((a, b) => {
        const scoreA = (a.likeCount || 0) * 2 + (a.commentCount || 0)
        const scoreB = (b.likeCount || 0) * 2 + (b.commentCount || 0)
        return scoreB - scoreA
      })

      res.json({
        success: true,
        data: {
          posts: postsWithInfo,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: count,
            totalPages: Math.ceil(count / Number(limit))
          },
          timeRange
        }
      })
    } catch (error) {
      console.error('获取热门动态失败:', error)
      res.status(500).json({
        success: false,
        message: '获取热门动态失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }
}
