import swaggerJsdoc from 'swagger-jsdoc'
import swaggerUi from 'swagger-ui-express'
import { Express } from 'express'

/**
 * Swagger配置选项
 * 定义了OpenAPI规范的基本信息、服务器配置和安全方案
 */
const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Personal Blog API',
      version: '1.0.0',
      description: 'API documentation for Personal Blog System',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: process.env.API_URL || 'http://localhost:3001',
        description: 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    }
  },
  apis: ['./src/routes/*.ts', './src/models/*.ts'] // paths to files containing OpenAPI definitions
}

/**
 * 生成Swagger规范文档
 * 基于配置选项和API文件中的注释生成OpenAPI规范
 */
const specs = swaggerJsdoc(options)

/**
 * 配置并启动Swagger文档服务
 * 设置Swagger UI界面和JSON格式的API规范端点
 * @param app Express应用实例
 */
export const setupSwagger = (app: Express): void => {
  // 配置Swagger UI中间件，提供交互式API文档界面
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Personal Blog API Documentation'
  }))

  // JSON endpoint for the swagger spec
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json')
    res.send(specs)
  })
}