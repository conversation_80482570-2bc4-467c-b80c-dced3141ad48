"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostController = void 0;
const models_1 = require("../models");
const sequelize_1 = require("sequelize");
class PostController {
    static async getPosts(req, res) {
        try {
            const { page = 1, limit = 10, visibility = 'public', authorId, search } = req.query;
            const offset = (Number(page) - 1) * Number(limit);
            const whereClause = {};
            if (visibility) {
                whereClause.visibility = visibility;
            }
            if (authorId) {
                whereClause.authorId = Number(authorId);
            }
            if (search) {
                whereClause.content = {
                    [sequelize_1.Op.like]: `%${search}%`
                };
            }
            const { rows: posts, count } = await models_1.Post.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: models_1.User,
                        as: 'author',
                        attributes: ['id', 'username', 'email']
                    }
                ],
                order: [['createdAt', 'DESC']],
                limit: Number(limit),
                offset,
                distinct: true
            });
            const authReq = req;
            const userId = authReq.user?.id;
            const postsWithInfo = await Promise.all(posts.map(async (post) => {
                const fullInfo = await post.getFullInfo(userId);
                return fullInfo;
            }));
            res.json({
                success: true,
                data: {
                    posts: postsWithInfo,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit),
                        total: count,
                        totalPages: Math.ceil(count / Number(limit))
                    }
                }
            });
        }
        catch (error) {
            console.error('获取说说列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取说说列表失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async getPost(req, res) {
        try {
            const { id } = req.params;
            const authReq = req;
            const userId = authReq.user?.id;
            const post = await models_1.Post.findByPk(Number(id), {
                include: [
                    {
                        model: models_1.User,
                        as: 'author',
                        attributes: ['id', 'username', 'email']
                    }
                ]
            });
            if (!post) {
                res.status(404).json({
                    success: false,
                    message: '说说不存在'
                });
                return;
            }
            const canView = await post.canBeViewedBy(userId);
            if (!canView) {
                res.status(403).json({
                    success: false,
                    message: '没有权限查看此说说'
                });
                return;
            }
            const fullInfo = await post.getFullInfo(userId);
            res.json({
                success: true,
                data: fullInfo
            });
        }
        catch (error) {
            console.error('获取说说详情失败:', error);
            res.status(500).json({
                success: false,
                message: '获取说说详情失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async createPost(req, res) {
        try {
            const { content, images, visibility = 'public', location } = req.body;
            const userId = req.user.id;
            if (!content || content.trim().length === 0) {
                res.status(400).json({
                    success: false,
                    message: '说说内容不能为空'
                });
                return;
            }
            const post = await models_1.Post.create({
                content: content.trim(),
                images: images || [],
                visibility,
                location,
                authorId: userId
            });
            const createdPost = await models_1.Post.findByPk(post.id, {
                include: [
                    {
                        model: models_1.User,
                        as: 'author',
                        attributes: ['id', 'username', 'email']
                    }
                ]
            });
            const fullInfo = await createdPost.getFullInfo(userId);
            res.status(201).json({
                success: true,
                message: '说说发布成功',
                data: fullInfo
            });
        }
        catch (error) {
            console.error('创建说说失败:', error);
            res.status(500).json({
                success: false,
                message: '创建说说失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async updatePost(req, res) {
        try {
            const { id } = req.params;
            const { content, images, visibility, location } = req.body;
            const userId = req.user.id;
            const post = await models_1.Post.findByPk(Number(id));
            if (!post) {
                res.status(404).json({
                    success: false,
                    message: '说说不存在'
                });
                return;
            }
            if (post.authorId !== userId) {
                res.status(403).json({
                    success: false,
                    message: '只能编辑自己的说说'
                });
                return;
            }
            if (content !== undefined)
                post.content = content.trim();
            if (images !== undefined)
                post.images = images;
            if (visibility !== undefined)
                post.visibility = visibility;
            if (location !== undefined)
                post.location = location;
            await post.save();
            const updatedPost = await models_1.Post.findByPk(post.id, {
                include: [
                    {
                        model: models_1.User,
                        as: 'author',
                        attributes: ['id', 'username', 'email']
                    }
                ]
            });
            const fullInfo = await updatedPost.getFullInfo(userId);
            res.json({
                success: true,
                message: '说说更新成功',
                data: fullInfo
            });
        }
        catch (error) {
            console.error('更新说说失败:', error);
            res.status(500).json({
                success: false,
                message: '更新说说失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async deletePost(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user.id;
            const post = await models_1.Post.findByPk(Number(id));
            if (!post) {
                res.status(404).json({
                    success: false,
                    message: '说说不存在'
                });
                return;
            }
            if (post.authorId !== userId) {
                res.status(403).json({
                    success: false,
                    message: '只能删除自己的说说'
                });
                return;
            }
            await models_1.PostLike.deleteByPostId(Number(id));
            await models_1.Comment.destroy({ where: { postId: Number(id) } });
            await post.destroy();
            res.json({
                success: true,
                message: '说说删除成功'
            });
        }
        catch (error) {
            console.error('删除说说失败:', error);
            res.status(500).json({
                success: false,
                message: '删除说说失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async toggleLike(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user.id;
            const post = await models_1.Post.findByPk(Number(id));
            if (!post) {
                res.status(404).json({
                    success: false,
                    message: '说说不存在'
                });
                return;
            }
            const canView = await post.canBeViewedBy(userId);
            if (!canView) {
                res.status(403).json({
                    success: false,
                    message: '没有权限操作此说说'
                });
                return;
            }
            const result = await models_1.PostLike.toggleLike(Number(id), userId);
            res.json({
                success: true,
                message: result.action === 'liked' ? '点赞成功' : '取消点赞成功',
                data: {
                    action: result.action,
                    likeCount: result.likeCount
                }
            });
        }
        catch (error) {
            console.error('切换点赞状态失败:', error);
            res.status(500).json({
                success: false,
                message: '操作失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async getLikes(req, res) {
        try {
            const { id } = req.params;
            const { page = 1, limit = 20 } = req.query;
            const post = await models_1.Post.findByPk(Number(id));
            if (!post) {
                res.status(404).json({
                    success: false,
                    message: '说说不存在'
                });
                return;
            }
            const offset = (Number(page) - 1) * Number(limit);
            const result = await models_1.PostLike.getLikeUsers(Number(id), Number(limit), offset);
            res.json({
                success: true,
                data: {
                    likes: result.rows,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit),
                        total: result.count,
                        totalPages: Math.ceil(result.count / Number(limit))
                    }
                }
            });
        }
        catch (error) {
            console.error('获取点赞列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取点赞列表失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
}
exports.PostController = PostController;
//# sourceMappingURL=post.js.map