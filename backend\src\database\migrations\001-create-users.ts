import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 执行数据库迁移升级操作，创建用户表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 返回空的Promise，表示异步操作完成
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 创建用户表，包含用户基本信息和时间戳字段
  await queryInterface.createTable('users', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true
    },
    password_hash: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  })

  // 为用户名和邮箱字段添加索引以提高查询性能
  // 使用 try-catch 来避免重复创建索引的错误
  try {
    await queryInterface.addIndex('users', ['username'])
  } catch {
    console.log('Index users_username already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('users', ['email'])
  } catch {
    console.log('Index users_email already exists, skipping...')
  }
}

/**
 * 执行数据库迁移降级操作，删除用户表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 返回空的Promise，表示异步操作完成
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('users')
}