"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = __importDefault(require("./auth"));
const article_1 = __importDefault(require("./article"));
const tag_1 = __importDefault(require("./tag"));
const category_1 = __importDefault(require("./category"));
const comment_1 = __importDefault(require("./comment"));
const post_1 = __importDefault(require("./post"));
const timeline_1 = __importDefault(require("./timeline"));
const upload_1 = __importDefault(require("./upload"));
const router = (0, express_1.Router)();
router.use('/auth', auth_1.default);
router.use('/articles', article_1.default);
router.use('/tags', tag_1.default);
router.use('/categories', category_1.default);
router.use('/comments', comment_1.default);
router.use('/posts', post_1.default);
router.use('/timeline', timeline_1.default);
router.use('/upload', upload_1.default);
router.get('/', (req, res) => {
    res.json({
        success: true,
        message: 'Personal Blog API',
        version: '1.0.0',
        endpoints: {
            auth: '/api/auth',
            articles: '/api/articles',
            tags: '/api/tags',
            categories: '/api/categories',
            comments: '/api/comments',
            posts: '/api/posts',
            timeline: '/api/timeline',
            upload: '/api/upload',
            health: '/health',
            docs: '/api-docs'
        }
    });
});
exports.default = router;
//# sourceMappingURL=index.js.map