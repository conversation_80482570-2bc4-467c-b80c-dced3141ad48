import jwt from 'jsonwebtoken'

export interface JwtPayload {
  id: number
  username: string
  email: string
}

/**
 * 生成JWT令牌
 * @param payload 包含用户信息的载荷对象，包含id、username和email字段
 * @returns 签名后的JWT令牌字符串
 */
export const generateToken = (payload: JwtPayload): string => {
  const secret = process.env.JWT_SECRET
  // 检查JWT密钥是否存在
  if (!secret) {
    throw new Error('JWT_SECRET is not defined')
  }

  // 使用jsonwebtoken库签名生成令牌，设置过期时间
  return jwt.sign(payload, secret, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  } as any)
}

/**
 * 验证JWT令牌
 * @param token 需要验证的JWT令牌字符串
 * @returns 解码后的载荷对象，包含用户信息
 */
export const verifyToken = (token: string): JwtPayload => {
  const secret = process.env.JWT_SECRET
  // 检查JWT密钥是否存在
  if (!secret) {
    throw new Error('JWT_SECRET is not defined')
  }

  // 使用jsonwebtoken库验证令牌并返回解码后的载荷
  return jwt.verify(token, secret) as JwtPayload
}