{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AACA,2CAA0D;AAC1D,iDAA4C;AAgBrC,MAAM,iBAAiB,GAAG,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IACrH,IAAI,CAAC;QAEH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;QAC/C,MAAM,KAAK,GAAG,kBAAW,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAA;QAG5D,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,uBAAuB,EAAE,cAAc,CAAC,CAAA;QACjE,CAAC;QAGD,MAAM,MAAM,GAAG,MAAM,kBAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;QAGrD,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACpC,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,MAAM,CAAC,OAAO,IAAI,0BAA0B,EAAE,WAAW,CAAC,CAAA;QACnF,CAAC;QAGD,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAA;IACb,CAAC;AACH,CAAC,CAAA;AAzBY,QAAA,iBAAiB,qBAyB7B;AASM,MAAM,YAAY,GAAG,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IAChH,IAAI,CAAC;QAEH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;QAC/C,MAAM,KAAK,GAAG,kBAAW,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAA;QAG5D,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,EAAE,CAAA;QACf,CAAC;QAGD,MAAM,MAAM,GAAG,MAAM,kBAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;QAGrD,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YAClC,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QACxB,CAAC;QAED,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAEf,IAAI,EAAE,CAAA;IACR,CAAC;AACH,CAAC,CAAA;AAxBY,QAAA,YAAY,gBAwBxB"}