import { User } from '../../models/User'
import { sequelize } from '../../config/database'

describe('User Model', () => {
  beforeAll(async () => {
    await sequelize.sync({ force: true })
  })

  afterAll(async () => {
    await sequelize.close()
  })

  beforeEach(async () => {
    await User.destroy({ where: {} })
  })

  describe('User Creation', () => {
    it('should create a user with valid data', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: 'password123'
      }

      const user = await User.create(userData)

      expect(user.id).toBeDefined()
      expect(user.username).toBe(userData.username)
      expect(user.email).toBe(userData.email)
      expect(user.passwordHash).not.toBe(userData.passwordHash) // Should be hashed
      expect(user.createdAt).toBeDefined()
      expect(user.updatedAt).toBeDefined()
    })

    it('should hash password before saving', async () => {
      const plainPassword = 'password123'
      const user = await User.create({
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: plainPassword
      })

      expect(user.passwordHash).not.toBe(plainPassword)
      expect(user.passwordHash.length).toBeGreaterThan(50) // Bcrypt hash length
    })

    it('should validate password correctly', async () => {
      const plainPassword = 'password123'
      const user = await User.create({
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: plainPassword
      })

      const isValid = await user.validatePassword(plainPassword)
      const isInvalid = await user.validatePassword('wrongpassword')

      expect(isValid).toBe(true)
      expect(isInvalid).toBe(false)
    })
  })

  describe('User Validation', () => {
    it('should require username', async () => {
      await expect(User.create({
        email: '<EMAIL>',
        passwordHash: 'password123'
      } as any)).rejects.toThrow()
    })

    it('should require email', async () => {
      await expect(User.create({
        username: 'testuser',
        passwordHash: 'password123'
      } as any)).rejects.toThrow()
    })

    it('should require valid email format', async () => {
      await expect(User.create({
        username: 'testuser',
        email: 'invalid-email',
        passwordHash: 'password123'
      })).rejects.toThrow()
    })

    it('should enforce unique username', async () => {
      await User.create({
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: 'password123'
      })

      await expect(User.create({
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: 'password123'
      })).rejects.toThrow()
    })

    it('should enforce unique email', async () => {
      await User.create({
        username: 'testuser1',
        email: '<EMAIL>',
        passwordHash: 'password123'
      })

      await expect(User.create({
        username: 'testuser2',
        email: '<EMAIL>',
        passwordHash: 'password123'
      })).rejects.toThrow()
    })
  })

  describe('User Methods', () => {
    it('should find user by username', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: 'password123'
      }

      await User.create(userData)
      const foundUser = await User.findByUsername('testuser')

      expect(foundUser).toBeTruthy()
      expect(foundUser?.username).toBe('testuser')
    })

    it('should find user by email', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: 'password123'
      }

      await User.create(userData)
      const foundUser = await User.findByEmail('<EMAIL>')

      expect(foundUser).toBeTruthy()
      expect(foundUser?.email).toBe('<EMAIL>')
    })

    it('should exclude password from JSON output', async () => {
      const user = await User.create({
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: 'password123'
      })

      const json = user.toJSON()

      expect(json).not.toHaveProperty('passwordHash')
      expect(json).toHaveProperty('username')
      expect(json).toHaveProperty('email')
    })
  })
})