"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const slug_1 = require("../../utils/slug");
const up = async (queryInterface) => {
    console.log('Creating sample tags...');
    const sampleTags = [
        'JavaScript',
        'TypeScript',
        'React',
        'Vue.js',
        'Node.js',
        'Express',
        'MySQL',
        'MongoDB',
        'Docker',
        'AWS',
        '前端开发',
        '后端开发',
        '全栈开发',
        'Web开发',
        '数据库',
        '云计算',
        '微服务',
        'API设计',
        '性能优化',
        '代码规范',
        '项目管理',
        '敏捷开发',
        '测试驱动开发',
        '持续集成',
        '开源项目',
        'CSS',
        'HTML',
        'Webpack',
        'Vite',
        'Git'
    ];
    const existingTags = await queryInterface.select(null, 'tags', {});
    const existingTagNames = existingTags.map((tag) => tag.name);
    const newTags = sampleTags.filter(tagName => !existingTagNames.includes(tagName));
    if (newTags.length === 0) {
        console.log('All sample tags already exist, skipping...');
        return;
    }
    const tagsToInsert = newTags.map((tagName, index) => ({
        name: tagName,
        slug: (0, slug_1.generateSlug)(tagName),
        created_at: new Date(`2024-01-06 ${String(9 + Math.floor(index / 10)).padStart(2, '0')}:${String((index % 10) * 6).padStart(2, '0')}:00`)
    }));
    await queryInterface.bulkInsert('tags', tagsToInsert);
    console.log(`Created ${newTags.length} sample tags:`);
    newTags.forEach(tag => console.log(`  • ${tag}`));
};
exports.up = up;
const down = async (queryInterface) => {
    console.log('Removing sample tags...');
    const sampleTagNames = [
        'JavaScript',
        'TypeScript',
        'React',
        'Vue.js',
        'Node.js',
        'Express',
        'MySQL',
        'MongoDB',
        'Docker',
        'AWS',
        '前端开发',
        '后端开发',
        '全栈开发',
        'Web开发',
        '数据库',
        '云计算',
        '微服务',
        'API设计',
        '性能优化',
        '代码规范',
        '项目管理',
        '敏捷开发',
        '测试驱动开发',
        '持续集成',
        '开源项目',
        'CSS',
        'HTML',
        'Webpack',
        'Vite',
        'Git'
    ];
    await queryInterface.bulkDelete('tags', {
        name: sampleTagNames
    }, {});
    console.log('Sample tags removed successfully!');
};
exports.down = down;
//# sourceMappingURL=003-tags.js.map