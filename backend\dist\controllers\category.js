"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteCategory = exports.deleteCategories = exports.getCategoryStats = exports.getCategoryArticles = exports.updateCategory = exports.createCategory = exports.getCategory = exports.getCategories = void 0;
const Category_1 = require("../models/Category");
const Article_1 = require("../models/Article");
const User_1 = require("../models/User");
const Tag_1 = require("../models/Tag");
const getCategories = async (req, res, next) => {
    try {
        const { tree, flat, stats } = req.query;
        let categories;
        if (tree === 'true') {
            categories = await Category_1.Category.getTree();
        }
        else if (flat === 'true') {
            categories = await Category_1.Category.getFlatList();
        }
        else if (stats === 'true') {
            categories = await Category_1.Category.getStats(true);
        }
        else {
            categories = await Category_1.Category.findAll({
                order: [['sort', 'ASC'], ['name', 'ASC']],
                include: [
                    { model: Category_1.Category, as: 'parent', attributes: ['id', 'name', 'slug'] }
                ]
            });
        }
        res.json({
            success: true,
            data: { categories }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getCategories = getCategories;
const getCategory = async (req, res, next) => {
    try {
        const { id } = req.params;
        if (!id) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_ID',
                    message: 'Category ID is required'
                }
            });
            return;
        }
        let category = null;
        if (/^\d+$/.test(id)) {
            category = await Category_1.Category.findByPk(id, {
                include: [
                    { model: Category_1.Category, as: 'parent', attributes: ['id', 'name', 'slug'] },
                    { model: Category_1.Category, as: 'children', separate: true, order: [['sort', 'ASC'], ['name', 'ASC']] }
                ]
            });
        }
        else {
            category = await Category_1.Category.findBySlug(id);
        }
        if (!category) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'CATEGORY_NOT_FOUND',
                    message: 'Category not found'
                }
            });
            return;
        }
        res.json({
            success: true,
            data: { category }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getCategory = getCategory;
const createCategory = async (req, res, next) => {
    try {
        const { name, description, parentId, sort = 0, slug } = req.body;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        if (!name || !name.trim()) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_INPUT',
                    message: 'Category name is required'
                }
            });
            return;
        }
        if (parentId) {
            const parentCategory = await Category_1.Category.findByPk(parentId);
            if (!parentCategory) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: 'PARENT_NOT_FOUND',
                        message: 'Parent category not found'
                    }
                });
                return;
            }
        }
        const categoryData = {
            name: name.trim(),
            description: description?.trim(),
            parentId: parentId || null,
            sort
        };
        if (slug && slug.trim()) {
            categoryData.slug = slug.trim();
        }
        const category = await Category_1.Category.create(categoryData);
        const createdCategory = await Category_1.Category.findByPk(category.id, {
            include: [
                { model: Category_1.Category, as: 'parent', attributes: ['id', 'name', 'slug'] },
                { model: Category_1.Category, as: 'children', separate: true, order: [['sort', 'ASC'], ['name', 'ASC']] }
            ]
        });
        res.status(201).json({
            success: true,
            data: { category: createdCategory }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.createCategory = createCategory;
const updateCategory = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { name, description, parentId, sort, slug } = req.body;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const category = await Category_1.Category.findByPk(id);
        if (!category) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'CATEGORY_NOT_FOUND',
                    message: 'Category not found'
                }
            });
            return;
        }
        if (parentId !== undefined) {
            if (parentId === parseInt(id || '0')) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_PARENT',
                        message: 'Category cannot be its own parent'
                    }
                });
                return;
            }
            if (parentId) {
                const parentCategory = await Category_1.Category.findByPk(parentId);
                if (!parentCategory) {
                    res.status(400).json({
                        success: false,
                        error: {
                            code: 'PARENT_NOT_FOUND',
                            message: 'Parent category not found'
                        }
                    });
                    return;
                }
                if (await parentCategory.isChildOf(category.id)) {
                    res.status(400).json({
                        success: false,
                        error: {
                            code: 'CIRCULAR_REFERENCE',
                            message: 'Cannot set parent category that would create a circular reference'
                        }
                    });
                    return;
                }
            }
        }
        const updateData = {};
        if (name !== undefined && name.trim())
            updateData.name = name.trim();
        if (description !== undefined)
            updateData.description = description?.trim();
        if (parentId !== undefined)
            updateData.parentId = parentId || null;
        if (sort !== undefined)
            updateData.sort = sort;
        if (slug !== undefined && slug.trim())
            updateData.slug = slug.trim();
        await category.update(updateData);
        const updatedCategory = await Category_1.Category.findByPk(category.id, {
            include: [
                { model: Category_1.Category, as: 'parent', attributes: ['id', 'name', 'slug'] },
                { model: Category_1.Category, as: 'children', separate: true, order: [['sort', 'ASC'], ['name', 'ASC']] }
            ]
        });
        res.json({
            success: true,
            data: { category: updatedCategory }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateCategory = updateCategory;
const getCategoryArticles = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { page = '1', limit = '10', status = 'published', includeChildren = 'false' } = req.query;
        const pageNum = parseInt(page, 10);
        const limitNum = parseInt(limit, 10);
        const offset = (pageNum - 1) * limitNum;
        const category = await Category_1.Category.findByPk(id);
        if (!category) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'CATEGORY_NOT_FOUND',
                    message: 'Category not found'
                }
            });
            return;
        }
        const whereClause = { status };
        if (includeChildren === 'true') {
            const allChildren = await category.getAllChildren();
            const categoryIds = [category.id, ...allChildren.map(child => child.id)];
            whereClause.categoryId = categoryIds;
        }
        else {
            whereClause.categoryId = category.id;
        }
        const { rows: articles, count } = await Article_1.Article.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: User_1.User,
                    as: 'author',
                    attributes: ['id', 'username']
                },
                {
                    model: Category_1.Category,
                    as: 'category',
                    attributes: ['id', 'name', 'slug']
                },
                {
                    model: Tag_1.Tag,
                    as: 'tags',
                    attributes: ['id', 'name', 'slug'],
                    through: { attributes: [] }
                }
            ],
            order: [['createdAt', 'DESC']],
            limit: limitNum,
            offset,
            distinct: true
        });
        const totalPages = Math.ceil(count / limitNum);
        const hasNextPage = pageNum < totalPages;
        const hasPrevPage = pageNum > 1;
        res.json({
            success: true,
            data: {
                category,
                articles,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: count,
                    itemsPerPage: limitNum,
                    hasNextPage,
                    hasPrevPage
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getCategoryArticles = getCategoryArticles;
const getCategoryStats = async (req, res, next) => {
    try {
        const { includeChildren = 'true' } = req.query;
        const stats = await Category_1.Category.getStats(includeChildren === 'true');
        res.json({
            success: true,
            data: { stats }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getCategoryStats = getCategoryStats;
const deleteCategories = async (req, res, next) => {
    try {
        const { ids } = req.body;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        if (!Array.isArray(ids) || ids.length === 0) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_INPUT',
                    message: 'IDs array is required and cannot be empty'
                }
            });
            return;
        }
        const categories = await Category_1.Category.findAll({
            where: { id: ids }
        });
        if (categories.length === 0) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'NO_CATEGORIES_FOUND',
                    message: 'No categories found'
                }
            });
            return;
        }
        const validationErrors = [];
        for (const category of categories) {
            const childrenCount = await Category_1.Category.count({
                where: { parentId: category.id }
            });
            const articlesCount = await Article_1.Article.count({
                where: { categoryId: category.id }
            });
            if (childrenCount > 0) {
                validationErrors.push(`Category "${category.name}" has child categories`);
            }
            if (articlesCount > 0) {
                validationErrors.push(`Category "${category.name}" has articles`);
            }
        }
        if (validationErrors.length > 0) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_FAILED',
                    message: 'Cannot delete categories',
                    details: validationErrors
                }
            });
            return;
        }
        await Category_1.Category.destroy({
            where: { id: categories.map(category => category.id) }
        });
        res.json({
            success: true,
            message: `${categories.length} categories deleted successfully`,
            data: {
                deletedCount: categories.length,
                deletedIds: categories.map(category => category.id)
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteCategories = deleteCategories;
const deleteCategory = async (req, res, next) => {
    try {
        const { id } = req.params;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const category = await Category_1.Category.findByPk(id);
        if (!category) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'CATEGORY_NOT_FOUND',
                    message: 'Category not found'
                }
            });
            return;
        }
        const childrenCount = await Category_1.Category.count({
            where: { parentId: category.id }
        });
        if (childrenCount > 0) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'HAS_CHILDREN',
                    message: 'Cannot delete category that has child categories'
                }
            });
            return;
        }
        const articlesCount = await Article_1.Article.count({
            where: { categoryId: category.id }
        });
        if (articlesCount > 0) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'HAS_ARTICLES',
                    message: 'Cannot delete category that has articles'
                }
            });
            return;
        }
        await category.destroy();
        res.json({
            success: true,
            message: 'Category deleted successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteCategory = deleteCategory;
//# sourceMappingURL=category.js.map