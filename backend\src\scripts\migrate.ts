#!/usr/bin/env ts-node

import { MigrationRunner } from '../utils/migrationRunner'
import { dbConnection } from '../database/connection'
import dotenv from 'dotenv'

// 加载环境变量
dotenv.config()

/**
 * 数据库迁移脚本
 * 用于执行数据库结构迁移
 */
class MigrateScript {
  private migrationRunner: MigrationRunner

  constructor() {
    this.migrationRunner = new MigrationRunner()
  }

  /**
   * 执行迁移
   */
  async run(): Promise<void> {
    console.log('🚀 Starting database migration...')
    console.log('=' .repeat(50))

    try {
      // 显示数据库连接信息
      const connectionInfo = dbConnection.getConnectionInfo()
      console.log(`📊 Database: ${connectionInfo.database}`)
      console.log(`🏠 Host: ${connectionInfo.host}:${connectionInfo.port}`)
      console.log(`👤 User: ${connectionInfo.username}`)
      console.log('')

      // 测试数据库连接
      console.log('🔍 Testing database connection...')
      const isConnected = await dbConnection.testConnection()
      
      if (!isConnected) {
        console.error('❌ Database connection failed!')
        process.exit(1)
      }

      console.log('✅ Database connection successful!')
      console.log('')

      // 执行迁移
      console.log('📦 Running migrations...')
      await this.migrationRunner.runMigrations()
      
      console.log('')
      console.log('=' .repeat(50))
      console.log('🎉 Migration completed successfully!')
      
    } catch (error) {
      console.error('')
      console.error('=' .repeat(50))
      console.error('❌ Migration failed!')
      console.error('Error details:', error)
      process.exit(1)
    }
  }

  /**
   * 显示帮助信息
   */
  showHelp(): void {
    console.log(`
📚 Database Migration Tool

Usage:
  npm run migrate              Run all pending migrations
  ts-node src/scripts/migrate.ts    Run migrations directly

Environment Variables:
  DB_HOST      Database host (default: localhost)
  DB_PORT      Database port (default: 3306)
  DB_NAME      Database name (default: person-blog)
  DB_USER      Database user (default: person-blog)
  DB_PASSWORD  Database password

Examples:
  npm run migrate
  NODE_ENV=production npm run migrate:prod
`)
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2)
  
  // 检查是否请求帮助
  if (args.includes('--help') || args.includes('-h')) {
    const script = new MigrateScript()
    script.showHelp()
    return
  }

  // 执行迁移
  const script = new MigrateScript()
  await script.run()
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
  process.exit(1)
})

// 执行主函数
if (require.main === module) {
  main().catch((error) => {
    console.error('Script execution failed:', error)
    process.exit(1)
  })
}
