"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const tag_1 = require("../controllers/tag");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.get('/', tag_1.getTags);
router.post('/', auth_1.authenticateToken, tag_1.createTag);
router.put('/:id', auth_1.authenticateToken, tag_1.updateTag);
router.delete('/:id', auth_1.authenticateToken, tag_1.deleteTag);
router.post('/batch-delete', auth_1.authenticateToken, tag_1.batchDeleteTags);
router.get('/stats', tag_1.getTagStats);
router.get('/popular', tag_1.getPopularTags);
router.get('/:tagName/articles', tag_1.getTagArticles);
router.post('/article-association', auth_1.authenticateToken, tag_1.addTagToArticle);
router.delete('/article-association/:articleId/:tagId', auth_1.authenticateToken, tag_1.removeTagFromArticle);
router.get('/article/:articleId', tag_1.getArticleTags);
exports.default = router;
//# sourceMappingURL=tag.js.map