import {
  createElementBlock,
  defineComponent,
  onBeforeUnmount,
  openBlock,
  ref,
  watch
} from "./chunk-MMWK553F.js";
import "./chunk-ZC22LKFR.js";

// node_modules/vue-quilly/dist/vue-quilly.js
var y = defineComponent({
  __name: "QuillyEditor",
  props: {
    modelValue: {},
    options: {},
    isSemanticHtmlModel: { type: Boolean }
  },
  emits: ["update:modelValue", "text-change", "selection-change", "editor-change", "blur", "focus", "ready"],
  setup(p, { expose: f, emit: h }) {
    const i = p, l = h;
    let n = null;
    const r = ref(), c = ref(), m = (t) => {
      c.value = i.modelValue;
      const e = t.getContents(), o = t.clipboard.convert({ html: i.modelValue ?? "" });
      t.setContents(o), l("text-change", { delta: o, oldContent: e, source: "api" });
    }, g = (t) => {
      const e = new t(r.value, i.options);
      return i.modelValue && m(e), e.on("selection-change", (o, a, s) => {
        l(o ? "focus" : "blur", e), l("selection-change", { range: o, oldRange: a, source: s });
      }), e.on("text-change", (o, a, s) => {
        c.value = i.isSemanticHtmlModel ? e.getSemanticHTML() : e.root.innerHTML, l("text-change", { delta: o, oldContent: a, source: s });
      }), e.on("editor-change", (o) => {
        l("editor-change", o);
      }), l("ready", e), n = e, e;
    };
    return watch(
      () => i.modelValue,
      (t) => {
        n && (t && t !== c.value ? (m(n), c.value = i.isSemanticHtmlModel ? n.getSemanticHTML() : n.root.innerHTML) : t || n.setContents([]));
      }
    ), watch(c, (t, e) => {
      n && (t && t !== e ? l("update:modelValue", t) : t || n.setContents([]));
    }), onBeforeUnmount(() => {
      n = null;
    }), f({
      initialize: g
    }), (t, e) => (openBlock(), createElementBlock("div", {
      ref_key: "container",
      ref: r
    }, null, 512));
  }
});
export {
  y as QuillyEditor
};
//# sourceMappingURL=vue-quilly.js.map
