import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
export declare const getTags: (req: Request, res: Response) => Promise<void>;
export declare const getTagArticles: (req: Request, res: Response) => Promise<void>;
export declare const createTag: (req: Request, res: Response) => Promise<void>;
export declare const deleteTag: (req: Request, res: Response) => Promise<void>;
export declare const getTagStats: (req: Request, res: Response) => Promise<void>;
export declare const getPopularTags: (req: Request, res: Response) => Promise<void>;
export declare const addTagToArticle: (req: AuthenticatedRequest, res: Response) => Promise<void>;
export declare const removeTagFromArticle: (req: AuthenticatedRequest, res: Response) => Promise<void>;
export declare const getArticleTags: (req: Request, res: Response) => Promise<void>;
export declare const updateTag: (req: Request, res: Response) => Promise<void>;
export declare const batchDeleteTags: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=tag.d.ts.map