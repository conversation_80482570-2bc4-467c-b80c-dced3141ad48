{"version": 3, "file": "DetachedBrowserFrame.cjs", "sourceRoot": "", "sources": ["../../../src/browser/detached-browser/DetachedBrowserFrame.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,wEAA0D;AAC1D,uGAA4E;AAK5E,yFAA8D;AAC9D,iHAAsF;AACtF,qGAA0E;AAM1E,oHAAyF;AAEzF;;GAEG;AACH,MAAqB,oBAAoB;IAIxB,WAAW,GAA2B,EAAE,CAAC;IACzC,WAAW,GAAgC,IAAI,CAAC;IAChD,IAAI,CAAsB;IAC1B,MAAM,GAAY,KAAK,CAAC;IACjC,CAAC,cAAc,CAAC,gBAAgB,CAAC,GAAqB,IAAI,6BAAgB,CAAC,IAAI,CAAC,CAAC;IACjF,CAAC,cAAc,CAAC,SAAS,CAAC,GAAsC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;IACnF,CAAC,cAAc,CAAC,WAAW,CAAC,GAAyB,IAAI,CAAC;IAC1D,CAAC,cAAc,CAAC,YAAY,CAAC,GAAoD,IAAI,CAAC;IACtF,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;IAC/B,CAAC,cAAc,CAAC,OAAO,CAAC,GAAmB;QACjD;YACC,KAAK,EAAE,EAAE;YACT,IAAI,EAAE,aAAa;YACnB,KAAK,EAAE,IAAI;YACX,iBAAiB,EAAE,yCAA4B,CAAC,IAAI;YACpD,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,IAAI;SACf;KACD,CAAC;IAEF;;;;;OAKG;IACH,YAAY,IAAyB;QACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC;YAC3D,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAE9D,wCAAwC;YACxC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBAC5D,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7E,CAAC;QACF,CAAC;IACF,CAAC;IAED;;;;OAIG;IACH,IAAW,OAAO;QACjB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;QACpF,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC;IACvD,CAAC;IAED;;;;OAIG;IACH,IAAW,OAAO,CAAC,OAAO;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;QACpF,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;QACzD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAC,GAAG,KAAK,CAAC;QACnE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC5B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACH,IAAW,GAAG;QACb,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;QACpF,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACH,IAAW,GAAG,CAAC,GAAG;QACjB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;QACpF,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAC1D,IAAI,EACJ,4BAAe,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAC9C,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,IAAW,QAAQ;QAClB,OAAO,IAAI,CAAC,MAAM,EAAE,QAAQ,IAAI,IAAI,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,iBAAiB;QAC7B,MAAM,OAAO,CAAC,GAAG,CAAC;YACjB,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,iBAAiB,EAAE;YACzD,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;SAC7D,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,iBAAiB;QACvB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACI,KAAK;QACX,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,KAAK,EAAE,CAAC;QACtD,CAAC;QACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACtC,4DAA4D;YAC5D,OAAO,CAAC,GAAG,CACV,IAAI,CAAC,WAAW;iBACd,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;iBAC7B,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CACzD;iBACC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;iBACrB,KAAK,CAAC,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,MAAuB;QACtC,OAAO,wCAA2B,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;OAMG;IACI,IAAI,CAAC,GAAW,EAAE,OAAsB;QAC9C,OAAO,kCAAqB,CAAC,QAAQ,CAAC;YACrC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW;YAClD,KAAK,EAAE,IAAI;YACX,GAAG,EAAE,GAAG;YACR,WAAW,EAAE,OAAO;SACpB,CAAC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,OAAsB;QACnC,OAAO,kCAAqB,CAAC,YAAY,CAAC;YACzC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW;YAClD,KAAK,EAAE,IAAI;YACX,WAAW,EAAE,OAAO;SACpB,CAAC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,OAAsB;QACtC,OAAO,kCAAqB,CAAC,eAAe,CAAC;YAC5C,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW;YAClD,KAAK,EAAE,IAAI;YACX,WAAW,EAAE,OAAO;SACpB,CAAC,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,KAAc,EAAE,OAAsB;QACpD,OAAO,kCAAqB,CAAC,aAAa,CAAC;YAC1C,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW;YAClD,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,KAAK;YACZ,WAAW,EAAE,OAAO;SACpB,CAAC,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,OAAwB;QACrC,OAAO,kCAAqB,CAAC,MAAM,CAAC;YACnC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW;YAClD,KAAK,EAAE,IAAI;YACX,WAAW,EAAE,OAAO;SACpB,CAAC,CAAC;IACJ,CAAC;CACD;AA/ND,uCA+NC"}