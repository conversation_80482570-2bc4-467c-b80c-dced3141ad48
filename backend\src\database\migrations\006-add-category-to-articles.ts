import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 执行数据库迁移，给文章表添加分类字段
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 无返回值的异步函数
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 给文章表添加分类ID字段
  await queryInterface.addColumn('articles', 'category_id', {
    type: DataTypes.INTEGER,
    allowNull: true, // 设置为可空，以兼容现有数据
    references: {
      model: 'categories',
      key: 'id'
    },
    onDelete: 'SET NULL', // 当分类被删除时，将文章的分类ID设置为NULL
    onUpdate: 'CASCADE'
  })

  // 为分类ID字段创建索引以提高查询性能
  // 使用 try-catch 来避免重复创建索引的错误
  try {
    await queryInterface.addIndex('articles', ['category_id'])
  } catch {
    console.log('Index articles_category_id already exists, skipping...')
  }
}

/**
 * 回滚数据库迁移，删除文章表的分类字段
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 无返回值的异步函数
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  // 先删除索引
  await queryInterface.removeIndex('articles', ['category_id'])

  // 删除分类ID字段
  await queryInterface.removeColumn('articles', 'category_id')
}
