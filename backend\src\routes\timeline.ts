import { Router } from 'express'
import { TimelineController } from '../controllers/timeline'
import { authenticateToken } from '../middleware/auth'

const router = Router()

/**
 * 时间线相关路由配置
 * 所有路由都以 /api/timeline 为前缀
 */

/**
 * GET /api/timeline/public
 * 获取公开时间线
 * 显示所有公开的动态
 * 查询参数：
 * - page: 页码（默认1）
 * - limit: 每页数量（默认10）
 * - search: 搜索关键词（可选）
 */
router.get('/public', TimelineController.getPublicTimeline)

/**
 * GET /api/timeline/trending
 * 获取热门动态
 * 根据点赞数和评论数排序的热门内容
 * 查询参数：
 * - page: 页码（默认1）
 * - limit: 每页数量（默认10）
 * - timeRange: 时间范围（1d/3d/7d/30d，默认7d）
 */
router.get('/trending', TimelineController.getTrendingPosts)

/**
 * GET /api/timeline/user/:userId
 * 获取指定用户的时间线
 * 显示用户的动态（根据权限过滤）
 * 参数：
 * - userId: 用户ID
 * 查询参数：
 * - page: 页码（默认1）
 * - limit: 每页数量（默认10）
 */
router.get('/user/:userId', TimelineController.getUserTimeline)

// 需要认证的路由
/**
 * GET /api/timeline
 * 获取个人时间线
 * 显示用户自己和好友的动态
 * 需要认证
 * 查询参数：
 * - page: 页码（默认1）
 * - limit: 每页数量（默认10）
 */
router.get('/', authenticateToken, TimelineController.getPersonalTimeline)

export default router
