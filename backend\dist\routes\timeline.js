"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const timeline_1 = require("../controllers/timeline");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.get('/public', timeline_1.TimelineController.getPublicTimeline);
router.get('/trending', timeline_1.TimelineController.getTrendingPosts);
router.get('/user/:userId', timeline_1.TimelineController.getUserTimeline);
router.get('/', auth_1.authenticateToken, timeline_1.TimelineController.getPersonalTimeline);
exports.default = router;
//# sourceMappingURL=timeline.js.map