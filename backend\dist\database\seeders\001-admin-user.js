"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const up = async (queryInterface) => {
    console.log('Creating sample users...');
    const existingUsers = await queryInterface.select(null, 'users', {});
    if (existingUsers.length > 0) {
        console.log('Users already exist, skipping...');
        return;
    }
    const saltRounds = 12;
    const adminPasswordHash = await bcryptjs_1.default.hash('admin123', saltRounds);
    const userPasswordHash = await bcryptjs_1.default.hash('user123', saltRounds);
    const users = [
        {
            username: 'admin',
            email: '<EMAIL>',
            password_hash: adminPasswordHash,
            created_at: new Date('2024-01-01 10:00:00'),
            updated_at: new Date('2024-01-01 10:00:00')
        },
        {
            username: 'john_doe',
            email: '<EMAIL>',
            password_hash: userPasswordHash,
            created_at: new Date('2024-01-02 09:30:00'),
            updated_at: new Date('2024-01-02 09:30:00')
        },
        {
            username: 'jane_smith',
            email: '<EMAIL>',
            password_hash: userPasswordHash,
            created_at: new Date('2024-01-03 14:15:00'),
            updated_at: new Date('2024-01-03 14:15:00')
        },
        {
            username: 'tech_writer',
            email: '<EMAIL>',
            password_hash: userPasswordHash,
            created_at: new Date('2024-01-04 11:20:00'),
            updated_at: new Date('2024-01-04 11:20:00')
        }
    ];
    await queryInterface.bulkInsert('users', users);
    console.log(`Created ${users.length} sample users:`);
    users.forEach(user => {
        console.log(`  • ${user.username} (${user.email})`);
    });
    console.log('');
    console.log('Default passwords:');
    console.log('  • admin: admin123');
    console.log('  • others: user123');
    console.log('⚠️  Please change default passwords in production!');
};
exports.up = up;
const down = async (queryInterface) => {
    console.log('Removing sample users...');
    const usernames = ['admin', 'john_doe', 'jane_smith', 'tech_writer'];
    await queryInterface.bulkDelete('users', {
        username: usernames
    }, {});
    console.log('Sample users removed successfully!');
};
exports.down = down;
//# sourceMappingURL=001-admin-user.js.map