import{d as f,q as v,k,a as r,e as t,u as e,t as a,b as m,i as l,w as d,h as o,y as g,z as y,o as _,_ as b}from"./index-1ed5a19c.js";const w={class:"admin-view"},x={class:"admin-header"},A={class:"user-info"},V={key:0},h={class:"admin-nav"},C={class:"admin-content"},N={class:"dashboard-card"},B={class:"quick-actions"},S={class:"action-buttons"},q={key:0,class:"user-details"},D=f({__name:"Admin",setup(I){const u=g(),n=v(),p=async()=>{await n.logout(),u.push("/login")};return k(async()=>{if(!n.isAuthenticated){u.push("/login");return}await n.checkAuth()||u.push("/login")}),(c,s)=>{const i=y("router-link");return _(),r("div",w,[t("header",x,[s[0]||(s[0]=t("h1",null,"管理后台",-1)),t("div",A,[e(n).user?(_(),r("span",V,"欢迎, "+a(e(n).user.username),1)):m("",!0),t("button",{onClick:p,class:"logout-button"},"退出登录")])]),t("nav",h,[l(i,{to:"/admin",class:"nav-link"},{default:d(()=>s[1]||(s[1]=[o("仪表板",-1)])),_:1,__:[1]}),l(i,{to:"/admin/articles",class:"nav-link"},{default:d(()=>s[2]||(s[2]=[o("文章管理",-1)])),_:1,__:[2]})]),t("main",C,[t("div",N,[s[10]||(s[10]=t("h2",null,"仪表板",-1)),s[11]||(s[11]=t("p",null,"欢迎来到个人博客管理系统！",-1)),t("div",B,[s[5]||(s[5]=t("h3",null,"快速操作",-1)),t("div",S,[l(i,{to:"/admin/articles/new",class:"action-btn primary"},{default:d(()=>s[3]||(s[3]=[o("新建文章",-1)])),_:1,__:[3]}),l(i,{to:"/admin/articles",class:"action-btn"},{default:d(()=>s[4]||(s[4]=[o("管理文章",-1)])),_:1,__:[4]})])]),e(n).user?(_(),r("div",q,[s[9]||(s[9]=t("h3",null,"用户信息",-1)),t("p",null,[s[6]||(s[6]=t("strong",null,"用户名:",-1)),o(" "+a(e(n).user.username),1)]),t("p",null,[s[7]||(s[7]=t("strong",null,"邮箱:",-1)),o(" "+a(e(n).user.email),1)]),t("p",null,[s[8]||(s[8]=t("strong",null,"用户ID:",-1)),o(" "+a(e(n).user.id),1)])])):m("",!0)])])])}}});const E=b(D,[["__scopeId","data-v-737d7f9d"]]);export{E as default};
