"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCommentStats = exports.getPendingComments = exports.getUserComments = exports.getArticleComments = exports.updateCommentStatus = exports.deleteComment = exports.updateComment = exports.createComment = exports.getComment = exports.getComments = void 0;
const Comment_1 = require("../models/Comment");
const Article_1 = require("../models/Article");
const User_1 = require("../models/User");
const errorHandler_1 = require("../middleware/errorHandler");
const joi_1 = __importDefault(require("joi"));
const createCommentSchema = joi_1.default.object({
    content: joi_1.default.string().min(1).max(2000).required(),
    articleId: joi_1.default.number().integer().positive().required(),
    parentId: joi_1.default.number().integer().positive().optional()
});
const updateCommentSchema = joi_1.default.object({
    content: joi_1.default.string().min(1).max(2000).required()
});
const updateCommentStatusSchema = joi_1.default.object({
    status: joi_1.default.string().valid('pending', 'approved', 'rejected').required()
});
const getComments = async (req, res, next) => {
    try {
        const { articleId, page = 1, limit = 10, status = 'approved' } = req.query;
        const pageNum = parseInt(page, 10);
        const limitNum = parseInt(limit, 10);
        const offset = (pageNum - 1) * limitNum;
        let result;
        if (articleId) {
            const articleIdNum = parseInt(articleId, 10);
            if (isNaN(articleIdNum)) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_ARTICLE_ID',
                        message: 'Valid article ID is required'
                    }
                });
                return;
            }
            result = await Comment_1.Comment.findByArticleId(articleIdNum, {
                limit: limitNum,
                offset,
                includeReplies: true
            });
        }
        else {
            const whereClause = {};
            if (status && status !== 'all') {
                whereClause.status = status;
            }
            result = await Comment_1.Comment.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: User_1.User,
                        as: 'author',
                        attributes: ['id', 'username']
                    },
                    {
                        model: Article_1.Article,
                        as: 'article',
                        attributes: ['id', 'title', 'slug']
                    }
                ],
                order: [['createdAt', 'DESC']],
                limit: limitNum,
                offset,
                distinct: true
            });
        }
        const totalPages = Math.ceil(result.count / limitNum);
        res.json({
            success: true,
            data: {
                comments: result.rows,
                pagination: {
                    page: pageNum,
                    limit: limitNum,
                    total: result.count,
                    totalPages
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getComments = getComments;
const getComment = async (req, res, next) => {
    try {
        const { id } = req.params;
        if (!id || !/^\d+$/.test(id)) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_ID',
                    message: 'Valid comment ID is required'
                }
            });
            return;
        }
        const comment = await Comment_1.Comment.findByPk(id, {
            include: [
                {
                    model: User_1.User,
                    as: 'author',
                    attributes: ['id', 'username']
                },
                {
                    model: Article_1.Article,
                    as: 'article',
                    attributes: ['id', 'title', 'slug']
                },
                {
                    model: Comment_1.Comment,
                    as: 'parent',
                    attributes: ['id', 'content'],
                    include: [
                        {
                            model: User_1.User,
                            as: 'author',
                            attributes: ['id', 'username']
                        }
                    ]
                },
                {
                    model: Comment_1.Comment,
                    as: 'replies',
                    where: { status: 'approved' },
                    required: false,
                    include: [
                        {
                            model: User_1.User,
                            as: 'author',
                            attributes: ['id', 'username']
                        }
                    ]
                }
            ]
        });
        if (!comment) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'COMMENT_NOT_FOUND',
                    message: 'Comment not found'
                }
            });
            return;
        }
        res.json({
            success: true,
            data: { comment }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getComment = getComment;
const createComment = async (req, res, next) => {
    try {
        const { error, value } = createCommentSchema.validate(req.body);
        if (error) {
            throw (0, errorHandler_1.createError)(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR');
        }
        const { content, articleId, parentId } = value;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const article = await Article_1.Article.findByPk(articleId);
        if (!article || article.status !== 'published') {
            res.status(404).json({
                success: false,
                error: {
                    code: 'ARTICLE_NOT_FOUND',
                    message: 'Published article not found'
                }
            });
            return;
        }
        if (parentId) {
            const parentComment = await Comment_1.Comment.findByPk(parentId);
            if (!parentComment || parentComment.status !== 'approved' || parentComment.articleId !== articleId) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'PARENT_COMMENT_NOT_FOUND',
                        message: 'Parent comment not found or not approved'
                    }
                });
                return;
            }
        }
        const comment = await Comment_1.Comment.create({
            content,
            articleId,
            authorId: req.user.id,
            parentId: parentId || null,
            status: 'pending'
        });
        const createdComment = await Comment_1.Comment.findByPk(comment.id, {
            include: [
                {
                    model: User_1.User,
                    as: 'author',
                    attributes: ['id', 'username']
                },
                {
                    model: Article_1.Article,
                    as: 'article',
                    attributes: ['id', 'title', 'slug']
                }
            ]
        });
        res.status(201).json({
            success: true,
            data: { comment: createdComment }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.createComment = createComment;
const updateComment = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { error, value } = updateCommentSchema.validate(req.body);
        if (error) {
            throw (0, errorHandler_1.createError)(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR');
        }
        const { content } = value;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const comment = await Comment_1.Comment.findByPk(id);
        if (!comment) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'COMMENT_NOT_FOUND',
                    message: 'Comment not found'
                }
            });
            return;
        }
        if (comment.authorId !== req.user.id) {
            res.status(403).json({
                success: false,
                error: {
                    code: 'FORBIDDEN',
                    message: 'You can only update your own comments'
                }
            });
            return;
        }
        await comment.update({
            content,
            status: 'pending'
        });
        const updatedComment = await Comment_1.Comment.findByPk(comment.id, {
            include: [
                {
                    model: User_1.User,
                    as: 'author',
                    attributes: ['id', 'username']
                },
                {
                    model: Article_1.Article,
                    as: 'article',
                    attributes: ['id', 'title', 'slug']
                }
            ]
        });
        res.json({
            success: true,
            data: { comment: updatedComment }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateComment = updateComment;
const deleteComment = async (req, res, next) => {
    try {
        const { id } = req.params;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const comment = await Comment_1.Comment.findByPk(id);
        if (!comment) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'COMMENT_NOT_FOUND',
                    message: 'Comment not found'
                }
            });
            return;
        }
        if (comment.authorId !== req.user.id) {
            res.status(403).json({
                success: false,
                error: {
                    code: 'FORBIDDEN',
                    message: 'You can only delete your own comments'
                }
            });
            return;
        }
        await comment.destroy();
        res.json({
            success: true,
            message: 'Comment deleted successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteComment = deleteComment;
const updateCommentStatus = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { error, value } = updateCommentStatusSchema.validate(req.body);
        if (error) {
            throw (0, errorHandler_1.createError)(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR');
        }
        const { status } = value;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const comment = await Comment_1.Comment.findByPk(id);
        if (!comment) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'COMMENT_NOT_FOUND',
                    message: 'Comment not found'
                }
            });
            return;
        }
        await comment.update({ status });
        const updatedComment = await Comment_1.Comment.findByPk(comment.id, {
            include: [
                {
                    model: User_1.User,
                    as: 'author',
                    attributes: ['id', 'username']
                },
                {
                    model: Article_1.Article,
                    as: 'article',
                    attributes: ['id', 'title', 'slug']
                }
            ]
        });
        res.json({
            success: true,
            data: { comment: updatedComment }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateCommentStatus = updateCommentStatus;
const getArticleComments = async (req, res, next) => {
    try {
        const { articleId } = req.params;
        const { page = 1, limit = 10 } = req.query;
        if (!articleId || !/^\d+$/.test(articleId)) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_ARTICLE_ID',
                    message: 'Valid article ID is required'
                }
            });
            return;
        }
        const pageNum = parseInt(page, 10);
        const limitNum = parseInt(limit, 10);
        const offset = (pageNum - 1) * limitNum;
        const articleIdNum = parseInt(articleId, 10);
        const article = await Article_1.Article.findByPk(articleIdNum);
        if (!article || article.status !== 'published') {
            res.status(404).json({
                success: false,
                error: {
                    code: 'ARTICLE_NOT_FOUND',
                    message: 'Published article not found'
                }
            });
            return;
        }
        const result = await Comment_1.Comment.findByArticleId(articleIdNum, {
            limit: limitNum,
            offset,
            includeReplies: true
        });
        const totalPages = Math.ceil(result.count / limitNum);
        res.json({
            success: true,
            data: {
                comments: result.rows,
                pagination: {
                    page: pageNum,
                    limit: limitNum,
                    total: result.count,
                    totalPages
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getArticleComments = getArticleComments;
const getUserComments = async (req, res, next) => {
    try {
        const { page = 1, limit = 10, status } = req.query;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const pageNum = parseInt(page, 10);
        const limitNum = parseInt(limit, 10);
        const offset = (pageNum - 1) * limitNum;
        const result = await Comment_1.Comment.findByAuthorId(req.user.id, {
            limit: limitNum,
            offset,
            status: status
        });
        const totalPages = Math.ceil(result.count / limitNum);
        res.json({
            success: true,
            data: {
                comments: result.rows,
                pagination: {
                    page: pageNum,
                    limit: limitNum,
                    total: result.count,
                    totalPages
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUserComments = getUserComments;
const getPendingComments = async (req, res, next) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const pageNum = parseInt(page, 10);
        const limitNum = parseInt(limit, 10);
        const offset = (pageNum - 1) * limitNum;
        const result = await Comment_1.Comment.findPending({
            limit: limitNum,
            offset
        });
        const totalPages = Math.ceil(result.count / limitNum);
        res.json({
            success: true,
            data: {
                comments: result.rows,
                pagination: {
                    page: pageNum,
                    limit: limitNum,
                    total: result.count,
                    totalPages
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getPendingComments = getPendingComments;
const getCommentStats = async (req, res, next) => {
    try {
        const [total, pending, approved, rejected] = await Promise.all([
            Comment_1.Comment.count(),
            Comment_1.Comment.count({ where: { status: 'pending' } }),
            Comment_1.Comment.count({ where: { status: 'approved' } }),
            Comment_1.Comment.count({ where: { status: 'rejected' } })
        ]);
        res.json({
            success: true,
            data: {
                stats: {
                    total,
                    pending,
                    approved,
                    rejected
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getCommentStats = getCommentStats;
//# sourceMappingURL=comment.js.map