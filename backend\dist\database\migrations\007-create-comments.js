"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('comments', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        content: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: false,
            comment: '评论内容'
        },
        status: {
            type: sequelize_1.DataTypes.ENUM('pending', 'approved', 'rejected'),
            allowNull: false,
            defaultValue: 'pending',
            comment: '评论状态：pending-待审核，approved-已批准，rejected-已拒绝'
        },
        article_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '关联的文章ID',
            references: {
                model: 'articles',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        author_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '评论作者ID',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        parent_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '父评论ID，用于回复功能',
            references: {
                model: 'comments',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    try {
        await queryInterface.addIndex('comments', ['article_id'], {
            name: 'idx_comments_article_id'
        });
    }
    catch {
        console.log('Index idx_comments_article_id already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('comments', ['author_id'], {
            name: 'idx_comments_author_id'
        });
    }
    catch {
        console.log('Index idx_comments_author_id already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('comments', ['parent_id'], {
            name: 'idx_comments_parent_id'
        });
    }
    catch {
        console.log('Index idx_comments_parent_id already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('comments', ['status'], {
            name: 'idx_comments_status'
        });
    }
    catch {
        console.log('Index idx_comments_status already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('comments', ['created_at'], {
            name: 'idx_comments_created_at'
        });
    }
    catch {
        console.log('Index idx_comments_created_at already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('comments', ['article_id', 'status'], {
            name: 'idx_comments_article_status'
        });
    }
    catch {
        console.log('Index idx_comments_article_status already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('comments', ['article_id', 'parent_id'], {
            name: 'idx_comments_article_parent'
        });
    }
    catch {
        console.log('Index idx_comments_article_parent already exists, skipping...');
    }
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('comments');
};
exports.down = down;
//# sourceMappingURL=007-create-comments.js.map