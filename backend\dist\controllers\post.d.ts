import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: number;
        username: string;
        email: string;
    };
}
export declare class PostController {
    static getPosts(req: Request, res: Response): Promise<void>;
    static getPost(req: Request, res: Response): Promise<void>;
    static createPost(req: AuthenticatedRequest, res: Response): Promise<void>;
    static updatePost(req: AuthenticatedRequest, res: Response): Promise<void>;
    static deletePost(req: AuthenticatedRequest, res: Response): Promise<void>;
    static toggleLike(req: AuthenticatedRequest, res: Response): Promise<void>;
    static getLikes(req: Request, res: Response): Promise<void>;
}
export {};
//# sourceMappingURL=post.d.ts.map