{"version": 3, "file": "IOptionalBrowserSettings.d.ts", "sourceRoot": "", "sources": ["../../../src/browser/types/IOptionalBrowserSettings.ts"], "names": [], "mappings": "AAAA,OAAO,uBAAuB,MAAM,qCAAqC,CAAC;AAC1E,OAAO,sCAAsC,MAAM,oDAAoD,CAAC;AACxG,OAAO,iBAAiB,MAAM,wCAAwC,CAAC;AACvE,OAAO,cAAc,MAAM,qCAAqC,CAAC;AAEjE,MAAM,CAAC,OAAO,WAAW,wBAAwB;IAChD,sCAAsC;IACtC,2BAA2B,CAAC,EAAE,OAAO,CAAC;IAEtC,wCAAwC;IACxC,4BAA4B,CAAC,EAAE,OAAO,CAAC;IAEvC,iCAAiC;IACjC,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAEhC,yCAAyC;IACzC,6BAA6B,CAAC,EAAE,OAAO,CAAC;IAExC,8CAA8C;IAC9C,kCAAkC,CAAC,EAAE,OAAO,CAAC;IAE7C,0BAA0B;IAC1B,KAAK,CAAC,EAAE;QACP,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,eAAe,CAAC,EAAE,MAAM,CAAC;QACzB,qBAAqB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC;IAEF;;OAEG;IACH,KAAK,CAAC,EAAE;QACP;;;;WAIG;QACH,uBAAuB,CAAC,EAAE,OAAO,CAAC;QAElC;;;;WAIG;QACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;QAE3B;;WAEG;QACH,WAAW,CAAC,EAAE,iBAAiB,GAAG,IAAI,CAAC;QAEvC;;WAEG;QACH,cAAc,CAAC,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;KACzC,CAAC;IAEF;;;;OAIG;IACH,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAEhC;;OAEG;IACH,YAAY,CAAC,EAAE,uBAAuB,CAAC;IAEvC;;OAEG;IACH,4BAA4B,CAAC,EAAE,OAAO,CAAC;IAEvC;;OAEG;IACH,wBAAwB,CAAC,EAAE,OAAO,CAAC;IAEnC;;OAEG;IACH,UAAU,CAAC,EAAE;QACZ,sEAAsE;QACtE,0BAA0B,CAAC,EAAE,OAAO,CAAC;QAErC,4EAA4E;QAC5E,2BAA2B,CAAC,EAAE,OAAO,CAAC;QAEtC,iFAAiF;QACjF,0BAA0B,CAAC,EAAE,OAAO,CAAC;QAErC,8HAA8H;QAC9H,uBAAuB,CAAC,EAAE,OAAO,CAAC;QAElC,mDAAmD;QACnD,iBAAiB,CAAC,EAAE,sCAAsC,CAAC;KAC3D,CAAC;IAEF;;OAEG;IACH,SAAS,CAAC,EAAE;QACX,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,cAAc,CAAC,EAAE,MAAM,CAAC;KACxB,CAAC;IAEF;;OAEG;IACH,MAAM,CAAC,EAAE;QACR,kBAAkB,CAAC,EAAE,MAAM,CAAC;QAC5B,oBAAoB,CAAC,EAAE,MAAM,CAAC;QAC9B,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,YAAY,CAAC,EAAE,MAAM,CAAC;KACtB,CAAC;IAEF;;OAEG;IACH,KAAK,CAAC,EAAE;QACP,sBAAsB,CAAC,EAAE,MAAM,CAAC;KAChC,CAAC;CACF"}