import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: number;
        username: string;
        email: string;
    };
}
export declare class TimelineController {
    static getPersonalTimeline(req: AuthenticatedRequest, res: Response): Promise<void>;
    static getPublicTimeline(req: Request, res: Response): Promise<void>;
    static getUserTimeline(req: Request, res: Response): Promise<void>;
    static getTrendingPosts(req: Request, res: Response): Promise<void>;
}
export {};
//# sourceMappingURL=timeline.d.ts.map