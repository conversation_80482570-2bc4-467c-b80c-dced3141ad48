import request from 'supertest'
import express from 'express'
import { sequelize } from '../../config/database'
import { User, Article, Tag } from '../../models'
import { generateToken } from '../../services/auth'
import articleRoutes from '../../routes/article'
import { errorHandler } from '../../middleware/errorHandler'

// Create test app
const app = express()
app.use(express.json())
app.use('/api/articles', articleRoutes)
app.use(errorHandler)

describe('Article Controller', () => {
  let testUser: User
  let authToken: string
  let testArticle: Article

  beforeAll(async () => {
    // Sync database
    await sequelize.sync({ force: true })
    
    // Create test user
    testUser = await User.create({
      username: 'testuser',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword'
    })

    // Generate auth token
    authToken = generateToken({ id: testUser.id, username: testUser.username, email: testUser.email })
  })

  beforeEach(async () => {
    // Clean up articles and tags before each test
    await Article.destroy({ where: {} })
    await Tag.destroy({ where: {} })
  })

  afterAll(async () => {
    await sequelize.close()
  })

  describe('POST /api/articles', () => {
    it('should create a new article', async () => {
      const articleData = {
        title: 'Test Article',
        content: 'This is test content',
        excerpt: 'Test excerpt',
        status: 'draft',
        tags: ['test', 'article']
      }

      const response = await request(app)
        .post('/api/articles')
        .set('Authorization', `Bearer ${authToken}`)
        .send(articleData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.article.title).toBe(articleData.title)
      expect(response.body.data.article.content).toBe(articleData.content)
      expect(response.body.data.article.status).toBe(articleData.status)
      expect(response.body.data.article.slug).toBe('test-article')
      expect(response.body.data.article.tags).toHaveLength(2)
    })

    it('should require authentication', async () => {
      const articleData = {
        title: 'Test Article',
        content: 'This is test content'
      }

      await request(app)
        .post('/api/articles')
        .send(articleData)
        .expect(401)
    })

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/articles')
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('GET /api/articles', () => {
    beforeEach(async () => {
      // Create test articles
      testArticle = await Article.create({
        title: 'Published Article',
        content: 'Published content',
        status: 'published',
        authorId: testUser.id
      })

      await Article.create({
        title: 'Draft Article',
        content: 'Draft content',
        status: 'draft',
        authorId: testUser.id
      })
    })

    it('should get all articles with pagination', async () => {
      const response = await request(app)
        .get('/api/articles')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.articles).toHaveLength(2)
      expect(response.body.data.pagination.totalItems).toBe(2)
      expect(response.body.data.pagination.currentPage).toBe(1)
    })

    it('should filter articles by status', async () => {
      const response = await request(app)
        .get('/api/articles?status=published')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.articles).toHaveLength(1)
      expect(response.body.data.articles[0].status).toBe('published')
    })

    it('should search articles by title and content', async () => {
      const response = await request(app)
        .get('/api/articles?search=Published')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.articles).toHaveLength(1)
      expect(response.body.data.articles[0].title).toContain('Published')
    })

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/articles?page=1&limit=1')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.articles).toHaveLength(1)
      expect(response.body.data.pagination.itemsPerPage).toBe(1)
      expect(response.body.data.pagination.hasNextPage).toBe(true)
    })
  })

  describe('GET /api/articles/:id', () => {
    beforeEach(async () => {
      testArticle = await Article.create({
        title: 'Test Article',
        content: 'Test content',
        status: 'published',
        authorId: testUser.id
      })
    })

    it('should get article by ID', async () => {
      const response = await request(app)
        .get(`/api/articles/${testArticle.id}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.article.id).toBe(testArticle.id)
      expect(response.body.data.article.title).toBe(testArticle.title)
    })

    it('should get article by slug', async () => {
      const response = await request(app)
        .get(`/api/articles/${testArticle.slug}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.article.slug).toBe(testArticle.slug)
    })

    it('should return 404 for non-existent article', async () => {
      const response = await request(app)
        .get('/api/articles/999999')
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('ARTICLE_NOT_FOUND')
    })
  })

  describe('PUT /api/articles/:id', () => {
    beforeEach(async () => {
      testArticle = await Article.create({
        title: 'Original Title',
        content: 'Original content',
        status: 'draft',
        authorId: testUser.id
      })
    })

    it('should update article', async () => {
      const updateData = {
        title: 'Updated Title',
        content: 'Updated content',
        status: 'published'
      }

      const response = await request(app)
        .put(`/api/articles/${testArticle.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.article.title).toBe(updateData.title)
      expect(response.body.data.article.content).toBe(updateData.content)
      expect(response.body.data.article.status).toBe(updateData.status)
    })

    it('should require authentication', async () => {
      await request(app)
        .put(`/api/articles/${testArticle.id}`)
        .send({ title: 'Updated Title' })
        .expect(401)
    })

    it('should only allow author to update', async () => {
      // Create another user
      const otherUser = await User.create({
        username: 'otheruserupdate',
        email: '<EMAIL>',
        passwordHash: 'hashedpassword'
      })

      const otherToken = generateToken({ id: otherUser.id, username: otherUser.username, email: otherUser.email })

      const response = await request(app)
        .put(`/api/articles/${testArticle.id}`)
        .set('Authorization', `Bearer ${otherToken}`)
        .send({ title: 'Updated Title' })
        .expect(403)

      expect(response.body.error.code).toBe('FORBIDDEN')
    })
  })

  describe('DELETE /api/articles/:id', () => {
    beforeEach(async () => {
      testArticle = await Article.create({
        title: 'Test Article',
        content: 'Test content',
        status: 'draft',
        authorId: testUser.id
      })
    })

    it('should delete article', async () => {
      const response = await request(app)
        .delete(`/api/articles/${testArticle.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)

      // Verify article is deleted
      const deletedArticle = await Article.findByPk(testArticle.id)
      expect(deletedArticle).toBeNull()
    })

    it('should require authentication', async () => {
      await request(app)
        .delete(`/api/articles/${testArticle.id}`)
        .expect(401)
    })

    it('should only allow author to delete', async () => {
      // Create another user
      const otherUser = await User.create({
        username: 'otheruserdelete',
        email: '<EMAIL>',
        passwordHash: 'hashedpassword'
      })

      const otherToken = generateToken({ id: otherUser.id, username: otherUser.username, email: otherUser.email })

      const response = await request(app)
        .delete(`/api/articles/${testArticle.id}`)
        .set('Authorization', `Bearer ${otherToken}`)
        .expect(403)

      expect(response.body.error.code).toBe('FORBIDDEN')
    })
  })

  describe('POST /api/articles/:id/publish', () => {
    beforeEach(async () => {
      testArticle = await Article.create({
        title: 'Draft Article',
        content: 'Draft content',
        status: 'draft',
        authorId: testUser.id
      })
    })

    it('should publish article', async () => {
      const response = await request(app)
        .post(`/api/articles/${testArticle.id}/publish`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.article.status).toBe('published')
      expect(response.body.data.article.publishedAt).toBeTruthy()
    })

    it('should require authentication', async () => {
      await request(app)
        .post(`/api/articles/${testArticle.id}/publish`)
        .expect(401)
    })
  })

  describe('POST /api/articles/:id/unpublish', () => {
    beforeEach(async () => {
      testArticle = await Article.create({
        title: 'Published Article',
        content: 'Published content',
        status: 'published',
        publishedAt: new Date(),
        authorId: testUser.id
      })
    })

    it('should unpublish article', async () => {
      const response = await request(app)
        .post(`/api/articles/${testArticle.id}/unpublish`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.article.status).toBe('draft')
      expect(response.body.data.article.publishedAt).toBeNull()
    })

    it('should require authentication', async () => {
      await request(app)
        .post(`/api/articles/${testArticle.id}/unpublish`)
        .expect(401)
    })
  })
})