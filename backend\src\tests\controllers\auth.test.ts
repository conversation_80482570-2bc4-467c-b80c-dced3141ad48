import request from 'supertest'
import express from 'express'
import cors from 'cors'
import { login, logout, getProfile, refreshToken, validateToken } from '../../controllers/auth'
import { AuthService } from '../../services/auth'
import { authenticateToken } from '../../middleware/auth'
import { errorHandler } from '../../middleware/errorHandler'

// Mock the AuthService
jest.mock('../../services/auth')
const MockedAuthService = AuthService as jest.Mocked<typeof AuthService>

// Create a test app
const app = express()
app.use(cors())
app.use(express.json())

// Add auth routes
app.post('/api/auth/login', login)
app.post('/api/auth/logout', logout)
app.get('/api/auth/profile', authenticateToken, getProfile)
app.post('/api/auth/refresh', refreshToken)
app.post('/api/auth/validate', validateToken)

// Error handler
app.use(errorHandler)

describe('Auth Controller', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('POST /api/auth/login', () => {
    it('should login successfully with valid credentials', async () => {
      const mockAuthResult = {
        success: true,
        user: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>'
        },
        token: 'mock-jwt-token'
      }

      MockedAuthService.login.mockResolvedValue(mockAuthResult)

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'password123'
        })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.user).toEqual(mockAuthResult.user)
      expect(response.body.data.token).toBe(mockAuthResult.token)
      expect(response.body.message).toBe('Login successful')
    })

    it('should return 401 for invalid credentials', async () => {
      const mockAuthResult = {
        success: false,
        message: 'Invalid username or password'
      }

      MockedAuthService.login.mockResolvedValue(mockAuthResult)

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'wrongpassword'
        })

      expect(response.status).toBe(401)
      expect(response.body.success).toBe(false)
      expect(response.body.error.message).toBe('Invalid username or password')
    })

    it('should return 400 for invalid request body', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'ab', // Too short
          password: '123' // Too short
        })

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })

    it('should return 400 for missing fields', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser'
          // Missing password
        })

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
    })
  })

  describe('POST /api/auth/logout', () => {
    it('should logout successfully', async () => {
      const response = await request(app)
        .post('/api/auth/logout')

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Logout successful')
    })
  })

  describe('GET /api/auth/profile', () => {
    it('should return user profile when authenticated', async () => {
      const mockUser = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>'
      }

      MockedAuthService.validateToken.mockResolvedValue({
        success: true,
        user: mockUser
      })

      MockedAuthService.extractTokenFromHeader.mockReturnValue('mock-token')

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer mock-token')

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.user).toEqual(mockUser)
    })

    it('should return 401 when not authenticated', async () => {
      MockedAuthService.extractTokenFromHeader.mockReturnValue(null)

      const response = await request(app)
        .get('/api/auth/profile')

      expect(response.status).toBe(401)
      expect(response.body.success).toBe(false)
    })
  })

  describe('POST /api/auth/refresh', () => {
    it('should refresh token successfully', async () => {
      const mockRefreshResult = {
        success: true,
        user: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>'
        },
        token: 'new-mock-jwt-token'
      }

      MockedAuthService.refreshToken.mockResolvedValue(mockRefreshResult)

      const response = await request(app)
        .post('/api/auth/refresh')
        .send({
          token: 'old-mock-token'
        })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.token).toBe('new-mock-jwt-token')
      expect(response.body.message).toBe('Token refreshed successfully')
    })

    it('should return 401 for invalid token', async () => {
      const mockRefreshResult = {
        success: false,
        message: 'Invalid token'
      }

      MockedAuthService.refreshToken.mockResolvedValue(mockRefreshResult)

      const response = await request(app)
        .post('/api/auth/refresh')
        .send({
          token: 'invalid-token'
        })

      expect(response.status).toBe(401)
      expect(response.body.success).toBe(false)
    })

    it('should return 400 for missing token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({})

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
    })
  })

  describe('POST /api/auth/validate', () => {
    it('should validate token successfully', async () => {
      const mockUser = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>'
      }

      MockedAuthService.extractTokenFromHeader.mockReturnValue('valid-token')
      MockedAuthService.validateToken.mockResolvedValue({
        success: true,
        user: mockUser
      })

      const response = await request(app)
        .post('/api/auth/validate')
        .set('Authorization', 'Bearer valid-token')

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.user).toEqual(mockUser)
      expect(response.body.data.valid).toBe(true)
    })

    it('should return 401 for invalid token', async () => {
      MockedAuthService.extractTokenFromHeader.mockReturnValue('invalid-token')
      MockedAuthService.validateToken.mockResolvedValue({
        success: false,
        message: 'Invalid token'
      })

      const response = await request(app)
        .post('/api/auth/validate')
        .set('Authorization', 'Bearer invalid-token')

      expect(response.status).toBe(401)
      expect(response.body.success).toBe(false)
    })

    it('should return 400 when no token provided', async () => {
      MockedAuthService.extractTokenFromHeader.mockReturnValue(null)

      const response = await request(app)
        .post('/api/auth/validate')

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
      expect(response.body.error.message).toBe('Token is required')
    })
  })
})