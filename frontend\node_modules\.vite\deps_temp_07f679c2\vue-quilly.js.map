{"version": 3, "sources": ["../../vue-quilly/dist/vue-quilly.js"], "sourcesContent": ["import { defineComponent as H, ref as u, watch as d, onBeforeUnmount as M, createElementBlock as v, openBlock as x } from \"vue\";\nconst y = /* @__PURE__ */ H({\n  __name: \"QuillyEditor\",\n  props: {\n    modelValue: {},\n    options: {},\n    isSemanticHtmlModel: { type: Boolean }\n  },\n  emits: [\"update:modelValue\", \"text-change\", \"selection-change\", \"editor-change\", \"blur\", \"focus\", \"ready\"],\n  setup(p, { expose: f, emit: h }) {\n    const i = p, l = h;\n    let n = null;\n    const r = u(), c = u(), m = (t) => {\n      c.value = i.modelValue;\n      const e = t.getContents(), o = t.clipboard.convert({ html: i.modelValue ?? \"\" });\n      t.setContents(o), l(\"text-change\", { delta: o, oldContent: e, source: \"api\" });\n    }, g = (t) => {\n      const e = new t(r.value, i.options);\n      return i.modelValue && m(e), e.on(\"selection-change\", (o, a, s) => {\n        l(o ? \"focus\" : \"blur\", e), l(\"selection-change\", { range: o, oldRange: a, source: s });\n      }), e.on(\"text-change\", (o, a, s) => {\n        c.value = i.isSemanticHtmlModel ? e.getSemanticHTML() : e.root.innerHTML, l(\"text-change\", { delta: o, oldContent: a, source: s });\n      }), e.on(\"editor-change\", (o) => {\n        l(\"editor-change\", o);\n      }), l(\"ready\", e), n = e, e;\n    };\n    return d(\n      () => i.modelValue,\n      (t) => {\n        n && (t && t !== c.value ? (m(n), c.value = i.isSemanticHtmlModel ? n.getSemanticHTML() : n.root.innerHTML) : t || n.setContents([]));\n      }\n    ), d(c, (t, e) => {\n      n && (t && t !== e ? l(\"update:modelValue\", t) : t || n.setContents([]));\n    }), M(() => {\n      n = null;\n    }), f({\n      initialize: g\n    }), (t, e) => (x(), v(\"div\", {\n      ref_key: \"container\",\n      ref: r\n    }, null, 512));\n  }\n});\nexport {\n  y as QuillyEditor\n};\n"], "mappings": ";;;;;;;;;;;AACA,IAAM,IAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,YAAY,CAAC;AAAA,IACb,SAAS,CAAC;AAAA,IACV,qBAAqB,EAAE,MAAM,QAAQ;AAAA,EACvC;AAAA,EACA,OAAO,CAAC,qBAAqB,eAAe,oBAAoB,iBAAiB,QAAQ,SAAS,OAAO;AAAA,EACzG,MAAM,GAAG,EAAE,QAAQ,GAAG,MAAM,EAAE,GAAG;AAC/B,UAAM,IAAI,GAAG,IAAI;AACjB,QAAI,IAAI;AACR,UAAM,IAAI,IAAE,GAAG,IAAI,IAAE,GAAG,IAAI,CAAC,MAAM;AACjC,QAAE,QAAQ,EAAE;AACZ,YAAM,IAAI,EAAE,YAAY,GAAG,IAAI,EAAE,UAAU,QAAQ,EAAE,MAAM,EAAE,cAAc,GAAG,CAAC;AAC/E,QAAE,YAAY,CAAC,GAAG,EAAE,eAAe,EAAE,OAAO,GAAG,YAAY,GAAG,QAAQ,MAAM,CAAC;AAAA,IAC/E,GAAG,IAAI,CAAC,MAAM;AACZ,YAAM,IAAI,IAAI,EAAE,EAAE,OAAO,EAAE,OAAO;AAClC,aAAO,EAAE,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,oBAAoB,CAAC,GAAG,GAAG,MAAM;AACjE,UAAE,IAAI,UAAU,QAAQ,CAAC,GAAG,EAAE,oBAAoB,EAAE,OAAO,GAAG,UAAU,GAAG,QAAQ,EAAE,CAAC;AAAA,MACxF,CAAC,GAAG,EAAE,GAAG,eAAe,CAAC,GAAG,GAAG,MAAM;AACnC,UAAE,QAAQ,EAAE,sBAAsB,EAAE,gBAAgB,IAAI,EAAE,KAAK,WAAW,EAAE,eAAe,EAAE,OAAO,GAAG,YAAY,GAAG,QAAQ,EAAE,CAAC;AAAA,MACnI,CAAC,GAAG,EAAE,GAAG,iBAAiB,CAAC,MAAM;AAC/B,UAAE,iBAAiB,CAAC;AAAA,MACtB,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,IAAI,GAAG;AAAA,IAC5B;AACA,WAAO;AAAA,MACL,MAAM,EAAE;AAAA,MACR,CAAC,MAAM;AACL,cAAM,KAAK,MAAM,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,sBAAsB,EAAE,gBAAgB,IAAI,EAAE,KAAK,aAAa,KAAK,EAAE,YAAY,CAAC,CAAC;AAAA,MACrI;AAAA,IACF,GAAG,MAAE,GAAG,CAAC,GAAG,MAAM;AAChB,YAAM,KAAK,MAAM,IAAI,EAAE,qBAAqB,CAAC,IAAI,KAAK,EAAE,YAAY,CAAC,CAAC;AAAA,IACxE,CAAC,GAAG,gBAAE,MAAM;AACV,UAAI;AAAA,IACN,CAAC,GAAG,EAAE;AAAA,MACJ,YAAY;AAAA,IACd,CAAC,GAAG,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC3B,SAAS;AAAA,MACT,KAAK;AAAA,IACP,GAAG,MAAM,GAAG;AAAA,EACd;AACF,CAAC;", "names": []}