import { User } from './User';
import { Article } from './Article';
import { Tag } from './Tag';
import { ArticleTag } from './ArticleTag';
import { Category } from './Category';
import { Comment } from './Comment';
import { Post } from './Post';
import { PostLike } from './PostLike';
export { User, Article, Tag, ArticleTag, Category, Comment, Post, PostLike };
export declare const syncModels: (force?: boolean) => Promise<void>;
//# sourceMappingURL=index.d.ts.map