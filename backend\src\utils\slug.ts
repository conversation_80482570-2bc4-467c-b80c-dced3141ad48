/**
 * 生成URL友好的slug字符串
 * @param text - 需要转换为slug的原始文本
 * @returns 转换后的slug字符串，只包含小写字母、数字和连字符
 */
export const generateSlug = (text: string): string => {
  // 中文字符转换映射
  const chineseMap: { [key: string]: string } = {
    '前端开发': 'frontend-dev',
    '后端开发': 'backend-dev',
    '全栈开发': 'fullstack-dev',
    'Web开发': 'web-dev',
    '数据库': 'database',
    '云计算': 'cloud-computing',
    '微服务': 'microservices',
    'API设计': 'api-design',
    '性能优化': 'performance-optimization',
    '代码规范': 'code-standards',
    '项目管理': 'project-management',
    '敏捷开发': 'agile-development',
    '测试驱动开发': 'test-driven-development',
    '持续集成': 'continuous-integration',
    '开源项目': 'open-source-projects',
    // 新增更多常用中文标签
    'JavaScript': 'javascript',
    'TypeScript': 'typescript',
    'Vue.js': 'vuejs',
    'React': 'react',
    'Angular': 'angular',
    'Node.js': 'nodejs',
    'Python': 'python',
    'Java': 'java',
    'Go': 'golang',
    'Rust': 'rust',
    'C++': 'cpp',
    'C#': 'csharp',
    'PHP': 'php',
    'Ruby': 'ruby',
    'Swift': 'swift',
    'Kotlin': 'kotlin',
    '移动开发': 'mobile-dev',
    'iOS开发': 'ios-dev',
    'Android开发': 'android-dev',
    '小程序': 'miniprogram',
    '人工智能': 'ai',
    '机器学习': 'machine-learning',
    '深度学习': 'deep-learning',
    '区块链': 'blockchain',
    '物联网': 'iot',
    '大数据': 'big-data',
    '算法': 'algorithm',
    '数据结构': 'data-structure',
    '网络安全': 'cybersecurity',
    '运维': 'devops',
    '容器化': 'containerization',
    'Docker': 'docker',
    'Kubernetes': 'kubernetes',
    'Linux': 'linux',
    'Windows': 'windows',
    'macOS': 'macos',
    '开发工具': 'dev-tools',
    '编辑器': 'editor',
    '版本控制': 'version-control',
    'Git': 'git',
    '测试': 'testing',
    '单元测试': 'unit-testing',
    '集成测试': 'integration-testing',
    '自动化测试': 'automation-testing',
    '前端框架': 'frontend-framework',
    '后端框架': 'backend-framework',
    '设计模式': 'design-pattern',
    '架构设计': 'architecture-design',
    '系统设计': 'system-design',
    '技术分享': 'tech-sharing',
    '学习笔记': 'study-notes',
    '教程': 'tutorial',
    '最佳实践': 'best-practices'
  }

  // 如果是中文标签，使用预定义映射
  if (chineseMap[text]) {
    return chineseMap[text]
  }

  // 检查是否包含中文字符
  const hasChinese = /[\u4e00-\u9fff]/.test(text)
  if (hasChinese) {
    // 对于未预定义的中文标签，尝试提取英文部分或使用拼音转换
    let slug = text
      .replace(/[\u4e00-\u9fff]/g, '') // 移除中文字符
      .toLowerCase()
      .trim()
      .replace(/[\s\W-]+/g, '-')
      .replace(/^-+|-+$/g, '')

    // 如果移除中文后没有有效字符，使用时间戳
    if (!slug || slug === '') {
      slug = `chinese-tag-${Date.now()}`
    }

    return slug
  }

  // 处理英文和其他字符
  let slug = text
    .toLowerCase() // 转换为小写
    .trim() // 去除首尾空格
    .replace(/[\s\W-]+/g, '-') // 将空格和非单词字符替换为连字符
    .replace(/^-+|-+$/g, '') // 去除开头和结尾的连字符

  // 如果生成的slug为空，使用时间戳作为后备
  if (!slug || slug === '') {
    slug = `tag-${Date.now()}`
  }

  return slug
}

/**
 * 确保生成唯一的slug，如果存在冲突则添加数字后缀
 * @param baseSlug - 基础slug字符串
 * @param checkExists - 检查slug是否已存在的异步函数
 * @returns 唯一的slug字符串
 */
export const ensureUniqueSlug = async (
  baseSlug: string,
  checkExists: (slug: string) => Promise<boolean>
): Promise<string> => {
  let slug = baseSlug
  let counter = 1

  // 循环检查slug是否存在，如果存在则添加递增的数字后缀
  while (await checkExists(slug)) {
    slug = `${baseSlug}-${counter}`
    counter++
  }

  return slug
}