import { Request, Response, NextFunction } from 'express'
import { Comment } from '../models/Comment'
import { Article } from '../models/Article'
import { User } from '../models/User'
import { createError } from '../middleware/errorHandler'
import Joi from 'joi'


/**
 * 扩展 Express 的 Request 接口，添加用户认证信息
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
  }
}


/**
 * 创建评论请求参数的校验规则
 */
const createCommentSchema = Joi.object({
  content: Joi.string().min(1).max(2000).required(),
  articleId: Joi.number().integer().positive().required(),
  parentId: Joi.number().integer().positive().optional()
})

/**
 * 更新评论请求参数的校验规则
 */
const updateCommentSchema = Joi.object({
  content: Joi.string().min(1).max(2000).required()
})

/**
 * 更新评论状态请求参数的校验规则
 */
const updateCommentStatusSchema = Joi.object({
  status: Joi.string().valid('pending', 'approved', 'rejected').required()
})


/**
 * 获取评论列表
 * @param req - Express 请求对象，包含查询参数：articleId, page, limit, status
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getComments = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { articleId, page = 1, limit = 10, status = 'approved' } = req.query

    const pageNum = parseInt(page as string, 10)
    const limitNum = parseInt(limit as string, 10)
    const offset = (pageNum - 1) * limitNum

    let result: { rows: Comment[], count: number }

    if (articleId) {
      // 获取指定文章的评论
      const articleIdNum = parseInt(articleId as string, 10)
      if (isNaN(articleIdNum)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_ARTICLE_ID',
            message: 'Valid article ID is required'
          }
        })
        return
      }

      result = await Comment.findByArticleId(articleIdNum, {
        limit: limitNum,
        offset,
        includeReplies: true
      })
    } else {
      // 获取所有评论（管理员功能）
      const whereClause: any = {}
      if (status && status !== 'all') {
        whereClause.status = status
      }

      result = await Comment.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['id', 'username']
          },
          {
            model: Article,
            as: 'article',
            attributes: ['id', 'title', 'slug']
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: limitNum,
        offset,
        distinct: true
      })
    }

    const totalPages = Math.ceil(result.count / limitNum)

    res.json({
      success: true,
      data: {
        comments: result.rows,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: result.count,
          totalPages
        }
      }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 根据ID获取单个评论
 * @param req - Express 请求对象，包含评论ID参数
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getComment = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    if (!id || !/^\d+$/.test(id)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ID',
          message: 'Valid comment ID is required'
        }
      })
      return
    }

    const comment = await Comment.findByPk(id, {
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username']
        },
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'slug']
        },
        {
          model: Comment,
          as: 'parent',
          attributes: ['id', 'content'],
          include: [
            {
              model: User,
              as: 'author',
              attributes: ['id', 'username']
            }
          ]
        },
        {
          model: Comment,
          as: 'replies',
          where: { status: 'approved' },
          required: false,
          include: [
            {
              model: User,
              as: 'author',
              attributes: ['id', 'username']
            }
          ]
        }
      ]
    })

    if (!comment) {
      res.status(404).json({
        success: false,
        error: {
          code: 'COMMENT_NOT_FOUND',
          message: 'Comment not found'
        }
      })
      return
    }

    res.json({
      success: true,
      data: { comment }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 创建新评论
 * @param req - AuthenticatedRequest 请求对象，包含评论内容、文章ID等信息以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const createComment = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // 校验请求体参数
    const { error, value } = createCommentSchema.validate(req.body)
    if (error) {
      throw createError(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR')
    }

    const { content, articleId, parentId } = value

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    // 验证文章是否存在且已发布
    const article = await Article.findByPk(articleId)
    if (!article || article.status !== 'published') {
      res.status(404).json({
        success: false,
        error: {
          code: 'ARTICLE_NOT_FOUND',
          message: 'Published article not found'
        }
      })
      return
    }

    // 如果是回复评论，验证父评论是否存在且已批准
    if (parentId) {
      const parentComment = await Comment.findByPk(parentId)
      if (!parentComment || parentComment.status !== 'approved' || parentComment.articleId !== articleId) {
        res.status(404).json({
          success: false,
          error: {
            code: 'PARENT_COMMENT_NOT_FOUND',
            message: 'Parent comment not found or not approved'
          }
        })
        return
      }
    }

    // 创建评论
    const comment = await Comment.create({
      content,
      articleId,
      authorId: req.user.id,
      parentId: parentId || null,
      status: 'pending' // 默认待审核状态
    })

    // 获取完整的评论信息
    const createdComment = await Comment.findByPk(comment.id, {
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username']
        },
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'slug']
        }
      ]
    })

    res.status(201).json({
      success: true,
      data: { comment: createdComment }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 更新评论内容
 * @param req - AuthenticatedRequest 请求对象，包含要更新的评论ID和内容以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const updateComment = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    // 校验请求体参数
    const { error, value } = updateCommentSchema.validate(req.body)
    if (error) {
      throw createError(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR')
    }

    const { content } = value

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    const comment = await Comment.findByPk(id)

    if (!comment) {
      res.status(404).json({
        success: false,
        error: {
          code: 'COMMENT_NOT_FOUND',
          message: 'Comment not found'
        }
      })
      return
    }

    // 验证权限：只能更新自己的评论
    if (comment.authorId !== req.user.id) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only update your own comments'
        }
      })
      return
    }

    // 更新评论内容，状态重置为待审核
    await comment.update({
      content,
      status: 'pending'
    })

    // 获取更新后的完整评论信息
    const updatedComment = await Comment.findByPk(comment.id, {
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username']
        },
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'slug']
        }
      ]
    })

    res.json({
      success: true,
      data: { comment: updatedComment }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 删除评论
 * @param req - AuthenticatedRequest 请求对象，包含要删除的评论ID以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const deleteComment = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    const comment = await Comment.findByPk(id)

    if (!comment) {
      res.status(404).json({
        success: false,
        error: {
          code: 'COMMENT_NOT_FOUND',
          message: 'Comment not found'
        }
      })
      return
    }

    // 验证权限：只能删除自己的评论
    if (comment.authorId !== req.user.id) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only delete your own comments'
        }
      })
      return
    }

    await comment.destroy()

    res.json({
      success: true,
      message: 'Comment deleted successfully'
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 更新评论状态（管理员功能）
 * @param req - AuthenticatedRequest 请求对象，包含要更新的评论ID和状态以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const updateCommentStatus = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    // 校验请求体参数
    const { error, value } = updateCommentStatusSchema.validate(req.body)
    if (error) {
      throw createError(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR')
    }

    const { status } = value

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    // TODO: 这里应该添加管理员权限检查
    // 目前简化处理，后续可以添加角色系统

    const comment = await Comment.findByPk(id)

    if (!comment) {
      res.status(404).json({
        success: false,
        error: {
          code: 'COMMENT_NOT_FOUND',
          message: 'Comment not found'
        }
      })
      return
    }

    // 更新评论状态
    await comment.update({ status })

    // 获取更新后的完整评论信息
    const updatedComment = await Comment.findByPk(comment.id, {
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username']
        },
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'slug']
        }
      ]
    })

    res.json({
      success: true,
      data: { comment: updatedComment }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 获取指定文章的评论列表
 * @param req - Express 请求对象，包含文章ID参数
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getArticleComments = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { articleId } = req.params
    const { page = 1, limit = 10 } = req.query

    if (!articleId || !/^\d+$/.test(articleId)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ARTICLE_ID',
          message: 'Valid article ID is required'
        }
      })
      return
    }

    const pageNum = parseInt(page as string, 10)
    const limitNum = parseInt(limit as string, 10)
    const offset = (pageNum - 1) * limitNum
    const articleIdNum = parseInt(articleId, 10)

    // 验证文章是否存在且已发布
    const article = await Article.findByPk(articleIdNum)
    if (!article || article.status !== 'published') {
      res.status(404).json({
        success: false,
        error: {
          code: 'ARTICLE_NOT_FOUND',
          message: 'Published article not found'
        }
      })
      return
    }

    const result = await Comment.findByArticleId(articleIdNum, {
      limit: limitNum,
      offset,
      includeReplies: true
    })

    const totalPages = Math.ceil(result.count / limitNum)

    res.json({
      success: true,
      data: {
        comments: result.rows,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: result.count,
          totalPages
        }
      }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 获取用户的评论列表
 * @param req - AuthenticatedRequest 请求对象，包含用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getUserComments = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { page = 1, limit = 10, status } = req.query

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    const pageNum = parseInt(page as string, 10)
    const limitNum = parseInt(limit as string, 10)
    const offset = (pageNum - 1) * limitNum

    const result = await Comment.findByAuthorId(req.user.id, {
      limit: limitNum,
      offset,
      status: status as any
    })

    const totalPages = Math.ceil(result.count / limitNum)

    res.json({
      success: true,
      data: {
        comments: result.rows,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: result.count,
          totalPages
        }
      }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 获取待审核的评论列表（管理员功能）
 * @param req - AuthenticatedRequest 请求对象，包含用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getPendingComments = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { page = 1, limit = 10 } = req.query

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    // TODO: 这里应该添加管理员权限检查
    // 目前简化处理，后续可以添加角色系统

    const pageNum = parseInt(page as string, 10)
    const limitNum = parseInt(limit as string, 10)
    const offset = (pageNum - 1) * limitNum

    const result = await Comment.findPending({
      limit: limitNum,
      offset
    })

    const totalPages = Math.ceil(result.count / limitNum)

    res.json({
      success: true,
      data: {
        comments: result.rows,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: result.count,
          totalPages
        }
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取评论统计数据（管理员功能）
 * @param req - Express 请求对象
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getCommentStats = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // 获取各状态的评论数量
    const [total, pending, approved, rejected] = await Promise.all([
      Comment.count(),
      Comment.count({ where: { status: 'pending' } }),
      Comment.count({ where: { status: 'approved' } }),
      Comment.count({ where: { status: 'rejected' } })
    ])

    res.json({
      success: true,
      data: {
        stats: {
          total,
          pending,
          approved,
          rejected
        }
      }
    })
  } catch (error) {
    next(error)
  }
}
