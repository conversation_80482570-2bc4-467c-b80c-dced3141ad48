import { Model, Optional, Association } from 'sequelize';
import { User } from './User';
import { Comment } from './Comment';
export interface PostAttributes {
    id: number;
    content: string;
    images?: string[];
    visibility: 'public' | 'private';
    location?: string;
    authorId: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface PostCreationAttributes extends Optional<PostAttributes, 'id' | 'images' | 'location' | 'createdAt' | 'updatedAt'> {
}
export declare class Post extends Model<PostAttributes, PostCreationAttributes> implements PostAttributes {
    id: number;
    content: string;
    images?: string[];
    visibility: 'public' | 'private';
    location?: string;
    authorId: number;
    createdAt: Date;
    updatedAt: Date;
    readonly author?: User;
    readonly comments?: Comment[];
    readonly likes?: any[];
    static associations: {
        author: Association<Post, User>;
        comments: Association<Post, Comment>;
        likes: Association<Post, any>;
    };
    getLikeCount(): Promise<number>;
    getCommentCount(): Promise<number>;
    isLikedByUser(userId: number): Promise<boolean>;
    canBeViewedBy(userId?: number): Promise<boolean>;
    getFullInfo(userId?: number): Promise<{
        id: number;
        content: string;
        images: string[];
        visibility: "public" | "private";
        location: string | undefined;
        authorId: number;
        author: User | undefined;
        likeCount: number;
        commentCount: number;
        isLiked: boolean;
        createdAt: Date;
        updatedAt: Date;
    }>;
}
//# sourceMappingURL=Post.d.ts.map