import jwt from 'jsonwebtoken'
import { User } from '../models/User'
import { comparePassword } from '../utils/password'

/**
 * JWT 负载接口，定义了存储在 JWT 中的用户信息
 */
export interface JWTPayload {
  id: number
  username: string
  email: string
}

/**
 * 登录凭据接口，用于用户登录时传递用户名和密码
 */
export interface LoginCredentials {
  username: string
  password: string
}

/**
 * 认证结果接口，定义了认证操作的返回格式
 */
export interface AuthResult {
  success: boolean
  user?: Omit<JWTPayload, 'password'>
  token?: string
  message?: string
}


/**
 * 生成 JWT token
 * @param payload - JWT 负载数据
 * @returns 生成的 JWT token 字符串
 * @throws 如果 JWT_SECRET 未配置则抛出错误
 */
export const generateToken = (payload: JWTPayload): string => {
  const jwtSecret = process.env.JWT_SECRET
  const jwtExpiresIn = process.env.JWT_EXPIRES_IN || '7d'

  if (!jwtSecret) {
    throw new Error('JWT_SECRET is not configured')
  }

  return jwt.sign(payload, jwtSecret, {
    expiresIn: jwtExpiresIn,
    issuer: 'personal-blog-system',
    audience: 'personal-blog-users'
  } as jwt.SignOptions)
}

/**
 * 认证服务类，提供用户认证相关功能
 */
export class AuthService {

  /**
   * 生成 JWT token
   * @param payload - JWT 负载数据
   * @returns 生成的 JWT token 字符串
   * @throws 如果 JWT_SECRET 未配置则抛出错误
   */
  static generateToken(payload: JWTPayload): string {
    const jwtSecret = process.env.JWT_SECRET
    const jwtExpiresIn = process.env.JWT_EXPIRES_IN || '7d'

    if (!jwtSecret) {
      throw new Error('JWT_SECRET is not configured')
    }

    return jwt.sign(payload, jwtSecret, {
      expiresIn: jwtExpiresIn,
      issuer: 'personal-blog-system',
      audience: 'personal-blog-users'
    } as jwt.SignOptions)
  }

  /**
   * 验证 JWT token
   * @param token - 需要验证的 JWT token
   * @returns 解码后的 JWT 负载数据
   * @throws 根据不同的验证错误抛出相应的错误信息
   */
  static verifyToken(token: string): JWTPayload {
    const jwtSecret = process.env.JWT_SECRET

    if (!jwtSecret) {
      throw new Error('JWT_SECRET is not configured')
    }

    try {
      const decoded = jwt.verify(token, jwtSecret, {
        issuer: 'personal-blog-system',
        audience: 'personal-blog-users'
      }) as JWTPayload

      return decoded
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Token has expired')
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid token')
      } else {
        throw new Error('Token verification failed')
      }
    }
  }

  /**
   * 用户登录认证
   * @param credentials - 登录凭据，包含用户名和密码
   * @returns 认证结果，包含成功状态、用户信息、token 和消息
   */
  static async login(credentials: LoginCredentials): Promise<AuthResult> {
    try {
      const { username, password } = credentials

      // 查找用户
      const user = await User.findByUsername(username)
      if (!user) {
        return {
          success: false,
          message: 'Invalid username or password'
        }
      }

      // 验证密码
      const isPasswordValid = await comparePassword(password, user.passwordHash)
      if (!isPasswordValid) {
        return {
          success: false,
          message: 'Invalid username or password'
        }
      }

      // 生成 JWT payload 和 token
      const payload: JWTPayload = {
        id: user.id,
        username: user.username,
        email: user.email
      }

      const token = generateToken(payload)

      return {
        success: true,
        user: payload,
        token
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        message: 'Authentication failed'
      }
    }
  }

  /**
   * 验证 token 并检查用户是否存在
   * @param token - 需要验证的 JWT token
   * @returns 验证结果，包含成功状态、用户信息和消息
   */
  static async validateToken(token: string): Promise<AuthResult> {
    try {
      const payload = this.verifyToken(token)

      // 检查用户是否存在
      const user = await User.findByPk(payload.id)
      if (!user) {
        return {
          success: false,
          message: 'User not found'
        }
      }

      return {
        success: true,
        user: payload
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Token validation failed'
      }
    }
  }

  /**
   * 从 Authorization 头部提取 Bearer token
   * @param authHeader - Authorization 头部字符串
   * @returns 提取的 token 字符串，如果格式不正确则返回 null
   */
  static extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader) {
      return null
    }

    const parts = authHeader.split(' ')
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null
    }

    return parts[1] || null
  }

  /**
   * 刷新 JWT token
   * @param token - 需要刷新的 JWT token
   * @returns 刷新结果，包含成功状态、用户信息和新的 token
   */
  static async refreshToken(token: string): Promise<AuthResult> {
    try {
      const payload = this.verifyToken(token)

      // 验证用户是否存在
      const user = await User.findByPk(payload.id)
      if (!user) {
        return {
          success: false,
          message: 'User not found'
        }
      }

      // 生成新的 token
      const newToken = generateToken(payload)

      return {
        success: true,
        user: payload,
        token: newToken
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Token refresh failed'
      }
    }
  }
}