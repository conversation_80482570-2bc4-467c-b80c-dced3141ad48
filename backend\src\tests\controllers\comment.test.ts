import request from 'supertest'
import express from 'express'
import cors from 'cors'
import { Comment } from '../../models/Comment'
import { User } from '../../models/User'
import { Article } from '../../models/Article'
import { AuthService } from '../../services/auth'
import commentRoutes from '../../routes/comment'
import { errorHandler } from '../../middleware/errorHandler'
import { sequelize } from '../../config/database'

// Mock the AuthService
jest.mock('../../services/auth')
const MockedAuthService = AuthService as jest.Mocked<typeof AuthService>

// Create a test app
const app = express()
app.use(cors())
app.use(express.json())
app.use('/api/comments', commentRoutes)
app.use(errorHandler)

describe('Comment Controller', () => {
  let testUser: User
  let testArticle: Article
  let authToken: string

  beforeAll(async () => {
    // 同步数据库
    await sequelize.sync({ force: true })

    // 创建测试用户
    testUser = await User.create({
      username: 'testuser',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword'
    })

    // 创建测试文章
    testArticle = await Article.create({
      title: 'Test Article',
      content: 'Test content',
      status: 'published',
      authorId: testUser.id
    })

    // 模拟认证token
    authToken = 'mock-jwt-token'

    // Mock AuthService methods
    MockedAuthService.extractTokenFromHeader.mockReturnValue(authToken)
    MockedAuthService.validateToken.mockResolvedValue({
      success: true,
      user: {
        id: testUser.id,
        username: testUser.username,
        email: testUser.email
      }
    })
  })

  afterAll(async () => {
    await sequelize.close()
  })

  afterEach(async () => {
    // 清理评论数据
    await Comment.destroy({ where: {}, force: true })
    jest.clearAllMocks()
  })

  describe('GET /api/comments', () => {
    beforeEach(async () => {
      // 创建测试评论
      await Comment.create({
        content: 'Approved comment',
        articleId: testArticle.id,
        authorId: testUser.id,
        status: 'approved'
      })

      await Comment.create({
        content: 'Pending comment',
        articleId: testArticle.id,
        authorId: testUser.id,
        status: 'pending'
      })
    })

    it('should get approved comments by default', async () => {
      const response = await request(app)
        .get('/api/comments')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.comments).toHaveLength(1)
      expect(response.body.data.comments[0].status).toBe('approved')
    })

    it('should filter comments by article ID', async () => {
      const response = await request(app)
        .get(`/api/comments?articleId=${testArticle.id}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.comments).toHaveLength(1)
    })

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/comments?page=1&limit=5')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.pagination).toBeDefined()
      expect(response.body.data.pagination.page).toBe(1)
      expect(response.body.data.pagination.limit).toBe(5)
    })
  })

  describe('GET /api/comments/:id', () => {
    let testComment: Comment

    beforeEach(async () => {
      testComment = await Comment.create({
        content: 'Test comment',
        articleId: testArticle.id,
        authorId: testUser.id,
        status: 'approved'
      })
    })

    it('should get comment by ID', async () => {
      const response = await request(app)
        .get(`/api/comments/${testComment.id}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.comment.id).toBe(testComment.id)
      expect(response.body.data.comment.content).toBe(testComment.content)
    })

    it('should return 404 for non-existent comment', async () => {
      const response = await request(app)
        .get('/api/comments/99999')
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('COMMENT_NOT_FOUND')
    })

    it('should return 400 for invalid comment ID', async () => {
      const response = await request(app)
        .get('/api/comments/invalid')
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('INVALID_ID')
    })
  })

  describe('POST /api/comments', () => {
    it('should create a new comment with valid data', async () => {
      const commentData = {
        content: 'This is a test comment',
        articleId: testArticle.id
      }

      const response = await request(app)
        .post('/api/comments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(commentData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.comment.content).toBe(commentData.content)
      expect(response.body.data.comment.status).toBe('pending')
    })

    it('should create a reply comment', async () => {
      // 先创建父评论
      const parentComment = await Comment.create({
        content: 'Parent comment',
        articleId: testArticle.id,
        authorId: testUser.id,
        status: 'approved'
      })

      const replyData = {
        content: 'This is a reply',
        articleId: testArticle.id,
        parentId: parentComment.id
      }

      const response = await request(app)
        .post('/api/comments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(replyData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.comment.parentId).toBe(parentComment.id)
    })

    it('should require authentication', async () => {
      const commentData = {
        content: 'Test comment',
        articleId: testArticle.id
      }

      const response = await request(app)
        .post('/api/comments')
        .send(commentData)
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('UNAUTHORIZED')
    })

    it('should validate comment content', async () => {
      // 测试空内容
      const response1 = await request(app)
        .post('/api/comments')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: '',
          articleId: testArticle.id
        })
        .expect(400)

      expect(response1.body.success).toBe(false)

      // 测试过长内容
      const longContent = 'a'.repeat(2001)
      const response2 = await request(app)
        .post('/api/comments')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: longContent,
          articleId: testArticle.id
        })
        .expect(400)

      expect(response2.body.success).toBe(false)
    })

    it('should return 404 for non-existent article', async () => {
      const commentData = {
        content: 'Test comment',
        articleId: 99999
      }

      const response = await request(app)
        .post('/api/comments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(commentData)
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('ARTICLE_NOT_FOUND')
    })
  })

  describe('PUT /api/comments/:id', () => {
    let testComment: Comment

    beforeEach(async () => {
      testComment = await Comment.create({
        content: 'Original content',
        articleId: testArticle.id,
        authorId: testUser.id,
        status: 'approved'
      })
    })

    it('should update comment content', async () => {
      const updateData = {
        content: 'Updated content'
      }

      const response = await request(app)
        .put(`/api/comments/${testComment.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.comment.content).toBe(updateData.content)
      expect(response.body.data.comment.status).toBe('pending') // 状态重置为待审核
    })

    it('should require authentication', async () => {
      const response = await request(app)
        .put(`/api/comments/${testComment.id}`)
        .send({ content: 'Updated content' })
        .expect(401)

      expect(response.body.success).toBe(false)
    })

    it('should only allow owner to update', async () => {
      // Mock different user
      MockedAuthService.validateToken.mockResolvedValueOnce({
        success: true,
        user: {
          id: 999,
          username: 'otheruser',
          email: '<EMAIL>'
        }
      })

      const response = await request(app)
        .put(`/api/comments/${testComment.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ content: 'Updated content' })
        .expect(403)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('FORBIDDEN')
    })
  })

  describe('DELETE /api/comments/:id', () => {
    let testComment: Comment

    beforeEach(async () => {
      testComment = await Comment.create({
        content: 'Test comment',
        articleId: testArticle.id,
        authorId: testUser.id
      })
    })

    it('should delete comment', async () => {
      const response = await request(app)
        .delete(`/api/comments/${testComment.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)

      // 验证评论已被删除
      const deletedComment = await Comment.findByPk(testComment.id)
      expect(deletedComment).toBeNull()
    })

    it('should require authentication', async () => {
      const response = await request(app)
        .delete(`/api/comments/${testComment.id}`)
        .expect(401)

      expect(response.body.success).toBe(false)
    })

    it('should only allow owner to delete', async () => {
      // Mock different user
      MockedAuthService.validateToken.mockResolvedValueOnce({
        success: true,
        user: {
          id: 999,
          username: 'otheruser',
          email: '<EMAIL>'
        }
      })

      const response = await request(app)
        .delete(`/api/comments/${testComment.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('FORBIDDEN')
    })
  })
})
