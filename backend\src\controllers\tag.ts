import { Request, Response } from 'express'
import { Tag, Article, ArticleTag } from '../models'
import { Op } from 'sequelize'
import { AuthenticatedRequest } from '../middleware/auth'

/**
 * 获取所有标签列表，并附带每个标签关联的文章数量（仅统计已发布的文章）
 * @param req Express请求对象
 * @param res Express响应对象
 * @returns 返回包含标签列表和文章数量的JSON响应
 */
export const getTags = async (req: Request, res: Response): Promise<void> => {
  try {
    // 查询所有标签的基本信息并按名称升序排列
    const tags = await Tag.findAll({
      attributes: ['id', 'name', 'slug', 'createdAt'],
      order: [['name', 'ASC']]
    })

    // 为每个标签计算关联的已发布文章数量
    const tagsWithCount = await Promise.all(
      tags.map(async (tag) => {
        const articleCount = await Article.count({
          include: [{
            model: Tag,
            as: 'tags',
            where: { id: tag.id },
            through: { attributes: [] }
          }],
          where: { status: 'published' }
        })
        return {
          ...tag.toJSON(),
          articleCount
        }
      })
    )

    res.json({
      success: true,
      tags: tagsWithCount
    })
  } catch (error) {
    console.error('Error fetching tags:', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_TAGS_ERROR',
        message: 'Failed to fetch tags'
      }
    })
  }
}

/**
 * 根据标签名称或slug获取该标签下的文章列表（分页）
 * @param req Express请求对象，包含标签名称参数和可选的分页参数
 * @param res Express响应对象
 * @returns 返回包含文章列表、标签信息和分页信息的JSON响应
 */
export const getTagArticles = async (req: Request, res: Response): Promise<void> => {
  try {
    const { tagName } = req.params
    const page = parseInt(req.query.page as string) || 1
    const limit = parseInt(req.query.limit as string) || 10
    const offset = (page - 1) * limit

    // 根据标签名称或slug查找标签
    const tag = await Tag.findOne({
      where: {
        [Op.or]: [
          { name: tagName },
          { slug: tagName }
        ]
      }
    })

    if (!tag) {
      res.status(404).json({
        success: false,
        error: {
          code: 'TAG_NOT_FOUND',
          message: 'Tag not found'
        }
      })
      return
    }

    // 查找该标签下已发布的文章并分页
    const { count, rows: articles } = await Article.findAndCountAll({
      where: { status: 'published' },
      include: [{
        model: Tag,
        as: 'tags',
        where: { id: tag.id },
        through: { attributes: [] }
      }],
      order: [['publishedAt', 'DESC']],
      limit,
      offset,
      distinct: true
    })

    const totalPages = Math.ceil(count / limit)

    res.json({
      success: true,
      articles,
      tag,
      pagination: {
        page,
        limit,
        total: count,
        totalPages
      }
    })
  } catch (error) {
    console.error('Error fetching tag articles:', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_TAG_ARTICLES_ERROR',
        message: 'Failed to fetch articles for tag'
      }
    })
  }
}

/**
 * 创建新标签
 * @param req Express请求对象，包含标签名称和可选的slug
 * @param res Express响应对象
 * @returns 返回创建成功的标签信息
 */
export const createTag = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, slug: providedSlug } = req.body

    if (!name || !name.trim()) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Tag name is required'
        }
      })
      return
    }

    // 使用提供的slug或自动生成
    let slug = providedSlug?.trim()
    if (!slug) {
      slug = name.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '')
    }

    // 验证slug格式
    if (!/^[a-z0-9-]+$/.test(slug)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Slug can only contain lowercase letters, numbers and hyphens'
        }
      })
      return
    }

    // 检查是否已存在相同名称或slug的标签
    const existingTag = await Tag.findOne({
      where: {
        [Op.or]: [
          { name: name.trim() },
          { slug }
        ]
      }
    })

    if (existingTag) {
      res.status(409).json({
        success: false,
        error: {
          code: 'TAG_EXISTS',
          message: 'Tag already exists'
        }
      })
      return
    }

    // 创建新标签
    const tag = await Tag.create({
      name: name.trim(),
      slug
    })

    res.status(201).json({
      success: true,
      tag
    })
  } catch (error) {
    console.error('Error creating tag:', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'CREATE_TAG_ERROR',
        message: 'Failed to create tag'
      }
    })
  }
}

/**
 * 删除指定ID的标签
 * @param req Express请求对象，包含标签ID参数
 * @param res Express响应对象
 * @returns 返回删除成功的消息
 */
export const deleteTag = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params

    // 查找要删除的标签
    const tag = await Tag.findByPk(id)
    if (!tag) {
      res.status(404).json({
        success: false,
        error: {
          code: 'TAG_NOT_FOUND',
          message: 'Tag not found'
        }
      })
      return
    }

    // 删除标签
    await tag.destroy()

    res.json({
      success: true,
      message: 'Tag deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting tag:', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'DELETE_TAG_ERROR',
        message: 'Failed to delete tag'
      }
    })
  }
}

/**
 * 获取标签统计信息，包括总数、使用情况等
 * @param req Express请求对象
 * @param res Express响应对象
 * @returns 返回标签统计信息和所有标签列表
 */
export const getTagStats = async (req: Request, res: Response): Promise<void> => {
  try {
    // 获取标签总数
    const totalTags = await Tag.count()

    // 获取所有标签信息
    const allTags = await Tag.findAll({
      attributes: ['id', 'name', 'slug', 'createdAt'],
      order: [['name', 'ASC']]
    })

    // 计算每个标签关联的文章数量
    const tagsWithCounts = await Promise.all(
      allTags.map(async (tag) => {
        const articleCount = await Article.count({
          include: [{
            model: Tag,
            as: 'tags',
            where: { id: tag.id },
            through: { attributes: [] }
          }],
          where: { status: 'published' }
        })
        return {
          ...tag.toJSON(),
          articleCount
        }
      })
    )

    // 计算统计数据
    const usedTags = tagsWithCounts.filter(tag => tag.articleCount > 0).length
    const unusedTags = totalTags - usedTags
    const totalArticleTagAssociations = tagsWithCounts.reduce((sum, tag) => sum + tag.articleCount, 0)
    const averageArticlesPerTag = usedTags > 0 ? totalArticleTagAssociations / usedTags : 0

    res.json({
      success: true,
      stats: {
        totalTags,
        usedTags,
        unusedTags,
        averageArticlesPerTag: Math.round(averageArticlesPerTag * 100) / 100
      },
      tags: tagsWithCounts
    })
  } catch (error) {
    console.error('Error fetching tag statistics:', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_TAG_STATS_ERROR',
        message: 'Failed to fetch tag statistics'
      }
    })
  }
}

/**
 * 获取热门标签列表（按文章数量排序）
 * @param req Express请求对象，可包含限制数量的查询参数
 * @param res Express响应对象
 * @returns 返回热门标签列表
 */
export const getPopularTags = async (req: Request, res: Response): Promise<void> => {
  try {
    const limit = parseInt(req.query.limit as string) || 10

    // 获取所有标签信息
    const allTags = await Tag.findAll({
      attributes: ['id', 'name', 'slug', 'createdAt']
    })

    // 计算每个标签关联的文章数量
    const tagsWithCounts = await Promise.all(
      allTags.map(async (tag) => {
        const articleCount = await Article.count({
          include: [{
            model: Tag,
            as: 'tags',
            where: { id: tag.id },
            through: { attributes: [] }
          }],
          where: { status: 'published' }
        })
        return {
          ...tag.toJSON(),
          articleCount
        }
      })
    )

    // 按文章数量降序排列并截取前N个
    const popularTags = tagsWithCounts
      .filter(tag => tag.articleCount > 0)
      .sort((a, b) => b.articleCount - a.articleCount)
      .slice(0, limit)

    res.json({
      success: true,
      tags: popularTags
    })
  } catch (error) {
    console.error('Error fetching popular tags:', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_POPULAR_TAGS_ERROR',
        message: 'Failed to fetch popular tags'
      }
    })
  }
}

/**
 * 为指定文章添加标签（需要认证）
 * @param req 认证过的请求对象，包含用户信息、文章ID和标签ID
 * @param res Express响应对象
 * @returns 返回添加成功的消息
 */
export const addTagToArticle = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { articleId, tagId } = req.body

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    // 验证文章是否存在且属于当前用户
    const article = await Article.findOne({
      where: {
        id: articleId,
        authorId: req.user.id
      }
    })

    if (!article) {
      res.status(404).json({
        success: false,
        error: {
          code: 'ARTICLE_NOT_FOUND',
          message: 'Article not found or access denied'
        }
      })
      return
    }

    // 验证标签是否存在
    const tag = await Tag.findByPk(tagId)
    if (!tag) {
      res.status(404).json({
        success: false,
        error: {
          code: 'TAG_NOT_FOUND',
          message: 'Tag not found'
        }
      })
      return
    }

    // 检查标签是否已关联到该文章
    const existingAssociation = await ArticleTag.findOne({
      where: { articleId, tagId }
    })

    if (existingAssociation) {
      res.status(409).json({
        success: false,
        error: {
          code: 'ASSOCIATION_EXISTS',
          message: 'Tag is already associated with this article'
        }
      })
      return
    }

    // 创建标签与文章的关联
    await ArticleTag.create({ articleId, tagId })

    res.status(201).json({
      success: true,
      message: 'Tag added to article successfully'
    })
  } catch (error) {
    console.error('Error adding tag to article:', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'ADD_TAG_ERROR',
        message: 'Failed to add tag to article'
      }
    })
  }
}

/**
 * 从指定文章中移除标签（需要认证）
 * @param req 认证过的请求对象，包含用户信息、文章ID和标签ID参数
 * @param res Express响应对象
 * @returns 返回移除成功的消息
 */
export const removeTagFromArticle = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { articleId, tagId } = req.params

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    // 验证文章是否存在且属于当前用户
    const article = await Article.findOne({
      where: {
        id: articleId,
        authorId: req.user.id
      }
    })

    if (!article) {
      res.status(404).json({
        success: false,
        error: {
          code: 'ARTICLE_NOT_FOUND',
          message: 'Article not found or access denied'
        }
      })
      return
    }

    // 查找标签与文章的关联
    const association = await ArticleTag.findOne({
      where: { articleId, tagId }
    })

    if (!association) {
      res.status(404).json({
        success: false,
        error: {
          code: 'ASSOCIATION_NOT_FOUND',
          message: 'Tag is not associated with this article'
        }
      })
      return
    }

    // 删除关联
    await association.destroy()

    res.json({
      success: true,
      message: 'Tag removed from article successfully'
    })
  } catch (error) {
    console.error('Error removing tag from article:', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'REMOVE_TAG_ERROR',
        message: 'Failed to remove tag from article'
      }
    })
  }
}

/**
 * 获取指定文章的所有标签
 * @param req Express请求对象，包含文章ID参数
 * @param res Express响应对象
 * @returns 返回文章关联的标签列表
 */
export const getArticleTags = async (req: Request, res: Response): Promise<void> => {
  try {
    const { articleId } = req.params

    // 查找文章并包含其关联的标签
    const article = await Article.findByPk(articleId, {
      include: [{
        model: Tag,
        as: 'tags',
        attributes: ['id', 'name', 'slug', 'createdAt'],
        through: { attributes: [] }
      }]
    })

    if (!article) {
      res.status(404).json({
        success: false,
        error: {
          code: 'ARTICLE_NOT_FOUND',
          message: 'Article not found'
        }
      })
      return
    }

    res.json({
      success: true,
      tags: (article as any).tags || []
    })
  } catch (error) {
    console.error('Error fetching article tags:', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_ARTICLE_TAGS_ERROR',
        message: 'Failed to fetch article tags'
      }
    })
  }
}

/**
 * 更新指定ID的标签信息
 * @param req Express请求对象，包含标签ID参数和更新数据
 * @param res Express响应对象
 * @returns 返回更新后的标签信息
 */
export const updateTag = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params
    const { name, slug: providedSlug } = req.body

    if (!name || !name.trim()) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Tag name is required'
        }
      })
      return
    }

    // 查找要更新的标签
    const tag = await Tag.findByPk(id)
    if (!tag) {
      res.status(404).json({
        success: false,
        error: {
          code: 'TAG_NOT_FOUND',
          message: 'Tag not found'
        }
      })
      return
    }

    // 使用提供的slug或自动生成
    let slug = providedSlug?.trim()
    if (!slug) {
      slug = name.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '')
    }

    // 验证slug格式
    if (!/^[a-z0-9-]+$/.test(slug)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Slug can only contain lowercase letters, numbers and hyphens'
        }
      })
      return
    }

    // 检查是否已存在相同名称或slug的其他标签
    const existingTag = await Tag.findOne({
      where: {
        [Op.and]: [
          { id: { [Op.ne]: id } },
          {
            [Op.or]: [
              { name: name.trim() },
              { slug }
            ]
          }
        ]
      }
    })

    if (existingTag) {
      res.status(409).json({
        success: false,
        error: {
          code: 'TAG_EXISTS',
          message: 'Tag with this name or slug already exists'
        }
      })
      return
    }

    // 更新标签
    await tag.update({
      name: name.trim(),
      slug
    })

    res.json({
      success: true,
      tag
    })
  } catch (error) {
    console.error('Error updating tag:', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'UPDATE_TAG_ERROR',
        message: 'Failed to update tag'
      }
    })
  }
}

/**
 * 批量删除标签
 * @param req Express请求对象，包含要删除的标签ID数组
 * @param res Express响应对象
 * @returns 返回删除结果
 */
export const batchDeleteTags = async (req: Request, res: Response): Promise<void> => {
  try {
    const { tagIds } = req.body

    if (!Array.isArray(tagIds) || tagIds.length === 0) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Tag IDs array is required'
        }
      })
      return
    }

    // 验证所有标签ID都是数字
    const validIds = tagIds.filter(id => Number.isInteger(id) && id > 0)
    if (validIds.length !== tagIds.length) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'All tag IDs must be valid integers'
        }
      })
      return
    }

    // 查找要删除的标签
    const tagsToDelete = await Tag.findAll({
      where: { id: validIds }
    })

    if (tagsToDelete.length === 0) {
      res.status(404).json({
        success: false,
        error: {
          code: 'TAGS_NOT_FOUND',
          message: 'No tags found with the provided IDs'
        }
      })
      return
    }

    // 批量删除标签
    const deletedCount = await Tag.destroy({
      where: { id: validIds }
    })

    res.json({
      success: true,
      message: `Successfully deleted ${deletedCount} tags`,
      deletedCount,
      requestedCount: tagIds.length
    })
  } catch (error) {
    console.error('Error batch deleting tags:', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'BATCH_DELETE_TAGS_ERROR',
        message: 'Failed to batch delete tags'
      }
    })
  }
}