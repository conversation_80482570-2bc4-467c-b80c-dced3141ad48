import { Sequelize } from 'sequelize'
import dotenv from 'dotenv'

dotenv.config()

const {
  DB_HOST = 'localhost',
  DB_PORT = '3306',
  DB_NAME = 'person-blog',
  DB_USER = 'person-blog',
  DB_PASSWORD = '123456',
  NODE_ENV = 'development'
} = process.env

/**
 * Sequelize数据库连接实例
 * 配置了MySQL数据库连接参数，包括连接池、模型定义规则等
 */
export const sequelize = new Sequelize(DB_NAME, DB_USER, DB_PASSWORD, {
  host: DB_HOST,
  port: parseInt(DB_PORT),
  dialect: 'mysql',
  logging: NODE_ENV === 'development' ? console.log : false,
  dialectOptions: {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  },
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  }
})

/**
 * 测试数据库连接状态
 * 通过Sequelize的authenticate方法验证数据库连接是否正常
 * @returns Promise<boolean> 连接成功返回true，失败返回false
 */
export const testConnection = async (): Promise<boolean> => {
  try {
    await sequelize.authenticate()
    console.log('Database connection has been established successfully.')
    return true
  } catch (error) {
    console.error('Unable to connect to the database:', error)
    return false
  }
}