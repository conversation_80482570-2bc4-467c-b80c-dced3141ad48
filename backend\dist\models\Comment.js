"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Comment = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const User_1 = require("./User");
const Article_1 = require("./Article");
class Comment extends sequelize_1.Model {
    async approve() {
        this.status = 'approved';
        await this.save();
    }
    async reject() {
        this.status = 'rejected';
        await this.save();
    }
    isTopLevel() {
        return this.parentId === null || this.parentId === undefined;
    }
    getPlainContent() {
        return this.content.replace(/<[^>]*>/g, '');
    }
    isArticleComment() {
        return !!this.articleId;
    }
    isPostComment() {
        return !!this.postId;
    }
    static async findByPostId(postId, options = {}) {
        const { limit = 10, offset = 0, includeReplies = true } = options;
        const includeClause = [
            {
                model: User_1.User,
                as: 'author',
                attributes: ['id', 'username']
            }
        ];
        if (includeReplies) {
            includeClause.push({
                model: Comment,
                as: 'replies',
                where: { status: 'approved' },
                required: false,
                include: [
                    {
                        model: User_1.User,
                        as: 'author',
                        attributes: ['id', 'username']
                    }
                ]
            });
        }
        return this.findAndCountAll({
            where: {
                postId,
                status: 'approved',
                parentId: null
            },
            include: includeClause,
            order: [['createdAt', 'DESC']],
            limit,
            offset,
            distinct: true
        });
    }
    static async findByArticleId(articleId, options = {}) {
        const { limit = 10, offset = 0, includeReplies = true } = options;
        const includeClause = [
            {
                model: User_1.User,
                as: 'author',
                attributes: ['id', 'username']
            }
        ];
        if (includeReplies) {
            includeClause.push({
                model: Comment,
                as: 'replies',
                where: { status: 'approved' },
                required: false,
                include: [
                    {
                        model: User_1.User,
                        as: 'author',
                        attributes: ['id', 'username']
                    }
                ]
            });
        }
        return this.findAndCountAll({
            where: {
                articleId,
                status: 'approved',
                parentId: null
            },
            include: includeClause,
            order: [['createdAt', 'DESC']],
            limit,
            offset,
            distinct: true
        });
    }
    static async findByAuthorId(authorId, options = {}) {
        const { limit = 10, offset = 0, status } = options;
        const whereClause = { authorId };
        if (status) {
            whereClause.status = status;
        }
        return this.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: Article_1.Article,
                    as: 'article',
                    attributes: ['id', 'title', 'slug']
                },
                {
                    model: Comment,
                    as: 'parent',
                    attributes: ['id', 'content'],
                    include: [
                        {
                            model: User_1.User,
                            as: 'author',
                            attributes: ['id', 'username']
                        }
                    ]
                }
            ],
            order: [['createdAt', 'DESC']],
            limit,
            offset,
            distinct: true
        });
    }
    static async findPending(options = {}) {
        const { limit = 10, offset = 0 } = options;
        return this.findAndCountAll({
            where: { status: 'pending' },
            include: [
                {
                    model: User_1.User,
                    as: 'author',
                    attributes: ['id', 'username']
                },
                {
                    model: Article_1.Article,
                    as: 'article',
                    attributes: ['id', 'title', 'slug']
                }
            ],
            order: [['createdAt', 'ASC']],
            limit,
            offset,
            distinct: true
        });
    }
}
exports.Comment = Comment;
Comment.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    content: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: false,
        validate: {
            notEmpty: true,
            len: [1, 2000]
        }
    },
    status: {
        type: sequelize_1.DataTypes.ENUM('pending', 'approved', 'rejected'),
        allowNull: false,
        defaultValue: 'pending'
    },
    articleId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        field: 'article_id',
        references: {
            model: 'articles',
            key: 'id'
        }
    },
    postId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        field: 'post_id',
        references: {
            model: 'posts',
            key: 'id'
        }
    },
    authorId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'author_id',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    parentId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        field: 'parent_id',
        references: {
            model: 'comments',
            key: 'id'
        }
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'Comment',
    tableName: 'comments',
    timestamps: true,
    underscored: true,
    hooks: {
        beforeCreate: (comment) => {
            if (!comment.articleId && !comment.postId) {
                throw new Error('评论必须关联文章或说说');
            }
            if (comment.articleId && comment.postId) {
                throw new Error('评论不能同时关联文章和说说');
            }
            comment.content = comment.content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
        },
        beforeUpdate: (comment) => {
            if (!comment.articleId && !comment.postId) {
                throw new Error('评论必须关联文章或说说');
            }
            if (comment.articleId && comment.postId) {
                throw new Error('评论不能同时关联文章和说说');
            }
            if (comment.changed('content')) {
                comment.content = comment.content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
            }
        }
    }
});
//# sourceMappingURL=Comment.js.map