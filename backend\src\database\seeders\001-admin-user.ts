import { QueryInterface } from 'sequelize'
import bcrypt from 'bcryptjs'

/**
 * 创建用户种子数据
 * 包括管理员用户和普通用户，用于测试和演示
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('Creating sample users...')

  // 检查是否已存在用户
  const existingUsers = await queryInterface.select(null, 'users', {})
  if (existingUsers.length > 0) {
    console.log('Users already exist, skipping...')
    return
  }

  // 生成密码哈希
  const saltRounds = 12
  const adminPasswordHash = await bcrypt.hash('admin123', saltRounds)
  const userPasswordHash = await bcrypt.hash('user123', saltRounds)

  // 定义用户数据
  const users = [
    {
      username: 'admin',
      email: '<EMAIL>',
      password_hash: adminPasswordHash,
      created_at: new Date('2024-01-01 10:00:00'),
      updated_at: new Date('2024-01-01 10:00:00')
    },
    {
      username: 'john_doe',
      email: '<EMAIL>',
      password_hash: userPasswordHash,
      created_at: new Date('2024-01-02 09:30:00'),
      updated_at: new Date('2024-01-02 09:30:00')
    },
    {
      username: 'jane_smith',
      email: '<EMAIL>',
      password_hash: userPasswordHash,
      created_at: new Date('2024-01-03 14:15:00'),
      updated_at: new Date('2024-01-03 14:15:00')
    },
    {
      username: 'tech_writer',
      email: '<EMAIL>',
      password_hash: userPasswordHash,
      created_at: new Date('2024-01-04 11:20:00'),
      updated_at: new Date('2024-01-04 11:20:00')
    }
  ]

  // 插入用户数据
  await queryInterface.bulkInsert('users', users)

  console.log(`Created ${users.length} sample users:`)
  users.forEach(user => {
    console.log(`  • ${user.username} (${user.email})`)
  })
  console.log('')
  console.log('Default passwords:')
  console.log('  • admin: admin123')
  console.log('  • others: user123')
  console.log('⚠️  Please change default passwords in production!')
}

/**
 * 回滚种子数据
 * 删除创建的示例用户
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('Removing sample users...')

  const usernames = ['admin', 'john_doe', 'jane_smith', 'tech_writer']

  await queryInterface.bulkDelete('users', {
    username: usernames
  }, {})

  console.log('Sample users removed successfully!')
}
