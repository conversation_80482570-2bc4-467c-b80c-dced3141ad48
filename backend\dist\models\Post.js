"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Post = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const Comment_1 = require("./Comment");
class Post extends sequelize_1.Model {
    async getLikeCount() {
        const { PostLike } = await Promise.resolve().then(() => __importStar(require('./PostLike')));
        return await PostLike.count({
            where: { postId: this.id }
        });
    }
    async getCommentCount() {
        return await Comment_1.Comment.count({
            where: { postId: this.id }
        });
    }
    async isLikedByUser(userId) {
        const { PostLike } = await Promise.resolve().then(() => __importStar(require('./PostLike')));
        const like = await PostLike.findOne({
            where: { postId: this.id, userId }
        });
        return !!like;
    }
    async canBeViewedBy(userId) {
        if (this.visibility === 'public') {
            return true;
        }
        if (this.visibility === 'private') {
            return userId === this.authorId;
        }
        return false;
    }
    async getFullInfo(userId) {
        const [likeCount, commentCount, isLiked] = await Promise.all([
            this.getLikeCount(),
            this.getCommentCount(),
            userId ? this.isLikedByUser(userId) : false
        ]);
        return {
            id: this.id,
            content: this.content,
            images: this.images || [],
            visibility: this.visibility,
            location: this.location,
            authorId: this.authorId,
            author: this.author,
            likeCount,
            commentCount,
            isLiked,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }
}
exports.Post = Post;
Post.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    content: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: false,
        validate: {
            notEmpty: true,
            len: [1, 1000]
        }
    },
    images: {
        type: sequelize_1.DataTypes.JSON,
        allowNull: true,
        validate: {
            isValidImageArray(value) {
                if (value !== null && value !== undefined) {
                    if (!Array.isArray(value)) {
                        throw new Error('图片必须是数组格式');
                    }
                    if (value.length > 9) {
                        throw new Error('最多只能上传9张图片');
                    }
                    for (const url of value) {
                        if (typeof url !== 'string' || !url.trim()) {
                            throw new Error('图片URL必须是有效的字符串');
                        }
                    }
                }
            }
        }
    },
    visibility: {
        type: sequelize_1.DataTypes.ENUM('public', 'private'),
        allowNull: false,
        defaultValue: 'public'
    },
    location: {
        type: sequelize_1.DataTypes.STRING(200),
        allowNull: true,
        validate: {
            len: [0, 200]
        }
    },
    authorId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'author_id',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'Post',
    tableName: 'posts',
    timestamps: true,
    indexes: [
        {
            fields: ['author_id']
        },
        {
            fields: ['created_at']
        },
        {
            fields: ['visibility']
        },
        {
            fields: ['author_id', 'created_at']
        }
    ]
});
//# sourceMappingURL=Post.js.map