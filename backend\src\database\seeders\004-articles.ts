import { QueryInterface } from 'sequelize'
import { generateSlug } from '../../utils/slug'

/**
 * 创建示例文章种子数据
 * 为博客系统创建一些示例文章内容，包含不同分类和状态
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('Creating sample articles...')

  // 获取用户ID
  const users = await queryInterface.select(null, 'users', {})
  if (users.length === 0) {
    console.log('No users found, skipping article creation...')
    return
  }

  const adminUser = users.find((user: any) => user.username === 'admin') as any
  const johnUser = users.find((user: any) => user.username === 'john_doe') as any
  const janeUser = users.find((user: any) => user.username === 'jane_smith') as any
  const techUser = users.find((user: any) => user.username === 'tech_writer') as any

  if (!adminUser) {
    console.log('Admin user not found, skipping article creation...')
    return
  }

  // 获取一些标签ID用于关联
  const tags = await queryInterface.select(null, 'tags', {})
  const tagMap = new Map(tags.map((tag: any) => [tag.name, tag.id]))

  // 定义示例文章
  const sampleArticles = [
    {
      title: '欢迎来到我的个人博客',
      content: `# 欢迎来到我的个人博客

欢迎访问我的个人技术博客！这里是我分享技术心得、项目经验和学习笔记的地方。

## 关于这个博客

这个博客系统是使用现代化的技术栈构建的：

- **前端**: React + TypeScript + Vite
- **后端**: Node.js + Express + TypeScript  
- **数据库**: MySQL + Sequelize ORM
- **认证**: JWT
- **文档**: Swagger API

## 你可以在这里找到什么

- 前端开发技术分享
- 后端开发最佳实践
- 学习笔记和总结
- 项目开发经验
- 工具和技巧推荐

## 联系我

如果你对文章内容有任何问题或建议，欢迎通过以下方式联系我：

- Email: <EMAIL>
- GitHub: [你的GitHub地址]

感谢你的访问，希望这些内容对你有所帮助！`,
      excerpt: '欢迎访问我的个人技术博客！这里是我分享技术心得、项目经验和学习笔记的地方。',
      status: 'published',
      authorId: adminUser.id,
      categoryId: 5, // 前端开发
      tags: ['Web开发', 'React', 'Node.js'],
      publishedAt: new Date('2024-01-10 10:00:00'),
      createdAt: new Date('2024-01-10 09:30:00'),
      updatedAt: new Date('2024-01-10 10:00:00')
    },
    {
      title: 'TypeScript 开发最佳实践',
      content: `# TypeScript 开发最佳实践

TypeScript 作为 JavaScript 的超集，为我们提供了强大的类型系统。本文将分享一些 TypeScript 开发中的最佳实践。

## 1. 严格的类型检查

启用严格模式可以帮助我们写出更安全的代码：

\`\`\`json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true
  }
}
\`\`\`

## 2. 合理使用接口和类型别名

### 接口定义

\`\`\`typescript
interface User {
  id: number
  name: string
  email: string
  createdAt: Date
}
\`\`\`

### 类型别名

\`\`\`typescript
type Status = 'pending' | 'approved' | 'rejected'
type UserWithStatus = User & { status: Status }
\`\`\`

## 3. 泛型的使用

泛型让我们的代码更加灵活和可重用：

\`\`\`typescript
function createResponse<T>(data: T): ApiResponse<T> {
  return {
    success: true,
    data,
    timestamp: new Date()
  }
}
\`\`\`

## 总结

TypeScript 的类型系统虽然增加了一些学习成本，但它带来的代码质量提升和开发体验改善是非常值得的。

希望这些实践对你的 TypeScript 开发有所帮助！`,
      excerpt: 'TypeScript 作为 JavaScript 的超集，为我们提供了强大的类型系统。本文将分享一些 TypeScript 开发中的最佳实践。',
      status: 'published',
      authorId: techUser?.id || adminUser.id,
      categoryId: 9, // JavaScript
      tags: ['TypeScript', 'JavaScript', '前端开发', '代码规范'],
      publishedAt: new Date('2024-01-12 14:30:00'),
      createdAt: new Date('2024-01-12 13:00:00'),
      updatedAt: new Date('2024-01-12 14:30:00')
    },
    {
      title: 'Node.js 后端开发入门指南',
      content: `# Node.js 后端开发入门指南

Node.js 让 JavaScript 开发者能够构建高性能的服务器端应用。本文将介绍 Node.js 后端开发的基础知识。

## 什么是 Node.js

Node.js 是一个基于 Chrome V8 引擎的 JavaScript 运行时环境，它让我们能够在服务器端运行 JavaScript 代码。

## 核心特性

### 1. 事件驱动
Node.js 使用事件驱动、非阻塞 I/O 模型，这使得它轻量且高效。

### 2. 单线程
虽然是单线程，但通过事件循环机制，Node.js 能够处理大量并发连接。

### 3. NPM 生态
拥有世界上最大的开源库生态系统。

## 快速开始

### 创建一个简单的 HTTP 服务器

\`\`\`javascript
const http = require('http')

const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'text/plain' })
  res.end('Hello World!')
})

server.listen(3000, () => {
  console.log('Server running at http://localhost:3000/')
})
\`\`\`

### 使用 Express 框架

\`\`\`javascript
const express = require('express')
const app = express()

app.get('/', (req, res) => {
  res.send('Hello Express!')
})

app.listen(3000, () => {
  console.log('Server is running on port 3000')
})
\`\`\`

## 总结

Node.js 为前端开发者提供了进入后端开发的绝佳机会。通过学习 Node.js，你可以成为一名全栈开发者。

开始你的 Node.js 之旅吧！`,
      excerpt: 'Node.js 让 JavaScript 开发者能够构建高性能的服务器端应用。本文将介绍 Node.js 后端开发的基础知识。',
      status: 'published',
      authorId: johnUser?.id || adminUser.id,
      categoryId: 13, // Node.js
      tags: ['Node.js', 'JavaScript', '后端开发', 'Express'],
      publishedAt: new Date('2024-01-15 16:00:00'),
      createdAt: new Date('2024-01-15 15:00:00'),
      updatedAt: new Date('2024-01-15 16:00:00')
    },
    {
      title: 'React Hooks 深入理解',
      content: `# React Hooks 深入理解

React Hooks 是 React 16.8 引入的新特性，它让我们能够在函数组件中使用状态和其他 React 特性。

## 什么是 Hooks

Hooks 是一些可以让你在函数组件里"钩入" React state 及生命周期等特性的函数。

## 常用的 Hooks

### useState

\`\`\`jsx
import React, { useState } from 'react'

function Counter() {
  const [count, setCount] = useState(0)

  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  )
}
\`\`\`

### useEffect

\`\`\`jsx
import React, { useState, useEffect } from 'react'

function Example() {
  const [count, setCount] = useState(0)

  useEffect(() => {
    document.title = \`You clicked \${count} times\`
  })

  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  )
}
\`\`\`

## 自定义 Hooks

你可以创建自己的 Hooks 来复用状态逻辑：

\`\`\`jsx
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue)

  const increment = () => setCount(count + 1)
  const decrement = () => setCount(count - 1)
  const reset = () => setCount(initialValue)

  return { count, increment, decrement, reset }
}
\`\`\`

## 总结

React Hooks 让函数组件变得更加强大，是现代 React 开发的重要工具。`,
      excerpt: 'React Hooks 是 React 16.8 引入的新特性，让我们能够在函数组件中使用状态和其他 React 特性。',
      status: 'published',
      authorId: janeUser?.id || adminUser.id,
      categoryId: 11, // React
      tags: ['React', 'JavaScript', '前端开发', 'Hooks'],
      publishedAt: new Date('2024-01-18 11:00:00'),
      createdAt: new Date('2024-01-18 10:00:00'),
      updatedAt: new Date('2024-01-18 11:00:00')
    },
    {
      title: 'MySQL 数据库优化技巧',
      content: `# MySQL 数据库优化技巧

数据库性能优化是后端开发中的重要技能。本文将分享一些 MySQL 数据库优化的实用技巧。

## 索引优化

### 1. 选择合适的索引类型

- **主键索引**: 每个表都应该有主键
- **唯一索引**: 用于唯一性约束
- **普通索引**: 提高查询性能
- **复合索引**: 多列组合索引

### 2. 索引设计原则

\`\`\`sql
-- 好的索引设计
CREATE INDEX idx_user_email ON users(email);
CREATE INDEX idx_article_status_created ON articles(status, created_at);

-- 避免过多索引
-- 每个索引都会增加写操作的开销
\`\`\`

## 查询优化

### 1. 使用 EXPLAIN 分析查询

\`\`\`sql
EXPLAIN SELECT * FROM articles
WHERE status = 'published'
ORDER BY created_at DESC
LIMIT 10;
\`\`\`

### 2. 避免 SELECT *

\`\`\`sql
-- 不好的做法
SELECT * FROM articles;

-- 好的做法
SELECT id, title, excerpt, created_at FROM articles;
\`\`\`

## 表结构优化

### 1. 选择合适的数据类型

- 使用最小的数据类型
- VARCHAR vs CHAR
- INT vs BIGINT

### 2. 表分区

对于大表，可以考虑分区：

\`\`\`sql
CREATE TABLE articles (
    id INT AUTO_INCREMENT,
    title VARCHAR(200),
    content TEXT,
    created_at DATETIME,
    PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
\`\`\`

## 总结

数据库优化是一个持续的过程，需要根据实际业务场景进行调整。`,
      excerpt: '数据库性能优化是后端开发中的重要技能。本文将分享一些 MySQL 数据库优化的实用技巧。',
      status: 'published',
      authorId: techUser?.id || adminUser.id,
      categoryId: 7, // 数据库
      tags: ['MySQL', '数据库', '性能优化', '后端开发'],
      publishedAt: new Date('2024-01-20 15:30:00'),
      createdAt: new Date('2024-01-20 14:00:00'),
      updatedAt: new Date('2024-01-20 15:30:00')
    },
    {
      title: 'Docker 容器化部署实践',
      content: `# Docker 容器化部署实践

Docker 是现代应用部署的重要工具。本文将介绍如何使用 Docker 进行应用容器化部署。

## 什么是 Docker

Docker 是一个开源的容器化平台，可以将应用程序及其依赖项打包到轻量级、可移植的容器中。

## 基础概念

- **镜像 (Image)**: 应用程序的只读模板
- **容器 (Container)**: 镜像的运行实例
- **Dockerfile**: 构建镜像的指令文件

## 创建 Dockerfile

\`\`\`dockerfile
# 使用官方 Node.js 镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["npm", "start"]
\`\`\`

## Docker Compose

使用 Docker Compose 管理多容器应用：

\`\`\`yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      - db

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: blog
    volumes:
      - db_data:/var/lib/mysql

volumes:
  db_data:
\`\`\`

## 最佳实践

1. **多阶段构建**: 减小镜像大小
2. **使用 .dockerignore**: 排除不必要的文件
3. **安全考虑**: 不要在镜像中包含敏感信息
4. **健康检查**: 添加容器健康检查

## 总结

Docker 大大简化了应用的部署和管理，是现代 DevOps 的重要工具。`,
      excerpt: 'Docker 是现代应用部署的重要工具。本文将介绍如何使用 Docker 进行应用容器化部署。',
      status: 'published',
      authorId: johnUser?.id || adminUser.id,
      categoryId: 8, // 运维部署
      tags: ['Docker', '运维部署', '容器化', 'DevOps'],
      publishedAt: new Date('2024-01-22 09:00:00'),
      createdAt: new Date('2024-01-22 08:00:00'),
      updatedAt: new Date('2024-01-22 09:00:00')
    },
    {
      title: 'CSS Grid 布局完全指南',
      content: `# CSS Grid 布局完全指南

CSS Grid 是一个强大的二维布局系统，可以同时处理行和列。本文将详细介绍 CSS Grid 的使用方法。

## 基础概念

CSS Grid 布局由网格容器（Grid Container）和网格项目（Grid Items）组成。

## 创建网格容器

\`\`\`css
.container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 100px 200px;
  gap: 20px;
}
\`\`\`

## 网格线命名

\`\`\`css
.container {
  display: grid;
  grid-template-columns: [start] 1fr [middle] 1fr [end];
  grid-template-rows: [header-start] 100px [header-end content-start] 1fr [content-end];
}
\`\`\`

## 网格区域

\`\`\`css
.container {
  display: grid;
  grid-template-areas:
    "header header header"
    "sidebar content content"
    "footer footer footer";
  grid-template-columns: 200px 1fr 1fr;
  grid-template-rows: 80px 1fr 60px;
}

.header { grid-area: header; }
.sidebar { grid-area: sidebar; }
.content { grid-area: content; }
.footer { grid-area: footer; }
\`\`\`

## 响应式网格

\`\`\`css
.container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}
\`\`\`

## 实际应用示例

\`\`\`html
<div class="blog-layout">
  <header class="header">Header</header>
  <nav class="sidebar">Sidebar</nav>
  <main class="content">Content</main>
  <footer class="footer">Footer</footer>
</div>
\`\`\`

\`\`\`css
.blog-layout {
  display: grid;
  grid-template-areas:
    "header header"
    "sidebar content"
    "footer footer";
  grid-template-columns: 250px 1fr;
  grid-template-rows: auto 1fr auto;
  min-height: 100vh;
  gap: 20px;
}

@media (max-width: 768px) {
  .blog-layout {
    grid-template-areas:
      "header"
      "content"
      "sidebar"
      "footer";
    grid-template-columns: 1fr;
  }
}
\`\`\`

## 总结

CSS Grid 提供了强大而灵活的布局能力，是现代 Web 开发的重要工具。`,
      excerpt: 'CSS Grid 是一个强大的二维布局系统，可以同时处理行和列。本文将详细介绍 CSS Grid 的使用方法。',
      status: 'draft',
      authorId: janeUser?.id || adminUser.id,
      categoryId: 12, // CSS
      tags: ['CSS', '前端开发', '布局', 'Grid'],
      publishedAt: null,
      createdAt: new Date('2024-01-24 16:00:00'),
      updatedAt: new Date('2024-01-24 17:30:00')
    }
  ]

  // 检查已存在的文章
  const existingArticles = await queryInterface.select(null, 'articles', {})
  const existingTitles = existingArticles.map((article: any) => article.title)

  // 过滤出需要创建的新文章
  const newArticles = sampleArticles.filter(article => !existingTitles.includes(article.title))

  if (newArticles.length === 0) {
    console.log('All sample articles already exist, skipping...')
    return
  }

  // 准备插入的文章数据
  const articlesToInsert = newArticles.map(article => ({
    title: article.title,
    slug: generateSlug(article.title),
    content: article.content,
    excerpt: article.excerpt,
    status: article.status,
    author_id: article.authorId,
    category_id: article.categoryId,
    published_at: article.publishedAt,
    created_at: article.createdAt,
    updated_at: article.updatedAt
  }))

  // 批量插入文章
  await queryInterface.bulkInsert('articles', articlesToInsert)

  console.log(`Created ${newArticles.length} sample articles`)

  // 为文章添加标签关联
  for (let i = 0; i < newArticles.length; i++) {
    const article = newArticles[i]
    if (!article) continue

    const insertedArticle = await queryInterface.select(null, 'articles', {
      where: { title: article.title }
    })

    if (insertedArticle.length > 0) {
      const articleId = (insertedArticle[0] as any).id

      // 为每篇文章关联标签
      for (const tagName of article.tags) {
        const tagId = tagMap.get(tagName)
        if (tagId) {
          await queryInterface.bulkInsert('article_tags', [{
            article_id: articleId,
            tag_id: tagId
          }])
        }
      }
    }
  }

  console.log('Article-tag associations created successfully!')
}

/**
 * 回滚种子数据
 * 删除创建的示例文章
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('Removing sample articles...')

  const sampleTitles = [
    '欢迎来到我的个人博客',
    'TypeScript 开发最佳实践',
    'Node.js 后端开发入门指南',
    'React Hooks 深入理解',
    'MySQL 数据库优化技巧',
    'Docker 容器化部署实践',
    'CSS Grid 布局完全指南'
  ]

  // 删除文章标签关联
  const articles = await queryInterface.select(null, 'articles', {
    where: { title: sampleTitles }
  })

  const articleIds = articles.map((article: any) => article.id)

  if (articleIds.length > 0) {
    await queryInterface.bulkDelete('article_tags', {
      article_id: articleIds
    }, {})
  }

  // 删除文章
  await queryInterface.bulkDelete('articles', {
    title: sampleTitles
  }, {})

  console.log('Sample articles removed successfully!')
}
