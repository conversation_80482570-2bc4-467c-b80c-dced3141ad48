import { Model, Optional, Association } from 'sequelize';
import { User } from './User';
import { Post } from './Post';
export interface PostLikeAttributes {
    id: number;
    postId: number;
    userId: number;
    createdAt: Date;
}
export interface PostLikeCreationAttributes extends Optional<PostLikeAttributes, 'id' | 'createdAt'> {
}
export declare class PostLike extends Model<PostLikeAttributes, PostLikeCreationAttributes> implements PostLikeAttributes {
    id: number;
    postId: number;
    userId: number;
    createdAt: Date;
    readonly post?: Post;
    readonly user?: User;
    static associations: {
        post: Association<PostLike, Post>;
        user: Association<PostLike, User>;
    };
    static toggleLike(postId: number, userId: number): Promise<{
        action: 'liked' | 'unliked';
        likeCount: number;
    }>;
    static getLikeUsers(postId: number, limit?: number, offset?: number): Promise<{
        rows: PostLike[];
        count: number;
    }>;
    static isLikedByUser(postId: number, userId: number): Promise<boolean>;
    static getUserLikedPosts(userId: number, limit?: number, offset?: number): Promise<{
        rows: PostLike[];
        count: number;
    }>;
    static deleteByPostId(postId: number): Promise<number>;
    static deleteByUserId(userId: number): Promise<number>;
}
//# sourceMappingURL=PostLike.d.ts.map