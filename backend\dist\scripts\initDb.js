#!/usr/bin/env ts-node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const migrationRunner_1 = require("../utils/migrationRunner");
const seedRunner_1 = require("../utils/seedRunner");
const connection_1 = require("../database/connection");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
class InitDbScript {
    constructor() {
        this.migrationRunner = new migrationRunner_1.MigrationRunner();
        this.seedRunner = new seedRunner_1.SeedRunner();
    }
    async run(options = {}) {
        const { force = false, seedOnly = false, migrateOnly = false } = options;
        console.log('🚀 Starting database initialization...');
        console.log('='.repeat(60));
        try {
            const connectionInfo = connection_1.dbConnection.getConnectionInfo();
            console.log(`📊 Database: ${connectionInfo.database}`);
            console.log(`🏠 Host: ${connectionInfo.host}:${connectionInfo.port}`);
            console.log(`👤 User: ${connectionInfo.username}`);
            console.log(`🔧 Mode: ${force ? 'FORCE' : 'NORMAL'}`);
            console.log('');
            if (!seedOnly) {
                console.log('📦 Step 1: Creating database if not exists...');
                const dbCreated = await connection_1.dbConnection.createDatabaseIfNotExists();
                if (!dbCreated) {
                    console.error('❌ Failed to create database!');
                    process.exit(1);
                }
                console.log('✅ Database ready!');
                console.log('');
            }
            console.log('🔍 Step 2: Testing database connection...');
            const isConnected = await connection_1.dbConnection.testConnection();
            if (!isConnected) {
                console.error('❌ Database connection failed!');
                process.exit(1);
            }
            console.log('✅ Database connection successful!');
            console.log('');
            if (!seedOnly) {
                console.log('🏗️  Step 3: Running database migrations...');
                await this.migrationRunner.runMigrations();
                console.log('✅ Migrations completed!');
                console.log('');
            }
            if (!migrateOnly) {
                console.log('🌱 Step 4: Running seed data...');
                await this.seedRunner.runSeeders(force);
                console.log('✅ Seed data completed!');
                console.log('');
            }
            console.log('='.repeat(60));
            console.log('🎉 Database initialization completed successfully!');
            console.log('');
            console.log('📋 Summary:');
            console.log(`   • Database: ${connectionInfo.database}`);
            console.log(`   • Migrations: ${seedOnly ? 'Skipped' : 'Completed'}`);
            console.log(`   • Seed data: ${migrateOnly ? 'Skipped' : 'Completed'}`);
            console.log('');
            console.log('🚀 Your blog system is ready to use!');
        }
        catch (error) {
            console.error('');
            console.error('='.repeat(60));
            console.error('❌ Database initialization failed!');
            console.error('Error details:', error);
            process.exit(1);
        }
        finally {
            await connection_1.dbConnection.closeConnection();
        }
    }
    showHelp() {
        console.log(`
📚 Database Initialization Tool

Usage:
  npm run db:init [options]         Initialize database with default settings
  ts-node src/scripts/initDb.ts [options]    Run initialization directly

Options:
  --force          Force re-run all operations (recreate seed data)
  --seed-only      Only run seed data (skip migrations)
  --migrate-only   Only run migrations (skip seed data)
  --help, -h       Show this help message

Environment Variables:
  DB_HOST      Database host (default: localhost)
  DB_PORT      Database port (default: 3306)
  DB_NAME      Database name (default: person-blog)
  DB_USER      Database user (default: person-blog)
  DB_PASSWORD  Database password

Examples:
  npm run db:init                    # Full initialization
  npm run db:init -- --force         # Force re-initialization
  npm run db:init -- --seed-only     # Only run seed data
  npm run db:init -- --migrate-only  # Only run migrations
`);
    }
}
function parseArgs(args) {
    return {
        force: args.includes('--force'),
        seedOnly: args.includes('--seed-only'),
        migrateOnly: args.includes('--migrate-only'),
        help: args.includes('--help') || args.includes('-h')
    };
}
async function main() {
    const args = process.argv.slice(2);
    const options = parseArgs(args);
    const script = new InitDbScript();
    if (options.help) {
        script.showHelp();
        return;
    }
    if (options.seedOnly && options.migrateOnly) {
        console.error('❌ Error: --seed-only and --migrate-only cannot be used together');
        process.exit(1);
    }
    await script.run({
        force: options.force,
        seedOnly: options.seedOnly,
        migrateOnly: options.migrateOnly
    });
}
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
if (require.main === module) {
    main().catch((error) => {
        console.error('Script execution failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=initDb.js.map