# 数据库管理文档

## 概述

本项目使用 MySQL 数据库，通过 Sequelize ORM 进行数据库操作。数据库包含博客系统和说说功能的完整数据结构。

## 数据库结构

### 核心表

| 表名 | 描述 | 主要字段 |
|------|------|----------|
| `users` | 用户表 | id, username, email, password_hash |
| `categories` | 分类表 | id, name, slug, parent_id |
| `tags` | 标签表 | id, name, slug |
| `articles` | 文章表 | id, title, content, status, author_id |
| `comments` | 评论表 | id, content, article_id, post_id, author_id |

### 说说功能表

| 表名 | 描述 | 主要字段 |
|------|------|----------|
| `posts` | 说说表 | id, content, images, visibility, author_id |
| `post_likes` | 说说点赞表 | id, post_id, user_id |

### 关联表

| 表名 | 描述 | 主要字段 |
|------|------|----------|
| `article_tags` | 文章标签关联 | article_id, tag_id |

## 数据库脚本

### 可用命令

```bash
# 初始化数据库（包含迁移和种子数据）
npm run db:init

# 检查数据库状态
npm run db:check

# 清理数据库（删除所有数据）
npm run db:clean

# 仅运行迁移
npm run migrate

# 编译项目
npm run build
```

### 生产环境命令

```bash
# 生产环境初始化
npm run db:init:prod

# 生产环境迁移
npm run migrate:prod
```

## 数据库迁移

### 迁移文件列表

1. `001-create-users.ts` - 创建用户表
2. `002-create-tags.ts` - 创建标签表
3. `003-create-articles.ts` - 创建文章表
4. `004-create-article-tags.ts` - 创建文章标签关联表
5. `005-create-categories.ts` - 创建分类表
6. `006-add-category-to-articles.ts` - 为文章添加分类字段
7. `007-create-comments.ts` - 创建评论表
8. `008-create-posts.ts` - 创建说说表
9. `009-create-post-likes.ts` - 创建说说点赞表
10. `010-add-post-support-to-comments.ts` - 为评论表添加说说支持

### 创建新迁移

```bash
# 在 src/database/migrations/ 目录下创建新文件
# 文件名格式: XXX-description.ts
# 其中 XXX 是三位数字序号
```

## 种子数据

### 种子文件列表

1. `001-admin-user.ts` - 创建管理员和示例用户
2. `002-categories.ts` - 创建示例分类
3. `003-tags.ts` - 创建示例标签
4. `004-articles.ts` - 创建示例文章
5. `005-comments.ts` - 创建示例评论
6. `006-posts.ts` - 创建示例说说
7. `007-post-likes.ts` - 创建示例点赞

### 默认用户账号

| 用户名 | 邮箱 | 密码 | 角色 |
|--------|------|------|------|
| admin | <EMAIL> | admin123 | 管理员 |
| john_doe | <EMAIL> | user123 | 普通用户 |
| jane_smith | <EMAIL> | user123 | 普通用户 |
| tech_writer | <EMAIL> | user123 | 普通用户 |

⚠️ **生产环境请务必修改默认密码！**

## 说说功能特性

### 可见性控制

- `public` - 公开，所有人可见
- `private` - 私密，仅作者可见

### 数据关系

- 用户可以发布多个说说
- 说说可以被多个用户点赞
- 说说可以有多个评论
- 评论支持回复功能

### 索引优化

- `posts` 表在 `author_id`, `created_at`, `visibility` 上有索引
- `post_likes` 表在 `(post_id, user_id)` 上有唯一索引
- `comments` 表在 `post_id` 上有索引

## 数据库配置

### 环境变量

```env
DB_HOST=localhost
DB_PORT=3306
DB_NAME=personal_blog
DB_USER=root
DB_PASSWORD=your_password
```

### 连接配置

数据库连接配置位于 `src/config/database.ts`，支持：

- 连接池管理
- 自动重连
- 查询日志
- 事务支持

## 备份与恢复

### 备份数据库

```bash
mysqldump -u username -p database_name > backup.sql
```

### 恢复数据库

```bash
mysql -u username -p database_name < backup.sql
```

## 性能优化建议

1. **索引优化**
   - 为常用查询字段添加索引
   - 避免过多索引影响写性能

2. **查询优化**
   - 使用 `EXPLAIN` 分析查询计划
   - 避免 N+1 查询问题

3. **数据分页**
   - 大数据量查询使用分页
   - 考虑使用游标分页

4. **缓存策略**
   - 热点数据使用 Redis 缓存
   - 静态内容使用 CDN

## 故障排除

### 常见问题

1. **连接失败**
   - 检查数据库服务是否启动
   - 验证连接参数是否正确

2. **迁移失败**
   - 检查数据库权限
   - 查看错误日志

3. **数据不一致**
   - 运行 `npm run db:check` 检查状态
   - 必要时重新初始化数据库

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看数据库慢查询日志
tail -f /var/log/mysql/slow.log
```

## 开发指南

### 添加新表

1. 创建迁移文件
2. 定义 Sequelize 模型
3. 配置模型关系
4. 添加种子数据（可选）
5. 更新文档

### 修改表结构

1. 创建新的迁移文件
2. 实现 `up` 和 `down` 方法
3. 测试迁移和回滚
4. 更新相关模型

### 最佳实践

- 始终为迁移提供回滚方法
- 使用事务确保数据一致性
- 为重要操作添加日志
- 定期备份生产数据
