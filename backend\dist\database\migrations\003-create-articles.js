"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('articles', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        title: {
            type: sequelize_1.DataTypes.STRING(200),
            allowNull: false
        },
        slug: {
            type: sequelize_1.DataTypes.STRING(200),
            allowNull: false,
            unique: true
        },
        content: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: false
        },
        excerpt: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        status: {
            type: sequelize_1.DataTypes.ENUM('draft', 'published'),
            allowNull: false,
            defaultValue: 'draft'
        },
        author_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        published_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true
        }
    });
    try {
        await queryInterface.addIndex('articles', ['status']);
    }
    catch {
        console.log('Index articles_status already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('articles', ['published_at']);
    }
    catch {
        console.log('Index articles_published_at already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('articles', ['slug']);
    }
    catch {
        console.log('Index articles_slug already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('articles', ['author_id']);
    }
    catch {
        console.log('Index articles_author_id already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('articles', ['created_at']);
    }
    catch {
        console.log('Index articles_created_at already exists, skipping...');
    }
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('articles');
};
exports.down = down;
//# sourceMappingURL=003-create-articles.js.map