{"version": 3, "file": "cleanDb.js", "sourceRoot": "", "sources": ["../../src/scripts/cleanDb.ts"], "names": [], "mappings": ";;;;;;AAEA,iDAA8C;AAC9C,uDAAqD;AACrD,oDAA2B;AAG3B,gBAAM,CAAC,MAAM,EAAE,CAAA;AAMf,MAAM,aAAa;IAIjB,KAAK,CAAC,GAAG;QACP,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAA;QAC9C,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;QAE5B,IAAI,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAChD,MAAM,WAAW,GAAG,MAAM,yBAAY,CAAC,cAAc,EAAE,CAAA;YAEvD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAA;gBAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACjB,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAChD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YAGf,MAAM,cAAc,GAAG,oBAAS,CAAC,iBAAiB,EAAE,CAAA;YAGpD,MAAM,cAAc,CAAC,SAAS,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAA;YAClE,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAA;YAG7C,MAAM,YAAY,GAAG;gBACnB,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,YAAY;aACb,CAAA;YAED,KAAK,MAAM,SAAS,IAAI,YAAY,EAAE,CAAC;gBACrC,IAAI,CAAC;oBACH,MAAM,cAAc,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;oBACzC,OAAO,CAAC,GAAG,CAAC,uBAAuB,SAAS,EAAE,CAAC,CAAA;gBACjD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,8BAA8B,CAAC,CAAA;gBACnE,CAAC;YACH,CAAC;YAGD,MAAM,cAAc,CAAC,SAAS,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAA;YAClE,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAA;YAE/C,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACf,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;YAC5B,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAA;YACzD,OAAO,CAAC,GAAG,CAAC,mEAAmE,CAAC,CAAA;QAElF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;YACjB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;YAC9B,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAA;YAC3C,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAA;YACtC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACjB,CAAC;gBAAS,CAAC;YAET,MAAM,yBAAY,CAAC,eAAe,EAAE,CAAA;QACtC,CAAC;IACH,CAAC;CACF;AAKD,KAAK,UAAU,IAAI;IACjB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IAGlC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC;;;;;;;;;;CAUf,CAAC,CAAA;QACE,OAAM;IACR,CAAC;IAGD,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAA;IACtE,OAAO,CAAC,GAAG,CAAC,mEAAmE,CAAC,CAAA;IAChF,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAA;IAGpE,MAAM,MAAM,GAAG,IAAI,aAAa,EAAE,CAAA;IAClC,MAAM,MAAM,CAAC,GAAG,EAAE,CAAA;AACpB,CAAC;AAGD,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;IACnD,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAA;IACpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC,CAAC,CAAA;AAEF,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;IACxC,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAA;IAC3C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC,CAAC,CAAA;AAGF,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACrB,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC,CAAC,CAAA;AACJ,CAAC"}