import { Router } from 'express'
import authRoutes from './auth'
import articleRoutes from './article'
import tagRoutes from './tag'
import categoryRoutes from './category'
import commentRoutes from './comment'
import postRoutes from './post'

import timelineRoutes from './timeline'
import uploadRoutes from './upload'

const router = Router()

// 挂载认证相关路由到 /auth 路径
router.use('/auth', authRoutes)

// 挂载文章相关路由到 /articles 路径
router.use('/articles', articleRoutes)

// 挂载标签相关路由到 /tags 路径
router.use('/tags', tagRoutes)

// 挂载分类相关路由到 /categories 路径
router.use('/categories', categoryRoutes)

// 挂载评论相关路由到 /comments 路径
router.use('/comments', commentRoutes)

// 挂载说说相关路由到 /posts 路径
router.use('/posts', postRoutes)



// 挂载时间线相关路由到 /timeline 路径
router.use('/timeline', timelineRoutes)

// 挂载上传相关路由到 /upload 路径
router.use('/upload', uploadRoutes)

/**
 * 处理根路径的GET请求，返回API基本信息和可用端点
 * @param req - Express请求对象
 * @param res - Express响应对象
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Personal Blog API',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      articles: '/api/articles',
      tags: '/api/tags',
      categories: '/api/categories',
      comments: '/api/comments',
      posts: '/api/posts',
      timeline: '/api/timeline',
      upload: '/api/upload',
      health: '/health',
      docs: '/api-docs'
    }
  })
})

export default router