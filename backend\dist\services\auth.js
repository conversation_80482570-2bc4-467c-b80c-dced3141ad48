"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = exports.generateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const User_1 = require("../models/User");
const password_1 = require("../utils/password");
const generateToken = (payload) => {
    const jwtSecret = process.env.JWT_SECRET;
    const jwtExpiresIn = process.env.JWT_EXPIRES_IN || '7d';
    if (!jwtSecret) {
        throw new Error('JWT_SECRET is not configured');
    }
    return jsonwebtoken_1.default.sign(payload, jwtSecret, {
        expiresIn: jwtExpiresIn,
        issuer: 'personal-blog-system',
        audience: 'personal-blog-users'
    });
};
exports.generateToken = generateToken;
class AuthService {
    static generateToken(payload) {
        const jwtSecret = process.env.JWT_SECRET;
        const jwtExpiresIn = process.env.JWT_EXPIRES_IN || '7d';
        if (!jwtSecret) {
            throw new Error('JWT_SECRET is not configured');
        }
        return jsonwebtoken_1.default.sign(payload, jwtSecret, {
            expiresIn: jwtExpiresIn,
            issuer: 'personal-blog-system',
            audience: 'personal-blog-users'
        });
    }
    static verifyToken(token) {
        const jwtSecret = process.env.JWT_SECRET;
        if (!jwtSecret) {
            throw new Error('JWT_SECRET is not configured');
        }
        try {
            const decoded = jsonwebtoken_1.default.verify(token, jwtSecret, {
                issuer: 'personal-blog-system',
                audience: 'personal-blog-users'
            });
            return decoded;
        }
        catch (error) {
            if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
                throw new Error('Token has expired');
            }
            else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
                throw new Error('Invalid token');
            }
            else {
                throw new Error('Token verification failed');
            }
        }
    }
    static async login(credentials) {
        try {
            const { username, password } = credentials;
            const user = await User_1.User.findByUsername(username);
            if (!user) {
                return {
                    success: false,
                    message: 'Invalid username or password'
                };
            }
            const isPasswordValid = await (0, password_1.comparePassword)(password, user.passwordHash);
            if (!isPasswordValid) {
                return {
                    success: false,
                    message: 'Invalid username or password'
                };
            }
            const payload = {
                id: user.id,
                username: user.username,
                email: user.email
            };
            const token = (0, exports.generateToken)(payload);
            return {
                success: true,
                user: payload,
                token
            };
        }
        catch (error) {
            console.error('Login error:', error);
            return {
                success: false,
                message: 'Authentication failed'
            };
        }
    }
    static async validateToken(token) {
        try {
            const payload = this.verifyToken(token);
            const user = await User_1.User.findByPk(payload.id);
            if (!user) {
                return {
                    success: false,
                    message: 'User not found'
                };
            }
            return {
                success: true,
                user: payload
            };
        }
        catch (error) {
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Token validation failed'
            };
        }
    }
    static extractTokenFromHeader(authHeader) {
        if (!authHeader) {
            return null;
        }
        const parts = authHeader.split(' ');
        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            return null;
        }
        return parts[1] || null;
    }
    static async refreshToken(token) {
        try {
            const payload = this.verifyToken(token);
            const user = await User_1.User.findByPk(payload.id);
            if (!user) {
                return {
                    success: false,
                    message: 'User not found'
                };
            }
            const newToken = (0, exports.generateToken)(payload);
            return {
                success: true,
                user: payload,
                token: newToken
            };
        }
        catch (error) {
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Token refresh failed'
            };
        }
    }
}
exports.AuthService = AuthService;
//# sourceMappingURL=auth.js.map