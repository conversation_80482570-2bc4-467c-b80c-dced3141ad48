export declare class DatabaseConnection {
    private static instance;
    private constructor();
    static getInstance(): DatabaseConnection;
    createDatabaseIfNotExists(): Promise<boolean>;
    testConnection(): Promise<boolean>;
    getConnectionInfo(): {
        host: string;
        port: number;
        database: string;
        username: string;
    };
    closeConnection(): Promise<void>;
}
export declare const dbConnection: DatabaseConnection;
//# sourceMappingURL=connection.d.ts.map