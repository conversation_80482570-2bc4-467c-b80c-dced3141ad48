import { Request, Response, NextFunction } from 'express'
import { Article } from '../models/Article'
import { Tag } from '../models/Tag'
import { User } from '../models/User'
import { Category } from '../models/Category'
import { Op } from 'sequelize'


/**
 * 扩展 Express 的 Request 接口，添加用户认证信息
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
  }
}


/**
 * 获取文章列表
 * @param req - Express 请求对象，包含查询参数：page(页码), limit(每页数量), status(状态), tag(标签), category(分类), search(搜索关键词), author(作者ID)
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getArticles = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const {
      page = '1',
      limit = '10',
      status,
      tag,
      category,
      search,
      author
    } = req.query

    const pageNum = parseInt(page as string, 10)
    const limitNum = parseInt(limit as string, 10)
    const offset = (pageNum - 1) * limitNum

    // 构建查询条件
    const whereClause: any = {}

    if (status && ['draft', 'published'].includes(status as string)) {
      whereClause.status = status
    }

    if (search) {
      whereClause[Op.or] = [
        { title: { [Op.like]: `%${search}%` } },
        { content: { [Op.like]: `%${search}%` } }
      ]
    }

    if (author) {
      whereClause.authorId = author
    }

    if (category) {
      whereClause.categoryId = category
    }

    // 构建关联查询条件
    const includeClause: any[] = [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'username']
      },
      {
        model: Tag,
        as: 'tags',
        attributes: ['id', 'name', 'slug'],
        through: { attributes: [] }
      },
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'name', 'slug']
      }
    ]

    if (tag) {
      includeClause[1].where = { slug: tag }
      includeClause[1].required = true
    }

    const { rows: articles, count } = await Article.findAndCountAll({
      where: whereClause,
      include: includeClause,
      order: [['createdAt', 'DESC']],
      limit: limitNum,
      offset,
      distinct: true
    })

    const totalPages = Math.ceil(count / limitNum)
    const hasNextPage = pageNum < totalPages
    const hasPrevPage = pageNum > 1

    res.json({
      success: true,
      data: {
        articles,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: count,
          itemsPerPage: limitNum,
          hasNextPage,
          hasPrevPage
        }
      }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 根据ID或slug获取单篇文章
 * @param req - Express 请求对象，包含文章ID或slug参数
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getArticle = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    if (!id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ID',
          message: 'Article ID is required'
        }
      })
      return
    }

    let article: Article | null = null

    // 根据ID是数字还是字符串判断使用哪种查找方式
    if (/^\d+$/.test(id)) {
      article = await Article.findByPk(id, {
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['id', 'username']
          },
          {
            model: Tag,
            as: 'tags',
            attributes: ['id', 'name', 'slug'],
            through: { attributes: [] }
          },
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name', 'slug']
          }
        ]
      })
    } else {
      article = await Article.findBySlug(id)
    }

    if (!article) {
      res.status(404).json({
        success: false,
        error: {
          code: 'ARTICLE_NOT_FOUND',
          message: 'Article not found'
        }
      })
      return
    }

    res.json({
      success: true,
      data: { article }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 创建新文章
 * @param req - AuthenticatedRequest 请求对象，包含文章标题、内容等信息以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const createArticle = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { title, content, excerpt, status = 'draft', tags = [], slug, categoryId } = req.body

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    const articleData: any = {
      title,
      content,
      excerpt,
      status,
      authorId: req.user.id,
      categoryId: categoryId || null
    }

    // 如果用户提供了slug，则使用用户的slug
    if (slug && slug.trim()) {
      articleData.slug = slug.trim()
    }

    const article = await Article.create(articleData)

    // 处理标签关联
    if (tags.length > 0) {
      const tagInstances = await Promise.all(
        tags.map(async (tagName: string) => {
          const [tag] = await Tag.findOrCreate({
            where: { name: tagName.trim() },
            defaults: {
              name: tagName.trim(),
              slug: tagName.trim().toLowerCase().replace(/\s+/g, '-')
            }
          })
          return tag
        })
      )

      await (article as any).setTags(tagInstances)
    }

    const createdArticle = await Article.findByPk(article.id, {
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username']
        },
        {
          model: Tag,
          as: 'tags',
          attributes: ['id', 'name', 'slug'],
          through: { attributes: [] }
        },
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        }
      ]
    })

    res.status(201).json({
      success: true,
      data: { article: createdArticle }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 更新文章
 * @param req - AuthenticatedRequest 请求对象，包含要更新的文章ID和更新数据以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const updateArticle = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params
    const { title, content, excerpt, status, tags, slug, categoryId } = req.body

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    const article = await Article.findByPk(id)

    if (!article) {
      res.status(404).json({
        success: false,
        error: {
          code: 'ARTICLE_NOT_FOUND',
          message: 'Article not found'
        }
      })
      return
    }

    // 验证权限：只能更新自己的文章
    if (article.authorId !== req.user.id) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only edit your own articles'
        }
      })
      return
    }

    // 构建更新数据
    const updateData: any = {}
    if (title !== undefined) updateData.title = title
    if (content !== undefined) updateData.content = content
    if (excerpt !== undefined) updateData.excerpt = excerpt
    if (status !== undefined) updateData.status = status
    if (slug !== undefined && slug.trim()) updateData.slug = slug.trim()
    if (categoryId !== undefined) updateData.categoryId = categoryId

    await article.update(updateData)

    // 更新标签关联
    if (tags !== undefined) {
      if (tags.length > 0) {
        const tagInstances = await Promise.all(
          tags.map(async (tagName: string) => {
            const [tag] = await Tag.findOrCreate({
              where: { name: tagName.trim() },
              defaults: {
                name: tagName.trim(),
                slug: tagName.trim().toLowerCase().replace(/\s+/g, '-')
              }
            })
            return tag
          })
        )

        await (article as any).setTags(tagInstances)
      } else {
        await (article as any).setTags([])
      }
    }

    const updatedArticle = await Article.findByPk(article.id, {
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username']
        },
        {
          model: Tag,
          as: 'tags',
          attributes: ['id', 'name', 'slug'],
          through: { attributes: [] }
        },
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        }
      ]
    })

    res.json({
      success: true,
      data: { article: updatedArticle }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 删除单篇文章
 * @param req - AuthenticatedRequest 请求对象，包含要删除的文章ID以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const deleteArticle = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    const article = await Article.findByPk(id)

    if (!article) {
      res.status(404).json({
        success: false,
        error: {
          code: 'ARTICLE_NOT_FOUND',
          message: 'Article not found'
        }
      })
      return
    }

    // 验证权限：只能删除自己的文章
    if (article.authorId !== req.user.id) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only delete your own articles'
        }
      })
      return
    }

    await article.destroy()

    res.json({
      success: true,
      message: 'Article deleted successfully'
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 批量删除文章
 * @param req - AuthenticatedRequest 请求对象，包含要删除的文章ID数组以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const deleteArticles = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { ids } = req.body

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    if (!Array.isArray(ids) || ids.length === 0) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'IDs array is required and cannot be empty'
        }
      })
      return
    }

    // 只能删除自己的文章
    const articles = await Article.findAll({
      where: {
        id: ids,
        authorId: req.user.id
      }
    })

    if (articles.length === 0) {
      res.status(404).json({
        success: false,
        error: {
          code: 'NO_ARTICLES_FOUND',
          message: 'No articles found or you do not have permission to delete them'
        }
      })
      return
    }

    await Article.destroy({
      where: {
        id: articles.map(article => article.id)
      }
    })

    res.json({
      success: true,
      message: `${articles.length} articles deleted successfully`,
      data: {
        deletedCount: articles.length,
        deletedIds: articles.map(article => article.id)
      }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 发布文章
 * @param req - AuthenticatedRequest 请求对象，包含要发布的文章ID以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const publishArticle = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    const article = await Article.findByPk(id)

    if (!article) {
      res.status(404).json({
        success: false,
        error: {
          code: 'ARTICLE_NOT_FOUND',
          message: 'Article not found'
        }
      })
      return
    }

    // 验证权限：只能发布自己的文章
    if (article.authorId !== req.user.id) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only publish your own articles'
        }
      })
      return
    }

    await article.publish()

    const updatedArticle = await Article.findByPk(article.id, {
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username']
        },
        {
          model: Tag,
          as: 'tags',
          attributes: ['id', 'name', 'slug'],
          through: { attributes: [] }
        },
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        }
      ]
    })

    res.json({
      success: true,
      data: { article: updatedArticle }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 取消发布文章
 * @param req - AuthenticatedRequest 请求对象，包含要取消发布的文章ID以及用户认证信息
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const unpublishArticle = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
      return
    }

    const article = await Article.findByPk(id)

    if (!article) {
      res.status(404).json({
        success: false,
        error: {
          code: 'ARTICLE_NOT_FOUND',
          message: 'Article not found'
        }
      })
      return
    }

    // 验证权限：只能取消发布自己的文章
    if (article.authorId !== req.user.id) {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only unpublish your own articles'
        }
      })
      return
    }

    await article.unpublish()

    const updatedArticle = await Article.findByPk(article.id, {
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username']
        },
        {
          model: Tag,
          as: 'tags',
          attributes: ['id', 'name', 'slug'],
          through: { attributes: [] }
        },
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        }
      ]
    })

    res.json({
      success: true,
      data: { article: updatedArticle }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 获取文章导航信息（上一篇/下一篇）
 * @param req - Express 请求对象，包含当前文章ID
 * @param res - Express 响应对象
 * @param next - Express 下一步中间件函数
 * @returns Promise<void>
 */
export const getArticleNavigation = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    if (!id || !/^\d+$/.test(id)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ID',
          message: 'Valid article ID is required'
        }
      })
      return
    }

    const articleId = parseInt(id, 10)

    const currentArticle = await Article.findByPk(articleId)

    if (!currentArticle || currentArticle.status !== 'published') {
      res.status(404).json({
        success: false,
        error: {
          code: 'ARTICLE_NOT_FOUND',
          message: 'Published article not found'
        }
      })
      return
    }

    // 查找上一篇文章（创建时间更晚的已发布文章）
    const prevArticle = await Article.findOne({
      where: {
        status: 'published',
        createdAt: { [Op.gt]: currentArticle.createdAt }
      },
      order: [['createdAt', 'ASC']],
      attributes: ['id', 'title', 'slug'],
      limit: 1
    })

    // 查找下一篇文章（创建时间更早的已发布文章）
    const nextArticle = await Article.findOne({
      where: {
        status: 'published',
        createdAt: { [Op.lt]: currentArticle.createdAt }
      },
      order: [['createdAt', 'DESC']],
      attributes: ['id', 'title', 'slug'],
      limit: 1
    })

    res.json({
      success: true,
      data: {
        navigation: {
          prev: prevArticle ? {
            id: prevArticle.id,
            title: prevArticle.title,
            slug: prevArticle.slug
          } : null,
          next: nextArticle ? {
            id: nextArticle.id,
            title: nextArticle.title,
            slug: nextArticle.slug
          } : null
        }
      }
    })
  } catch (error) {
    next(error)
  }
}