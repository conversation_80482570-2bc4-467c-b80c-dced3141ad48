"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Tag = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const slug_1 = require("../utils/slug");
class Tag extends sequelize_1.Model {
    static async findBySlug(slug) {
        return this.findOne({ where: { slug } });
    }
    static async findByName(name) {
        return this.findOne({ where: { name } });
    }
    static async findOrCreateByName(name) {
        const slug = (0, slug_1.generateSlug)(name);
        return this.findOrCreate({
            where: { name },
            defaults: { name, slug }
        });
    }
}
exports.Tag = Tag;
Tag.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    name: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        validate: {
            len: [1, 50],
            notEmpty: true
        }
    },
    slug: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        validate: {
            len: [1, 50],
            is: /^[a-z0-9-]+$/i
        }
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'Tag',
    tableName: 'tags',
    timestamps: true,
    updatedAt: false,
    underscored: true,
    hooks: {
        beforeValidate: (tag) => {
            if (tag.name && !tag.slug) {
                tag.slug = (0, slug_1.generateSlug)(tag.name);
            }
        }
    }
});
//# sourceMappingURL=Tag.js.map