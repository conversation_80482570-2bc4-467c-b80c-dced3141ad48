import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: number;
        username: string;
        email: string;
    };
}
export declare class UploadController {
    static uploadSingle: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
    static uploadMultiple: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
    static handleSingleUpload(req: AuthenticatedRequest, res: Response): Promise<void>;
    static handleMultipleUpload(req: AuthenticatedRequest, res: Response): Promise<void>;
    static deleteImage(req: AuthenticatedRequest, res: Response): Promise<void>;
    static getImageInfo(req: Request, res: Response): Promise<void>;
    static getUploadConfig(req: Request, res: Response): void;
}
export {};
//# sourceMappingURL=upload.d.ts.map