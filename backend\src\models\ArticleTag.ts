import { DataTypes, Model } from 'sequelize'
import { sequelize } from '../config/database'


/**
 * 文章标签关联关系的属性接口
 * 定义了文章和标签之间多对多关系所需的属性
 */
export interface ArticleTagAttributes {
  /**
   * 文章ID
   */
  articleId: number
  /**
   * 标签ID
   */
  tagId: number
}


/**
 * 文章标签关联模型类
 * 用于表示文章和标签之间的多对多关联关系
 * 继承自Sequelize的Model基类并实现ArticleTagAttributes接口
 */
export class ArticleTag extends Model<ArticleTagAttributes> implements ArticleTagAttributes {
  /**
   * 文章ID属性
   * 通过感叹号操作符标记为非空属性
   */
  public articleId!: number
  /**
   * 标签ID属性
   * 通过感叹号操作符标记为非空属性
   */
  public tagId!: number
}


/**
 * 初始化文章标签关联模型的数据库映射配置
 * 配置了表结构、字段映射关系和模型选项
 */
ArticleTag.init(
  {
    /**
     * 文章ID字段配置
     * 映射到数据库中的article_id字段
     */
    articleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'article_id',
      references: {
        model: 'articles',
        key: 'id'
      }
    },
    /**
     * 标签ID字段配置
     * 映射到数据库中的tag_id字段
     */
    tagId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'tag_id',
      references: {
        model: 'tags',
        key: 'id'
      }
    }
  },
  {
    /**
     * 模型配置选项
     * 包含数据库连接实例和模型元数据配置
     */
    sequelize,
    modelName: 'ArticleTag',
    tableName: 'article_tags',
    timestamps: false,
    underscored: true
  }
)