import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 执行数据库迁移，创建文章表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 无返回值的异步函数
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 创建文章表，包含文章的基本信息和元数据
  await queryInterface.createTable('articles', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false
    },
    slug: {
      type: DataTypes.STRING(200),
      allowNull: false,
      unique: true
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    excerpt: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('draft', 'published'),
      allowNull: false,
      defaultValue: 'draft'
    },
    author_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    published_at: {
      type: DataTypes.DATE,
      allowNull: true
    }
  })

  // 为常用查询字段创建索引以提高查询性能
  // 使用 try-catch 来避免重复创建索引的错误
  try {
    await queryInterface.addIndex('articles', ['status'])
  } catch {
    console.log('Index articles_status already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('articles', ['published_at'])
  } catch {
    console.log('Index articles_published_at already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('articles', ['slug'])
  } catch {
    console.log('Index articles_slug already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('articles', ['author_id'])
  } catch {
    console.log('Index articles_author_id already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('articles', ['created_at'])
  } catch {
    console.log('Index articles_created_at already exists, skipping...')
  }
}

/**
 * 回滚数据库迁移，删除文章表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 无返回值的异步函数
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('articles')
}