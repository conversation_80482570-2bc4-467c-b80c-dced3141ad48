"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.batchDeleteTags = exports.updateTag = exports.getArticleTags = exports.removeTagFromArticle = exports.addTagToArticle = exports.getPopularTags = exports.getTagStats = exports.deleteTag = exports.createTag = exports.getTagArticles = exports.getTags = void 0;
const models_1 = require("../models");
const sequelize_1 = require("sequelize");
const getTags = async (req, res) => {
    try {
        const tags = await models_1.Tag.findAll({
            attributes: ['id', 'name', 'slug', 'createdAt'],
            order: [['name', 'ASC']]
        });
        const tagsWithCount = await Promise.all(tags.map(async (tag) => {
            const articleCount = await models_1.Article.count({
                include: [{
                        model: models_1.Tag,
                        as: 'tags',
                        where: { id: tag.id },
                        through: { attributes: [] }
                    }],
                where: { status: 'published' }
            });
            return {
                ...tag.toJSON(),
                articleCount
            };
        }));
        res.json({
            success: true,
            tags: tagsWithCount
        });
    }
    catch (error) {
        console.error('Error fetching tags:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'FETCH_TAGS_ERROR',
                message: 'Failed to fetch tags'
            }
        });
    }
};
exports.getTags = getTags;
const getTagArticles = async (req, res) => {
    try {
        const { tagName } = req.params;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const tag = await models_1.Tag.findOne({
            where: {
                [sequelize_1.Op.or]: [
                    { name: tagName },
                    { slug: tagName }
                ]
            }
        });
        if (!tag) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'TAG_NOT_FOUND',
                    message: 'Tag not found'
                }
            });
            return;
        }
        const { count, rows: articles } = await models_1.Article.findAndCountAll({
            where: { status: 'published' },
            include: [{
                    model: models_1.Tag,
                    as: 'tags',
                    where: { id: tag.id },
                    through: { attributes: [] }
                }],
            order: [['publishedAt', 'DESC']],
            limit,
            offset,
            distinct: true
        });
        const totalPages = Math.ceil(count / limit);
        res.json({
            success: true,
            articles,
            tag,
            pagination: {
                page,
                limit,
                total: count,
                totalPages
            }
        });
    }
    catch (error) {
        console.error('Error fetching tag articles:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'FETCH_TAG_ARTICLES_ERROR',
                message: 'Failed to fetch articles for tag'
            }
        });
    }
};
exports.getTagArticles = getTagArticles;
const createTag = async (req, res) => {
    try {
        const { name, slug: providedSlug } = req.body;
        if (!name || !name.trim()) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: 'Tag name is required'
                }
            });
            return;
        }
        let slug = providedSlug?.trim();
        if (!slug) {
            slug = name.toLowerCase()
                .replace(/[^\w\s-]/g, '')
                .replace(/[\s_-]+/g, '-')
                .replace(/^-+|-+$/g, '');
        }
        if (!/^[a-z0-9-]+$/.test(slug)) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: 'Slug can only contain lowercase letters, numbers and hyphens'
                }
            });
            return;
        }
        const existingTag = await models_1.Tag.findOne({
            where: {
                [sequelize_1.Op.or]: [
                    { name: name.trim() },
                    { slug }
                ]
            }
        });
        if (existingTag) {
            res.status(409).json({
                success: false,
                error: {
                    code: 'TAG_EXISTS',
                    message: 'Tag already exists'
                }
            });
            return;
        }
        const tag = await models_1.Tag.create({
            name: name.trim(),
            slug
        });
        res.status(201).json({
            success: true,
            tag
        });
    }
    catch (error) {
        console.error('Error creating tag:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'CREATE_TAG_ERROR',
                message: 'Failed to create tag'
            }
        });
    }
};
exports.createTag = createTag;
const deleteTag = async (req, res) => {
    try {
        const { id } = req.params;
        const tag = await models_1.Tag.findByPk(id);
        if (!tag) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'TAG_NOT_FOUND',
                    message: 'Tag not found'
                }
            });
            return;
        }
        await tag.destroy();
        res.json({
            success: true,
            message: 'Tag deleted successfully'
        });
    }
    catch (error) {
        console.error('Error deleting tag:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'DELETE_TAG_ERROR',
                message: 'Failed to delete tag'
            }
        });
    }
};
exports.deleteTag = deleteTag;
const getTagStats = async (req, res) => {
    try {
        const totalTags = await models_1.Tag.count();
        const allTags = await models_1.Tag.findAll({
            attributes: ['id', 'name', 'slug', 'createdAt'],
            order: [['name', 'ASC']]
        });
        const tagsWithCounts = await Promise.all(allTags.map(async (tag) => {
            const articleCount = await models_1.Article.count({
                include: [{
                        model: models_1.Tag,
                        as: 'tags',
                        where: { id: tag.id },
                        through: { attributes: [] }
                    }],
                where: { status: 'published' }
            });
            return {
                ...tag.toJSON(),
                articleCount
            };
        }));
        const usedTags = tagsWithCounts.filter(tag => tag.articleCount > 0).length;
        const unusedTags = totalTags - usedTags;
        const totalArticleTagAssociations = tagsWithCounts.reduce((sum, tag) => sum + tag.articleCount, 0);
        const averageArticlesPerTag = usedTags > 0 ? totalArticleTagAssociations / usedTags : 0;
        res.json({
            success: true,
            stats: {
                totalTags,
                usedTags,
                unusedTags,
                averageArticlesPerTag: Math.round(averageArticlesPerTag * 100) / 100
            },
            tags: tagsWithCounts
        });
    }
    catch (error) {
        console.error('Error fetching tag statistics:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'FETCH_TAG_STATS_ERROR',
                message: 'Failed to fetch tag statistics'
            }
        });
    }
};
exports.getTagStats = getTagStats;
const getPopularTags = async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 10;
        const allTags = await models_1.Tag.findAll({
            attributes: ['id', 'name', 'slug', 'createdAt']
        });
        const tagsWithCounts = await Promise.all(allTags.map(async (tag) => {
            const articleCount = await models_1.Article.count({
                include: [{
                        model: models_1.Tag,
                        as: 'tags',
                        where: { id: tag.id },
                        through: { attributes: [] }
                    }],
                where: { status: 'published' }
            });
            return {
                ...tag.toJSON(),
                articleCount
            };
        }));
        const popularTags = tagsWithCounts
            .filter(tag => tag.articleCount > 0)
            .sort((a, b) => b.articleCount - a.articleCount)
            .slice(0, limit);
        res.json({
            success: true,
            tags: popularTags
        });
    }
    catch (error) {
        console.error('Error fetching popular tags:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'FETCH_POPULAR_TAGS_ERROR',
                message: 'Failed to fetch popular tags'
            }
        });
    }
};
exports.getPopularTags = getPopularTags;
const addTagToArticle = async (req, res) => {
    try {
        const { articleId, tagId } = req.body;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const article = await models_1.Article.findOne({
            where: {
                id: articleId,
                authorId: req.user.id
            }
        });
        if (!article) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'ARTICLE_NOT_FOUND',
                    message: 'Article not found or access denied'
                }
            });
            return;
        }
        const tag = await models_1.Tag.findByPk(tagId);
        if (!tag) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'TAG_NOT_FOUND',
                    message: 'Tag not found'
                }
            });
            return;
        }
        const existingAssociation = await models_1.ArticleTag.findOne({
            where: { articleId, tagId }
        });
        if (existingAssociation) {
            res.status(409).json({
                success: false,
                error: {
                    code: 'ASSOCIATION_EXISTS',
                    message: 'Tag is already associated with this article'
                }
            });
            return;
        }
        await models_1.ArticleTag.create({ articleId, tagId });
        res.status(201).json({
            success: true,
            message: 'Tag added to article successfully'
        });
    }
    catch (error) {
        console.error('Error adding tag to article:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'ADD_TAG_ERROR',
                message: 'Failed to add tag to article'
            }
        });
    }
};
exports.addTagToArticle = addTagToArticle;
const removeTagFromArticle = async (req, res) => {
    try {
        const { articleId, tagId } = req.params;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const article = await models_1.Article.findOne({
            where: {
                id: articleId,
                authorId: req.user.id
            }
        });
        if (!article) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'ARTICLE_NOT_FOUND',
                    message: 'Article not found or access denied'
                }
            });
            return;
        }
        const association = await models_1.ArticleTag.findOne({
            where: { articleId, tagId }
        });
        if (!association) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'ASSOCIATION_NOT_FOUND',
                    message: 'Tag is not associated with this article'
                }
            });
            return;
        }
        await association.destroy();
        res.json({
            success: true,
            message: 'Tag removed from article successfully'
        });
    }
    catch (error) {
        console.error('Error removing tag from article:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'REMOVE_TAG_ERROR',
                message: 'Failed to remove tag from article'
            }
        });
    }
};
exports.removeTagFromArticle = removeTagFromArticle;
const getArticleTags = async (req, res) => {
    try {
        const { articleId } = req.params;
        const article = await models_1.Article.findByPk(articleId, {
            include: [{
                    model: models_1.Tag,
                    as: 'tags',
                    attributes: ['id', 'name', 'slug', 'createdAt'],
                    through: { attributes: [] }
                }]
        });
        if (!article) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'ARTICLE_NOT_FOUND',
                    message: 'Article not found'
                }
            });
            return;
        }
        res.json({
            success: true,
            tags: article.tags || []
        });
    }
    catch (error) {
        console.error('Error fetching article tags:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'FETCH_ARTICLE_TAGS_ERROR',
                message: 'Failed to fetch article tags'
            }
        });
    }
};
exports.getArticleTags = getArticleTags;
const updateTag = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, slug: providedSlug } = req.body;
        if (!name || !name.trim()) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: 'Tag name is required'
                }
            });
            return;
        }
        const tag = await models_1.Tag.findByPk(id);
        if (!tag) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'TAG_NOT_FOUND',
                    message: 'Tag not found'
                }
            });
            return;
        }
        let slug = providedSlug?.trim();
        if (!slug) {
            slug = name.toLowerCase()
                .replace(/[^\w\s-]/g, '')
                .replace(/[\s_-]+/g, '-')
                .replace(/^-+|-+$/g, '');
        }
        if (!/^[a-z0-9-]+$/.test(slug)) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: 'Slug can only contain lowercase letters, numbers and hyphens'
                }
            });
            return;
        }
        const existingTag = await models_1.Tag.findOne({
            where: {
                [sequelize_1.Op.and]: [
                    { id: { [sequelize_1.Op.ne]: id } },
                    {
                        [sequelize_1.Op.or]: [
                            { name: name.trim() },
                            { slug }
                        ]
                    }
                ]
            }
        });
        if (existingTag) {
            res.status(409).json({
                success: false,
                error: {
                    code: 'TAG_EXISTS',
                    message: 'Tag with this name or slug already exists'
                }
            });
            return;
        }
        await tag.update({
            name: name.trim(),
            slug
        });
        res.json({
            success: true,
            tag
        });
    }
    catch (error) {
        console.error('Error updating tag:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'UPDATE_TAG_ERROR',
                message: 'Failed to update tag'
            }
        });
    }
};
exports.updateTag = updateTag;
const batchDeleteTags = async (req, res) => {
    try {
        const { tagIds } = req.body;
        if (!Array.isArray(tagIds) || tagIds.length === 0) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: 'Tag IDs array is required'
                }
            });
            return;
        }
        const validIds = tagIds.filter(id => Number.isInteger(id) && id > 0);
        if (validIds.length !== tagIds.length) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: 'All tag IDs must be valid integers'
                }
            });
            return;
        }
        const tagsToDelete = await models_1.Tag.findAll({
            where: { id: validIds }
        });
        if (tagsToDelete.length === 0) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'TAGS_NOT_FOUND',
                    message: 'No tags found with the provided IDs'
                }
            });
            return;
        }
        const deletedCount = await models_1.Tag.destroy({
            where: { id: validIds }
        });
        res.json({
            success: true,
            message: `Successfully deleted ${deletedCount} tags`,
            deletedCount,
            requestedCount: tagIds.length
        });
    }
    catch (error) {
        console.error('Error batch deleting tags:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'BATCH_DELETE_TAGS_ERROR',
                message: 'Failed to batch delete tags'
            }
        });
    }
};
exports.batchDeleteTags = batchDeleteTags;
//# sourceMappingURL=tag.js.map