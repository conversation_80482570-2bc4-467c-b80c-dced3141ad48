"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPostSecurity = exports.postSecurityMiddleware = exports.recordPostIP = exports.checkPostOwnership = exports.checkPostAccess = exports.postProfanityFilter = exports.postContentSecurity = exports.postRateLimit = void 0;
const errorHandler_1 = require("./errorHandler");
const models_1 = require("../models");
exports.postRateLimit = (() => {
    const userLastPostTime = new Map();
    const RATE_LIMIT_WINDOW = 60 * 1000;
    return (req, res, next) => {
        if (!req.user) {
            return next();
        }
        const userId = req.user.id;
        const now = Date.now();
        const lastPostTime = userLastPostTime.get(userId);
        if (lastPostTime && (now - lastPostTime) < RATE_LIMIT_WINDOW) {
            const remainingTime = Math.ceil((RATE_LIMIT_WINDOW - (now - lastPostTime)) / 1000);
            throw (0, errorHandler_1.createError)(429, `请等待 ${remainingTime} 秒后再发布新的说说`, 'RATE_LIMIT_EXCEEDED');
        }
        userLastPostTime.set(userId, now);
        if (userLastPostTime.size > 1000) {
            const cutoffTime = now - RATE_LIMIT_WINDOW;
            for (const [id, time] of userLastPostTime.entries()) {
                if (time < cutoffTime) {
                    userLastPostTime.delete(id);
                }
            }
        }
        next();
    };
})();
const postContentSecurity = (req, res, next) => {
    const { content, images } = req.body;
    if (!content) {
        return next();
    }
    const scriptPattern = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi;
    if (scriptPattern.test(content)) {
        throw (0, errorHandler_1.createError)(400, '说说内容包含禁止的脚本标签', 'SECURITY_VIOLATION');
    }
    const maliciousLinkPattern = /javascript:|data:|vbscript:|on\w+\s*=/gi;
    if (maliciousLinkPattern.test(content)) {
        throw (0, errorHandler_1.createError)(400, '说说内容包含禁止的链接或事件', 'SECURITY_VIOLATION');
    }
    if (content.length > 1000) {
        throw (0, errorHandler_1.createError)(400, '说说内容过长（最多1000个字符）', 'CONTENT_TOO_LONG');
    }
    if (!content.trim()) {
        throw (0, errorHandler_1.createError)(400, '说说内容不能为空', 'EMPTY_CONTENT');
    }
    if (images && Array.isArray(images)) {
        if (images.length > 9) {
            throw (0, errorHandler_1.createError)(400, '最多只能上传9张图片', 'TOO_MANY_IMAGES');
        }
        for (const imageUrl of images) {
            if (typeof imageUrl !== 'string' || !imageUrl.trim()) {
                throw (0, errorHandler_1.createError)(400, '图片URL格式无效', 'INVALID_IMAGE_URL');
            }
            try {
                new URL(imageUrl);
            }
            catch {
                throw (0, errorHandler_1.createError)(400, '图片URL格式无效', 'INVALID_IMAGE_URL');
            }
            const supportedFormats = /\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i;
            if (!supportedFormats.test(imageUrl)) {
                throw (0, errorHandler_1.createError)(400, '不支持的图片格式，仅支持 jpg、png、gif、webp', 'UNSUPPORTED_IMAGE_FORMAT');
            }
        }
    }
    next();
};
exports.postContentSecurity = postContentSecurity;
const postProfanityFilter = (req, res, next) => {
    const { content } = req.body;
    if (!content) {
        return next();
    }
    const profanityWords = [
        'spam', 'scam', 'fraud', 'hack', 'virus', 'phishing',
    ];
    const contentLower = content.toLowerCase();
    const foundProfanity = profanityWords.find(word => contentLower.includes(word));
    if (foundProfanity) {
        throw (0, errorHandler_1.createError)(400, '说说内容包含不当词汇', 'INAPPROPRIATE_CONTENT');
    }
    next();
};
exports.postProfanityFilter = postProfanityFilter;
const checkPostAccess = async (req, res, next) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        if (!id) {
            return next();
        }
        const post = await models_1.Post.findByPk(Number(id));
        if (!post) {
            throw (0, errorHandler_1.createError)(404, '说说不存在', 'POST_NOT_FOUND');
        }
        const canView = await post.canBeViewedBy(userId);
        if (!canView) {
            throw (0, errorHandler_1.createError)(403, '没有权限查看此说说', 'ACCESS_DENIED');
        }
        req.post = post;
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.checkPostAccess = checkPostAccess;
const checkPostOwnership = async (req, res, next) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            throw (0, errorHandler_1.createError)(401, '需要登录', 'AUTHENTICATION_REQUIRED');
        }
        const post = await models_1.Post.findByPk(Number(id));
        if (!post) {
            throw (0, errorHandler_1.createError)(404, '说说不存在', 'POST_NOT_FOUND');
        }
        if (post.authorId !== userId) {
            throw (0, errorHandler_1.createError)(403, '只能操作自己的说说', 'OWNERSHIP_REQUIRED');
        }
        req.post = post;
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.checkPostOwnership = checkPostOwnership;
const recordPostIP = (req, res, next) => {
    const getClientIP = () => {
        const forwardedFor = req.headers['x-forwarded-for'];
        const realIP = req.headers['x-real-ip'];
        if (typeof forwardedFor === 'string') {
            return forwardedFor.split(',')[0]?.trim() || 'unknown';
        }
        if (Array.isArray(forwardedFor) && forwardedFor[0]) {
            return forwardedFor[0];
        }
        if (typeof realIP === 'string') {
            return realIP;
        }
        return req.socket?.remoteAddress || 'unknown';
    };
    req.body.clientIP = getClientIP();
    next();
};
exports.recordPostIP = recordPostIP;
exports.postSecurityMiddleware = [
    exports.postContentSecurity,
    exports.postProfanityFilter,
    exports.recordPostIP
];
exports.createPostSecurity = [
    exports.postRateLimit,
    ...exports.postSecurityMiddleware
];
//# sourceMappingURL=postSecurity.js.map