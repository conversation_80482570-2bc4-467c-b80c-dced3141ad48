"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const upload_1 = require("../controllers/upload");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.get('/config', upload_1.UploadController.getUploadConfig);
router.get('/image/:filename', upload_1.UploadController.getImageInfo);
router.post('/image', auth_1.authenticateToken, (req, res, next) => {
    upload_1.UploadController.uploadSingle(req, res, (err) => {
        if (err) {
            res.status(400).json({
                success: false,
                message: '图片上传失败',
                error: err.message
            });
            return;
        }
        next();
    });
}, upload_1.UploadController.handleSingleUpload);
router.post('/images', auth_1.authenticateToken, (req, res, next) => {
    upload_1.UploadController.uploadMultiple(req, res, (err) => {
        if (err) {
            res.status(400).json({
                success: false,
                message: '图片上传失败',
                error: err.message
            });
            return;
        }
        next();
    });
}, upload_1.UploadController.handleMultipleUpload);
router.delete('/image/:filename', auth_1.authenticateToken, upload_1.UploadController.deleteImage);
exports.default = router;
//# sourceMappingURL=upload.js.map