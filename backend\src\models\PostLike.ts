import { DataTypes, Model, Optional, Association } from 'sequelize'
import { sequelize } from '../config/database'
import { User } from './User'
import { Post } from './Post'

/**
 * 说说点赞模型的属性接口定义
 */
export interface PostLikeAttributes {
  id: number
  postId: number
  userId: number
  createdAt: Date
}

/**
 * 说说点赞创建时的属性接口定义，部分字段为可选
 */
export interface PostLikeCreationAttributes extends Optional<PostLikeAttributes, 'id' | 'createdAt'> { }

/**
 * 说说点赞模型类，用于与数据库中的 post_likes 表进行交互
 * 实现了 PostLikeAttributes 接口，并扩展了 Sequelize 的 Model 类
 */
export class PostLike extends Model<PostLikeAttributes, PostLikeCreationAttributes> implements PostLikeAttributes {
  public id!: number
  public postId!: number
  public userId!: number
  public createdAt!: Date

  // 关联关系
  public readonly post?: Post
  public readonly user?: User

  public static associations: {
    post: Association<PostLike, Post>
    user: Association<PostLike, User>
  }

  /**
   * 切换用户对说说的点赞状态
   * @param postId - 说说ID
   * @param userId - 用户ID
   * @returns 返回操作结果和当前状态
   */
  public static async toggleLike(postId: number, userId: number): Promise<{
    action: 'liked' | 'unliked'
    likeCount: number
  }> {
    const existingLike = await PostLike.findOne({
      where: { postId, userId }
    })

    if (existingLike) {
      // 如果已经点赞，则取消点赞
      await existingLike.destroy()
      const likeCount = await PostLike.count({ where: { postId } })
      return { action: 'unliked', likeCount }
    } else {
      // 如果未点赞，则添加点赞
      await PostLike.create({ postId, userId })
      const likeCount = await PostLike.count({ where: { postId } })
      return { action: 'liked', likeCount }
    }
  }

  /**
   * 获取说说的点赞用户列表
   * @param postId - 说说ID
   * @param limit - 限制数量
   * @param offset - 偏移量
   * @returns 返回点赞用户列表
   */
  public static async getLikeUsers(postId: number, limit: number = 20, offset: number = 0) {
    return await PostLike.findAndCountAll({
      where: { postId },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    })
  }

  /**
   * 检查用户是否已点赞指定说说
   * @param postId - 说说ID
   * @param userId - 用户ID
   * @returns 返回是否已点赞
   */
  public static async isLikedByUser(postId: number, userId: number): Promise<boolean> {
    const like = await PostLike.findOne({
      where: { postId, userId }
    })
    return !!like
  }

  /**
   * 获取用户点赞的说说列表
   * @param userId - 用户ID
   * @param limit - 限制数量
   * @param offset - 偏移量
   * @returns 返回用户点赞的说说列表
   */
  public static async getUserLikedPosts(userId: number, limit: number = 20, offset: number = 0) {
    return await PostLike.findAndCountAll({
      where: { userId },
      include: [
        {
          model: Post,
          as: 'post',
          include: [
            {
              model: User,
              as: 'author',
              attributes: ['id', 'username', 'email']
            }
          ]
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    })
  }

  /**
   * 批量删除说说的所有点赞记录（用于删除说说时）
   * @param postId - 说说ID
   * @returns 返回删除的记录数
   */
  public static async deleteByPostId(postId: number): Promise<number> {
    return await PostLike.destroy({
      where: { postId }
    })
  }

  /**
   * 批量删除用户的所有点赞记录（用于删除用户时）
   * @param userId - 用户ID
   * @returns 返回删除的记录数
   */
  public static async deleteByUserId(userId: number): Promise<number> {
    return await PostLike.destroy({
      where: { userId }
    })
  }
}

/**
 * 初始化说说点赞模型的数据库映射配置
 * 包含字段定义、验证规则、索引等
 */
PostLike.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    postId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'post_id',
      references: {
        model: 'posts',
        key: 'id'
      }
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  },
  {
    sequelize,
    modelName: 'PostLike',
    tableName: 'post_likes',
    timestamps: true,
    updatedAt: false, // 点赞记录不需要更新时间
    indexes: [
      {
        unique: true,
        fields: ['post_id', 'user_id'] // 确保同一用户不能重复点赞同一说说
      },
      {
        fields: ['post_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['created_at']
      }
    ]
  }
)
