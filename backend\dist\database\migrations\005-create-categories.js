"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('categories', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        name: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false
        },
        slug: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            unique: true
        },
        description: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        parent_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'categories',
                key: 'id'
            },
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        },
        sort: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    try {
        await queryInterface.addIndex('categories', ['slug'], {
            name: 'categories_slug'
        });
    }
    catch {
        console.log('Index categories_slug already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('categories', ['parent_id'], {
            name: 'categories_parent_id'
        });
    }
    catch {
        console.log('Index categories_parent_id already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('categories', ['sort'], {
            name: 'categories_sort'
        });
    }
    catch {
        console.log('Index categories_sort already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('categories', ['name'], {
            name: 'categories_name'
        });
    }
    catch {
        console.log('Index categories_name already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('categories', ['created_at'], {
            name: 'categories_created_at'
        });
    }
    catch {
        console.log('Index categories_created_at already exists, skipping...');
    }
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('categories');
};
exports.down = down;
//# sourceMappingURL=005-create-categories.js.map