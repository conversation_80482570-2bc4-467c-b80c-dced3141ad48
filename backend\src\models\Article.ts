import { DataTypes, Model, Optional, Association } from 'sequelize'
import { sequelize } from '../config/database'
import { generateSlug } from '../utils/slug'
import { User } from './User'
import { Tag } from './Tag'
import { Category } from './Category'


/**
 * 文章模型的属性接口定义
 */
export interface ArticleAttributes {
  id: number
  title: string
  slug: string
  content: string
  excerpt?: string
  status: 'draft' | 'published'
  authorId: number
  categoryId?: number
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
}


/**
 * 文章创建时的属性接口定义，部分字段为可选
 */
export interface ArticleCreationAttributes extends Optional<ArticleAttributes, 'id' | 'slug' | 'excerpt' | 'categoryId' | 'createdAt' | 'updatedAt' | 'publishedAt'> { }


/**
 * 文章模型类，继承自Sequelize的Model基类
 * 实现了文章的基本操作和关联关系
 */
export class Article extends Model<ArticleAttributes, ArticleCreationAttributes> implements ArticleAttributes {
  public id!: number
  public title!: string
  public slug!: string
  public content!: string
  public excerpt?: string
  public status!: 'draft' | 'published'
  public authorId!: number
  public categoryId?: number
  public createdAt!: Date
  public updatedAt!: Date
  public publishedAt?: Date


  public readonly author?: User
  public readonly tags?: Tag[]
  public readonly category?: Category

  public static associations: {
    author: Association<Article, User>
    tags: Association<Article, Tag>
    category: Association<Article, Category>
  }


  /**
   * 将文章状态设置为已发布
   * @returns Promise<void>
   */
  public async publish(): Promise<void> {
    this.status = 'published'
    this.publishedAt = new Date()
    await this.save()
  }

  /**
   * 将文章状态设置为草稿
   * @returns Promise<void>
   */
  public async unpublish(): Promise<void> {
    this.status = 'draft'
    this.publishedAt = null as any
    await this.save()
  }

  /**
   * 生成文章摘要
   * @param length 摘要长度，默认200字符
   * @returns 生成的文章摘要字符串
   */
  public generateExcerpt(length: number = 200): string {
    if (this.excerpt) return this.excerpt


    const plainText = this.content.replace(/<[^>]*>/g, '')
    return plainText.length > length
      ? plainText.substring(0, length) + '...'
      : plainText
  }


  /**
   * 根据slug查找文章，并包含作者和标签信息
   * @param slug 文章的唯一标识符
   * @returns Promise<Article | null> 找到的文章对象或null
   */
  public static async findBySlug(slug: string): Promise<Article | null> {
    return this.findOne({
      where: { slug },
      include: [
        { model: User, as: 'author', attributes: ['id', 'username'] },
        { model: Tag, as: 'tags' },
        { model: Category, as: 'category', attributes: ['id', 'name', 'slug'] }
      ]
    })
  }

  /**
   * 查找已发布的文章列表，支持分页和标签筛选
   * @param options 查询选项
   * @param options.limit 返回记录数限制
   * @param options.offset 偏移量
   * @param options.tagId 标签ID筛选条件
   * @returns Promise<{ rows: Article[], count: number }> 包含文章列表和总数的对象
   */
  public static async findPublished(options: {
    limit?: number
    offset?: number
    tagId?: number
    categoryId?: number
  } = {}): Promise<{ rows: Article[], count: number }> {
    const { limit = 10, offset = 0, tagId, categoryId } = options

    const whereClause: any = { status: 'published' }
    const includeClause: any[] = [
      { model: User, as: 'author', attributes: ['id', 'username'] },
      { model: Tag, as: 'tags' },
      { model: Category, as: 'category', attributes: ['id', 'name', 'slug'] }
    ]

    // 如果指定了分类ID，则添加分类筛选条件
    if (categoryId) {
      whereClause.categoryId = categoryId
    }

    // 如果指定了标签ID，则添加标签筛选条件
    if (tagId) {
      includeClause[1].where = { id: tagId }
      includeClause[1].required = true
    }

    return this.findAndCountAll({
      where: whereClause,
      include: includeClause,
      order: [['publishedAt', 'DESC']],
      limit,
      offset,
      distinct: true
    })
  }
}


/**
 * 初始化文章模型的数据库映射配置
 * 包含字段定义、验证规则、钩子函数等
 */
Article.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        len: [1, 200],
        notEmpty: true
      }
    },
    slug: {
      type: DataTypes.STRING(200),
      allowNull: false,
      unique: true,
      validate: {
        len: [1, 200],
        is: /^[a-z0-9-]+$/i
      }
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    excerpt: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('draft', 'published'),
      allowNull: false,
      defaultValue: 'draft'
    },
    authorId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'author_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    categoryId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'category_id',
      references: {
        model: 'categories',
        key: 'id'
      }
    },
    publishedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'published_at'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  },
  {
    sequelize,
    modelName: 'Article',
    tableName: 'articles',
    timestamps: true,
    underscored: true,
    /**
     * 模型钩子函数配置
     */
    hooks: {
      /**
       * 在验证前生成唯一slug
       */
      beforeValidate: async (article: Article) => {
        if (article.title && !article.slug) {
          let baseSlug = generateSlug(article.title)
          let slug = baseSlug
          let counter = 1


          while (await Article.findOne({ where: { slug } })) {
            slug = `${baseSlug}-${counter}`
            counter++
          }

          article.slug = slug
        }
      },
      /**
       * 在更新前处理发布状态变更
       */
      beforeUpdate: (article: Article) => {
        if (article.changed('status') && article.status === 'published' && !article.publishedAt) {
          article.publishedAt = new Date()
        }
      }
    }
  }
)