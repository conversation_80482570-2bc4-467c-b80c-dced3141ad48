import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 执行数据库迁移，创建标签表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 无返回值的异步函数
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 创建标签表，包含id、名称、别名和创建时间字段
  await queryInterface.createTable('tags', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true
    },
    slug: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  })

  // 为标签表的名称字段添加索引
  // 使用 try-catch 来避免重复创建索引的错误
  try {
    await queryInterface.addIndex('tags', ['name'])
  } catch {
    console.log('Index tags_name already exists, skipping...')
  }

  // 为标签表的别名字段添加索引
  try {
    await queryInterface.addIndex('tags', ['slug'])
  } catch {
    console.log('Index tags_slug already exists, skipping...')
  }
}

/**
 * 回滚数据库迁移，删除标签表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 无返回值的异步函数
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  // 删除标签表
  await queryInterface.dropTable('tags')
}