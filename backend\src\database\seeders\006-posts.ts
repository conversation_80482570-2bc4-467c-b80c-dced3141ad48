import { QueryInterface } from 'sequelize'

/**
 * 创建说说种子数据
 * 包括各种类型的说说，用于测试和演示
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('Creating sample posts...')

  // 检查是否已存在说说
  const existingPosts = await queryInterface.select(null, 'posts', {})
  if (existingPosts.length > 0) {
    console.log('Posts already exist, skipping...')
    return
  }

  // 获取用户ID
  const users = await queryInterface.select(null, 'users', {})
  if (users.length === 0) {
    console.log('No users found, skipping posts creation...')
    return
  }

  const adminUser = users.find((u: any) => u.username === 'admin') as any
  const regularUser = users.find((u: any) => u.username === 'john_doe') as any
  const testUser = users.find((u: any) => u.username === 'jane_smith') as any

  if (!adminUser || !regularUser) {
    console.log('Required users not found, skipping posts creation...')
    return
  }

  // 定义说说数据
  const posts = [
    // 管理员的说说
    {
      content: '欢迎来到我们的社交平台！这里是一个分享生活、交流想法的地方。希望大家能够友善互动，共同营造一个温馨的社区环境。',
      images: null,
      visibility: 'public',
      location: '北京市',
      author_id: adminUser.id,
      created_at: new Date('2024-01-01 12:00:00'),
      updated_at: new Date('2024-01-01 12:00:00')
    },
    {
      content: '今天天气真不错，阳光明媚，适合出去走走。大家有什么好的户外活动推荐吗？',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
        'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800'
      ]),
      visibility: 'public',
      location: '中央公园',
      author_id: adminUser.id,
      created_at: new Date('2024-01-02 09:30:00'),
      updated_at: new Date('2024-01-02 09:30:00')
    },
    {
      content: '刚刚完成了一个重要的项目，感觉很有成就感！团队合作真的很重要。',
      images: null,
      visibility: 'private',
      location: null,
      author_id: adminUser.id,
      created_at: new Date('2024-01-03 18:45:00'),
      updated_at: new Date('2024-01-03 18:45:00')
    },

    // 普通用户的说说
    {
      content: '第一次使用这个平台，感觉界面很友好！期待在这里认识更多朋友。',
      images: null,
      visibility: 'public',
      location: null,
      author_id: regularUser.id,
      created_at: new Date('2024-01-01 15:20:00'),
      updated_at: new Date('2024-01-01 15:20:00')
    },
    {
      content: '今天学会了一道新菜，味道还不错！分享给大家看看。',
      images: JSON.stringify([
        'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800',
        'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=800',
        'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=800'
      ]),
      visibility: 'public',
      location: '家里厨房',
      author_id: regularUser.id,
      created_at: new Date('2024-01-02 19:15:00'),
      updated_at: new Date('2024-01-02 19:15:00')
    },
    {
      content: '周末计划去爬山，有人一起吗？',
      images: null,
      visibility: 'public',
      location: null,
      author_id: regularUser.id,
      created_at: new Date('2024-01-04 10:00:00'),
      updated_at: new Date('2024-01-04 10:00:00')
    },
    {
      content: '今天心情不太好，希望明天会更好。',
      images: null,
      visibility: 'private',
      location: null,
      author_id: regularUser.id,
      created_at: new Date('2024-01-04 22:30:00'),
      updated_at: new Date('2024-01-04 22:30:00')
    },

    // 测试用户的说说（如果存在）
    ...(testUser ? [
      {
        content: '测试发布一条说说，看看功能是否正常。',
        images: null,
        visibility: 'public',
        location: null,
        author_id: testUser.id,
        created_at: new Date('2024-01-03 14:20:00'),
        updated_at: new Date('2024-01-03 14:20:00')
      },
      {
        content: '分享一些美丽的风景照片，希望大家喜欢！',
        images: JSON.stringify([
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
          'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800',
          'https://images.unsplash.com/photo-1426604966848-d7adac402bff?w=800',
          'https://images.unsplash.com/photo-1501594907352-04cda38ebc29?w=800'
        ]),
        visibility: 'public',
        location: '黄山',
        author_id: testUser.id,
        created_at: new Date('2024-01-05 08:00:00'),
        updated_at: new Date('2024-01-05 08:00:00')
      }
    ] : [])
  ]

  // 插入说说数据
  await queryInterface.bulkInsert('posts', posts)
  console.log(`Created ${posts.length} sample posts`)
}

/**
 * 删除说说种子数据
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('Removing sample posts...')
  await queryInterface.bulkDelete('posts', {}, {})
  console.log('Sample posts removed')
}
