"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const up = async (queryInterface) => {
    console.log('Creating sample post likes...');
    const existingLikes = await queryInterface.select(null, 'post_likes', {});
    if (existingLikes.length > 0) {
        console.log('Post likes already exist, skipping...');
        return;
    }
    const users = await queryInterface.select(null, 'users', {});
    const posts = await queryInterface.select(null, 'posts', {});
    if (users.length === 0 || posts.length === 0) {
        console.log('No users or posts found, skipping post likes creation...');
        return;
    }
    const adminUser = users.find((u) => u.username === 'admin');
    const regularUser = users.find((u) => u.username === 'user');
    const testUser = users.find((u) => u.username === 'testuser');
    if (!adminUser || !regularUser) {
        console.log('Required users not found, skipping post likes creation...');
        return;
    }
    const postLikes = [];
    for (const post of posts) {
        const postData = post;
        if (postData.author_id === adminUser.id) {
            postLikes.push({
                post_id: postData.id,
                user_id: regularUser.id,
                created_at: new Date(new Date(postData.created_at).getTime() + 30 * 60 * 1000)
            });
            if (testUser) {
                postLikes.push({
                    post_id: postData.id,
                    user_id: testUser.id,
                    created_at: new Date(new Date(postData.created_at).getTime() + 60 * 60 * 1000)
                });
            }
        }
        if (postData.author_id === regularUser.id) {
            postLikes.push({
                post_id: postData.id,
                user_id: adminUser.id,
                created_at: new Date(new Date(postData.created_at).getTime() + 45 * 60 * 1000)
            });
            if (testUser && postData.visibility === 'public') {
                postLikes.push({
                    post_id: postData.id,
                    user_id: testUser.id,
                    created_at: new Date(new Date(postData.created_at).getTime() + 90 * 60 * 1000)
                });
            }
        }
        if (testUser && postData.author_id === testUser.id) {
            postLikes.push({
                post_id: postData.id,
                user_id: adminUser.id,
                created_at: new Date(new Date(postData.created_at).getTime() + 20 * 60 * 1000)
            });
            postLikes.push({
                post_id: postData.id,
                user_id: regularUser.id,
                created_at: new Date(new Date(postData.created_at).getTime() + 75 * 60 * 1000)
            });
        }
    }
    const otherUsers = users.filter((u) => u.username !== 'admin' &&
        u.username !== 'user' &&
        u.username !== 'testuser');
    for (const user of otherUsers) {
        const userData = user;
        const userPosts = posts.filter((p) => p.author_id === userData.id);
        for (const post of userPosts) {
            const postData = post;
            if (Math.random() > 0.3) {
                postLikes.push({
                    post_id: postData.id,
                    user_id: adminUser.id,
                    created_at: new Date(new Date(postData.created_at).getTime() + Math.random() * 2 * 60 * 60 * 1000)
                });
            }
            if (Math.random() > 0.5) {
                postLikes.push({
                    post_id: postData.id,
                    user_id: regularUser.id,
                    created_at: new Date(new Date(postData.created_at).getTime() + Math.random() * 3 * 60 * 60 * 1000)
                });
            }
            if (testUser && Math.random() > 0.4) {
                postLikes.push({
                    post_id: postData.id,
                    user_id: testUser.id,
                    created_at: new Date(new Date(postData.created_at).getTime() + Math.random() * 4 * 60 * 60 * 1000)
                });
            }
        }
    }
    if (postLikes.length > 0) {
        await queryInterface.bulkInsert('post_likes', postLikes);
        console.log(`Created ${postLikes.length} sample post likes`);
    }
    else {
        console.log('No post likes to create');
    }
};
exports.up = up;
const down = async (queryInterface) => {
    console.log('Removing sample post likes...');
    await queryInterface.bulkDelete('post_likes', {}, {});
    console.log('Sample post likes removed');
};
exports.down = down;
//# sourceMappingURL=008-post-likes.js.map