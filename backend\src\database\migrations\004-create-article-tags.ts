import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 数据库迁移函数，用于创建文章标签关联表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 无返回值的异步函数
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 创建文章标签关联表，建立文章与标签的多对多关系
  await queryInterface.createTable('article_tags', {
    article_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'articles',
        key: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    },
    tag_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'tags',
        key: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    }
  })

  // 为文章ID和标签ID分别创建索引，提高查询性能
  // 使用 try-catch 来避免重复创建索引的错误
  try {
    await queryInterface.addIndex('article_tags', ['article_id'])
  } catch {
    console.log('Index article_tags_article_id already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('article_tags', ['tag_id'])
  } catch {
    console.log('Index article_tags_tag_id already exists, skipping...')
  }
}

/**
 * 数据库回滚函数，用于删除文章标签关联表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 无返回值的异步函数
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('article_tags')
}