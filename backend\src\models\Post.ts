import { DataTypes, Model, Optional, Association } from 'sequelize'
import { sequelize } from '../config/database'
import { User } from './User'
import { Comment } from './Comment'

/**
 * 说说动态模型的属性接口定义
 */
export interface PostAttributes {
  id: number
  content: string
  images?: string[] // JSON格式存储图片URL数组
  visibility: 'public' | 'private'
  location?: string
  authorId: number
  createdAt: Date
  updatedAt: Date
}

/**
 * 说说创建时的属性接口定义，部分字段为可选
 */
export interface PostCreationAttributes extends Optional<PostAttributes, 'id' | 'images' | 'location' | 'createdAt' | 'updatedAt'> { }

/**
 * 说说动态模型类，用于与数据库中的 posts 表进行交互
 * 实现了 PostAttributes 接口，并扩展了 Sequelize 的 Model 类
 */
export class Post extends Model<PostAttributes, PostCreationAttributes> implements PostAttributes {
  public id!: number
  public content!: string
  public images?: string[]
  public visibility!: 'public' | 'private'
  public location?: string
  public authorId!: number
  public createdAt!: Date
  public updatedAt!: Date

  // 关联关系
  public readonly author?: User
  public readonly comments?: Comment[]
  public readonly likes?: any[] // PostLike模型稍后定义

  public static associations: {
    author: Association<Post, User>
    comments: Association<Post, Comment>
    likes: Association<Post, any>
  }

  /**
   * 获取说说的点赞数量
   * @returns 返回点赞数量
   */
  public async getLikeCount(): Promise<number> {
    const { PostLike } = await import('./PostLike')
    return await PostLike.count({
      where: { postId: this.id }
    })
  }

  /**
   * 获取说说的评论数量
   * @returns 返回评论数量
   */
  public async getCommentCount(): Promise<number> {
    return await Comment.count({
      where: { postId: this.id }
    })
  }

  /**
   * 检查用户是否已点赞该说说
   * @param userId - 用户ID
   * @returns 返回是否已点赞
   */
  public async isLikedByUser(userId: number): Promise<boolean> {
    const { PostLike } = await import('./PostLike')
    const like = await PostLike.findOne({
      where: { postId: this.id, userId }
    })
    return !!like
  }

  /**
   * 检查用户是否有权限查看该说说
   * @param userId - 用户ID
   * @returns 返回是否有权限查看
   */
  public async canBeViewedBy(userId?: number): Promise<boolean> {
    // 公开动态所有人都可以查看
    if (this.visibility === 'public') {
      return true
    }

    // 私密动态只有作者可以查看
    if (this.visibility === 'private') {
      return userId === this.authorId
    }

    return false
  }

  /**
   * 获取说说的完整信息，包括作者、点赞数、评论数等
   * @param userId - 当前用户ID（用于检查是否已点赞）
   * @returns 返回完整的说说信息
   */
  public async getFullInfo(userId?: number) {
    const [likeCount, commentCount, isLiked] = await Promise.all([
      this.getLikeCount(),
      this.getCommentCount(),
      userId ? this.isLikedByUser(userId) : false
    ])

    return {
      id: this.id,
      content: this.content,
      images: this.images || [],
      visibility: this.visibility,
      location: this.location,
      authorId: this.authorId,
      author: this.author,
      likeCount,
      commentCount,
      isLiked,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    }
  }
}

/**
 * 初始化说说模型的数据库映射配置
 * 包含字段定义、验证规则等
 */
Post.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 1000] // 限制说说内容长度为1-1000字符
      }
    },
    images: {
      type: DataTypes.JSON,
      allowNull: true,
      validate: {
        isValidImageArray(value: any) {
          if (value !== null && value !== undefined) {
            if (!Array.isArray(value)) {
              throw new Error('图片必须是数组格式')
            }
            if (value.length > 9) {
              throw new Error('最多只能上传9张图片')
            }
            for (const url of value) {
              if (typeof url !== 'string' || !url.trim()) {
                throw new Error('图片URL必须是有效的字符串')
              }
            }
          }
        }
      }
    },
    visibility: {
      type: DataTypes.ENUM('public', 'private'),
      allowNull: false,
      defaultValue: 'public'
    },
    location: {
      type: DataTypes.STRING(200),
      allowNull: true,
      validate: {
        len: [0, 200]
      }
    },
    authorId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'author_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  },
  {
    sequelize,
    modelName: 'Post',
    tableName: 'posts',
    timestamps: true,
    indexes: [
      {
        fields: ['author_id']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['visibility']
      },
      {
        fields: ['author_id', 'created_at']
      }
    ]
  }
)
