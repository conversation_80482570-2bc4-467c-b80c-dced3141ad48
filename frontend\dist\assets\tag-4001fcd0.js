import{J as c,K as n,L as u,M as i,r as o}from"./index-1ed5a19c.js";const g={async getTags(){try{return(await c(()=>n.get("/tags"),"正在加载标签...")).data.tags}catch(t){u(t,"获取标签列表失败")}},async getTagArticles(t,a=1,s=10){try{const l=await c(()=>n.get(`/tags/${t}/articles`,{params:{page:a,limit:s}}),"正在加载标签文章...");return{success:l.success,articles:l.data.articles,pagination:l.data.pagination}}catch(l){u(l,"获取标签文章失败")}},async createTag(t){try{return(await c(()=>n.post("/tags",{name:t}),"正在创建标签...")).data.tag}catch(a){u(a,"创建标签失败")}},async deleteTag(t){try{await c(()=>n.delete(`/tags/${t}`),"正在删除标签...")}catch(a){u(a,"删除标签失败")}}},h=i("tag",()=>{const t=o([]),a=o(!1),s=o(null);return{tags:t,loading:a,error:s,fetchTags:async()=>{a.value=!0,s.value=null;try{const r=await g.getTags();return t.value=r,r}catch(r){throw s.value=r.message,r}finally{a.value=!1}},createTag:async r=>{a.value=!0,s.value=null;try{const e=await g.createTag(r);return t.value.push(e),e}catch(e){throw s.value=e.message,e}finally{a.value=!1}},deleteTag:async r=>{a.value=!0,s.value=null;try{await g.deleteTag(r),t.value=t.value.filter(e=>e.id!==r)}catch(e){throw s.value=e.message,e}finally{a.value=!1}},getTagBySlug:r=>t.value.find(e=>e.slug===r),getPopularTags:async(r=10)=>{a.value=!0,s.value=null;try{return await g.getPopularTags(r)}catch(e){throw s.value=e.message,e}finally{a.value=!1}}}});export{h as u};
