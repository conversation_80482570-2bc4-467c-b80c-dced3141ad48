import{d as y,q as b,r as p,c as V,k,a as l,e as s,s as x,v as g,x as _,t as u,b as d,h as q,y as A,o as n,_ as L}from"./index-1ed5a19c.js";const S={class:"login-container"},B={class:"login-card"},M={class:"form-group"},N=["disabled"],C={key:0,class:"error-message"},D={class:"form-group"},E=["disabled"],F={key:0,class:"error-message"},R={key:0,class:"error-message general-error"},T=["disabled"],U={key:0,class:"loading-spinner"},z=y({__name:"Login",setup(I){const i=A(),v=b(),r=p({username:"",password:""}),t=p(!1),a=p({username:"",password:"",general:""}),f=V(()=>r.value.username.length>=3&&r.value.password.length>=6&&!t.value),m=()=>{a.value={username:"",password:"",general:""}},h=()=>{m();let o=!0;return r.value.username.length<3?(a.value.username="用户名至少需要3个字符",o=!1):/^[a-zA-Z0-9]+$/.test(r.value.username)||(a.value.username="用户名只能包含字母和数字",o=!1),r.value.password.length<6&&(a.value.password="密码至少需要6个字符",o=!1),o},w=async()=>{if(h()){t.value=!0,m();try{const o=await v.login(r.value.username,r.value.password);if(o.success){const e=i.currentRoute.value.query.redirect;i.push(e||"/admin")}else a.value.general=o.error||"登录失败，请重试"}catch(o){a.value.general=o.message||"登录失败，请重试"}finally{t.value=!1}}};return k(async()=>{v.isAuthenticated&&await v.checkAuth()&&i.push("/admin")}),(o,e)=>(n(),l("div",S,[s("div",B,[e[4]||(e[4]=s("div",{class:"login-header"},[s("h1",null,"登录"),s("p",null,"登录到个人博客管理系统")],-1)),s("form",{onSubmit:x(w,["prevent"]),class:"login-form"},[s("div",M,[e[2]||(e[2]=s("label",{for:"username"},"用户名",-1)),g(s("input",{id:"username","onUpdate:modelValue":e[0]||(e[0]=c=>r.value.username=c),type:"text",required:"",disabled:t.value,placeholder:"请输入用户名",autocomplete:"username"},null,8,N),[[_,r.value.username]]),a.value.username?(n(),l("div",C,u(a.value.username),1)):d("",!0)]),s("div",D,[e[3]||(e[3]=s("label",{for:"password"},"密码",-1)),g(s("input",{id:"password","onUpdate:modelValue":e[1]||(e[1]=c=>r.value.password=c),type:"password",required:"",disabled:t.value,placeholder:"请输入密码",autocomplete:"current-password"},null,8,E),[[_,r.value.password]]),a.value.password?(n(),l("div",F,u(a.value.password),1)):d("",!0)]),a.value.general?(n(),l("div",R,u(a.value.general),1)):d("",!0),s("button",{type:"submit",disabled:t.value||!f.value,class:"login-button"},[t.value?(n(),l("span",U)):d("",!0),q(" "+u(t.value?"登录中...":"登录"),1)],8,T)],32),e[5]||(e[5]=s("div",{class:"login-footer"},[s("p",null,"个人博客系统 v1.0")],-1))])]))}});const $=L(z,[["__scopeId","data-v-f01d93d5"]]);export{$ as default};
