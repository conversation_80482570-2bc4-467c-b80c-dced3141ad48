import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: number;
        username: string;
    };
}
export declare const commentRateLimit: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const commentContentSecurity: (req: Request, res: Response, next: NextFunction) => void;
export declare const commentProfanityFilter: (req: Request, res: Response, next: NextFunction) => void;
export declare const checkCommentOwnership: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const recordCommentIP: (req: Request, res: Response, next: NextFunction) => void;
export declare const commentSecurityMiddleware: ((req: Request, res: Response, next: NextFunction) => void)[];
export declare const createCommentSecurity: ((req: AuthenticatedRequest, res: Response, next: NextFunction) => void)[];
export {};
//# sourceMappingURL=commentSecurity.d.ts.map