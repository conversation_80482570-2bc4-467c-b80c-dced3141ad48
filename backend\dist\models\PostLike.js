"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostLike = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const User_1 = require("./User");
const Post_1 = require("./Post");
class PostLike extends sequelize_1.Model {
    static async toggleLike(postId, userId) {
        const existingLike = await PostLike.findOne({
            where: { postId, userId }
        });
        if (existingLike) {
            await existingLike.destroy();
            const likeCount = await PostLike.count({ where: { postId } });
            return { action: 'unliked', likeCount };
        }
        else {
            await PostLike.create({ postId, userId });
            const likeCount = await PostLike.count({ where: { postId } });
            return { action: 'liked', likeCount };
        }
    }
    static async getLikeUsers(postId, limit = 20, offset = 0) {
        return await PostLike.findAndCountAll({
            where: { postId },
            include: [
                {
                    model: User_1.User,
                    as: 'user',
                    attributes: ['id', 'username', 'email']
                }
            ],
            order: [['createdAt', 'DESC']],
            limit,
            offset
        });
    }
    static async isLikedByUser(postId, userId) {
        const like = await PostLike.findOne({
            where: { postId, userId }
        });
        return !!like;
    }
    static async getUserLikedPosts(userId, limit = 20, offset = 0) {
        return await PostLike.findAndCountAll({
            where: { userId },
            include: [
                {
                    model: Post_1.Post,
                    as: 'post',
                    include: [
                        {
                            model: User_1.User,
                            as: 'author',
                            attributes: ['id', 'username', 'email']
                        }
                    ]
                }
            ],
            order: [['createdAt', 'DESC']],
            limit,
            offset
        });
    }
    static async deleteByPostId(postId) {
        return await PostLike.destroy({
            where: { postId }
        });
    }
    static async deleteByUserId(userId) {
        return await PostLike.destroy({
            where: { userId }
        });
    }
}
exports.PostLike = PostLike;
PostLike.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    postId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'post_id',
        references: {
            model: 'posts',
            key: 'id'
        }
    },
    userId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'user_id',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'PostLike',
    tableName: 'post_likes',
    timestamps: true,
    updatedAt: false,
    indexes: [
        {
            unique: true,
            fields: ['post_id', 'user_id']
        },
        {
            fields: ['post_id']
        },
        {
            fields: ['user_id']
        },
        {
            fields: ['created_at']
        }
    ]
});
//# sourceMappingURL=PostLike.js.map