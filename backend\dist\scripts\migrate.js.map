{"version": 3, "file": "migrate.js", "sourceRoot": "", "sources": ["../../src/scripts/migrate.ts"], "names": [], "mappings": ";;;;;;AAEA,8DAA0D;AAC1D,uDAAqD;AACrD,oDAA2B;AAG3B,gBAAM,CAAC,MAAM,EAAE,CAAA;AAMf,MAAM,aAAa;IAGjB;QACE,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAA;IAC9C,CAAC;IAKD,KAAK,CAAC,GAAG;QACP,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;QAChD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;QAE5B,IAAI,CAAC;YAEH,MAAM,cAAc,GAAG,yBAAY,CAAC,iBAAiB,EAAE,CAAA;YACvD,OAAO,CAAC,GAAG,CAAC,gBAAgB,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAA;YACtD,OAAO,CAAC,GAAG,CAAC,YAAY,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC,CAAA;YACrE,OAAO,CAAC,GAAG,CAAC,YAAY,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAA;YAClD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YAGf,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAChD,MAAM,WAAW,GAAG,MAAM,yBAAY,CAAC,cAAc,EAAE,CAAA;YAEvD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAA;gBAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACjB,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAChD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YAGf,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;YACvC,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAA;YAE1C,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACf,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;YAC5B,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAA;QAErD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;YACjB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;YAC9B,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAA;YACpC,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAA;YACtC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACjB,CAAC;IACH,CAAC;IAKD,QAAQ;QACN,OAAO,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;CAiBf,CAAC,CAAA;IACA,CAAC;CACF;AAKD,KAAK,UAAU,IAAI;IACjB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IAGlC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACnD,MAAM,MAAM,GAAG,IAAI,aAAa,EAAE,CAAA;QAClC,MAAM,CAAC,QAAQ,EAAE,CAAA;QACjB,OAAM;IACR,CAAC;IAGD,MAAM,MAAM,GAAG,IAAI,aAAa,EAAE,CAAA;IAClC,MAAM,MAAM,CAAC,GAAG,EAAE,CAAA;AACpB,CAAC;AAGD,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;IACnD,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAA;IACpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC,CAAC,CAAA;AAEF,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;IACxC,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAA;IAC3C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC,CAAC,CAAA;AAGF,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACrB,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC,CAAC,CAAA;AACJ,CAAC"}