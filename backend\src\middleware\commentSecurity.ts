import { Request, Response, NextFunction } from 'express'
import { createError } from './errorHandler'


/**
 * 扩展 Express 的 Request 接口，添加用户认证信息
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
  }
}


/**
 * 评论频率限制中间件
 * 防止用户短时间内发表过多评论
 */
export const commentRateLimit = (() => {
  // 存储用户最后评论时间的内存缓存
  const userLastCommentTime = new Map<number, number>()
  const RATE_LIMIT_WINDOW = 30 * 1000 // 30秒

  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next()
    }

    const userId = req.user.id
    const now = Date.now()
    const lastCommentTime = userLastCommentTime.get(userId)

    if (lastCommentTime && (now - lastCommentTime) < RATE_LIMIT_WINDOW) {
      const remainingTime = Math.ceil((RATE_LIMIT_WINDOW - (now - lastCommentTime)) / 1000)
      throw createError(
        429,
        `Please wait ${remainingTime} seconds before posting another comment`,
        'RATE_LIMIT_EXCEEDED'
      )
    }

    // 更新用户最后评论时间
    userLastCommentTime.set(userId, now)

    // 清理过期的记录（简单的内存管理）
    if (userLastCommentTime.size > 1000) {
      const cutoffTime = now - RATE_LIMIT_WINDOW
      for (const [id, time] of userLastCommentTime.entries()) {
        if (time < cutoffTime) {
          userLastCommentTime.delete(id)
        }
      }
    }

    next()
  }
})()


/**
 * 评论内容安全检查中间件
 * 检查评论内容是否包含恶意内容
 */
export const commentContentSecurity = (req: Request, res: Response, next: NextFunction): void => {
  const { content } = req.body

  if (!content) {
    return next()
  }

  // 检查是否包含恶意脚本
  const scriptPattern = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi
  if (scriptPattern.test(content)) {
    throw createError(400, 'Comment content contains prohibited script tags', 'SECURITY_VIOLATION')
  }

  // 检查是否包含恶意链接
  const maliciousLinkPattern = /javascript:|data:|vbscript:|on\w+\s*=/gi
  if (maliciousLinkPattern.test(content)) {
    throw createError(400, 'Comment content contains prohibited links or events', 'SECURITY_VIOLATION')
  }

  // 检查评论长度（额外的服务器端验证）
  if (content.length > 2000) {
    throw createError(400, 'Comment content is too long (max 2000 characters)', 'CONTENT_TOO_LONG')
  }

  // 检查是否为空内容或只包含空白字符
  if (!content.trim()) {
    throw createError(400, 'Comment content cannot be empty', 'EMPTY_CONTENT')
  }

  next()
}


/**
 * 敏感词过滤中间件
 * 检查评论内容是否包含敏感词汇
 */
export const commentProfanityFilter = (req: Request, res: Response, next: NextFunction): void => {
  const { content } = req.body

  if (!content) {
    return next()
  }

  // 简单的敏感词列表（实际应用中应该从数据库或配置文件加载）
  const profanityWords = [
    'spam', 'scam', 'fraud', 'hack', 'virus',
    // 可以根据需要添加更多敏感词
  ]

  const contentLower = content.toLowerCase()
  const foundProfanity = profanityWords.find(word => contentLower.includes(word))

  if (foundProfanity) {
    throw createError(400, 'Comment content contains inappropriate language', 'INAPPROPRIATE_CONTENT')
  }

  next()
}


/**
 * 评论权限检查中间件
 * 检查用户是否有权限对特定评论进行操作
 */
export const checkCommentOwnership = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  // 这个中间件的具体实现需要在控制器中进行，因为需要查询数据库
  // 这里只是一个占位符，实际的权限检查在控制器中完成
  next()
}


/**
 * IP地址记录中间件
 * 记录评论者的IP地址用于安全审计
 */
export const recordCommentIP = (req: Request, res: Response, next: NextFunction): void => {
  // 获取真实IP地址（考虑代理和负载均衡器）
  const getClientIP = (): string => {
    const forwardedFor = req.headers['x-forwarded-for']
    const realIP = req.headers['x-real-ip']

    if (typeof forwardedFor === 'string') {
      return forwardedFor.split(',')[0]?.trim() || 'unknown'
    }

    if (Array.isArray(forwardedFor) && forwardedFor[0]) {
      return forwardedFor[0]
    }

    if (typeof realIP === 'string') {
      return realIP
    }

    return req.socket?.remoteAddress || 'unknown'
  }

  // 将IP地址添加到请求体中，供控制器使用
  req.body.clientIP = getClientIP()

  next()
}


/**
 * 组合的评论安全中间件
 * 将多个安全检查组合在一起
 */
export const commentSecurityMiddleware = [
  commentContentSecurity,
  commentProfanityFilter,
  recordCommentIP
]


/**
 * 创建评论时的完整安全中间件链
 */
export const createCommentSecurity = [
  commentRateLimit,
  ...commentSecurityMiddleware
]
