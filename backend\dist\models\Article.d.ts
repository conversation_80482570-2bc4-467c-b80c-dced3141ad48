import { Model, Optional, Association } from 'sequelize';
import { User } from './User';
import { Tag } from './Tag';
import { Category } from './Category';
export interface ArticleAttributes {
    id: number;
    title: string;
    slug: string;
    content: string;
    excerpt?: string;
    status: 'draft' | 'published';
    authorId: number;
    categoryId?: number;
    createdAt: Date;
    updatedAt: Date;
    publishedAt?: Date;
}
export interface ArticleCreationAttributes extends Optional<ArticleAttributes, 'id' | 'slug' | 'excerpt' | 'categoryId' | 'createdAt' | 'updatedAt' | 'publishedAt'> {
}
export declare class Article extends Model<ArticleAttributes, ArticleCreationAttributes> implements ArticleAttributes {
    id: number;
    title: string;
    slug: string;
    content: string;
    excerpt?: string;
    status: 'draft' | 'published';
    authorId: number;
    categoryId?: number;
    createdAt: Date;
    updatedAt: Date;
    publishedAt?: Date;
    readonly author?: User;
    readonly tags?: Tag[];
    readonly category?: Category;
    static associations: {
        author: Association<Article, User>;
        tags: Association<Article, Tag>;
        category: Association<Article, Category>;
    };
    publish(): Promise<void>;
    unpublish(): Promise<void>;
    generateExcerpt(length?: number): string;
    static findBySlug(slug: string): Promise<Article | null>;
    static findPublished(options?: {
        limit?: number;
        offset?: number;
        tagId?: number;
        categoryId?: number;
    }): Promise<{
        rows: Article[];
        count: number;
    }>;
}
//# sourceMappingURL=Article.d.ts.map