"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const PropertySymbol = __importStar(require("../../PropertySymbol.cjs"));
const AsyncTaskManager_js_1 = __importDefault(require("../../async-task-manager/AsyncTaskManager.cjs"));
const BrowserFrameURL_js_1 = __importDefault(require("../utilities/BrowserFrameURL.cjs"));
const BrowserFrameScriptEvaluator_js_1 = __importDefault(require("../utilities/BrowserFrameScriptEvaluator.cjs"));
const BrowserFrameNavigator_js_1 = __importDefault(require("../utilities/BrowserFrameNavigator.cjs"));
const HistoryScrollRestorationEnum_js_1 = __importDefault(require("../../history/HistoryScrollRestorationEnum.cjs"));
/**
 * Browser frame used when constructing a Window instance without a browser.
 */
class DetachedBrowserFrame {
    childFrames = [];
    parentFrame = null;
    page;
    closed = false;
    [PropertySymbol.asyncTaskManager] = new AsyncTaskManager_js_1.default(this);
    [PropertySymbol.listeners] = { navigation: [] };
    [PropertySymbol.openerFrame] = null;
    [PropertySymbol.openerWindow] = null;
    [PropertySymbol.popup] = false;
    [PropertySymbol.history] = [
        {
            title: '',
            href: 'about:blank',
            state: null,
            scrollRestoration: HistoryScrollRestorationEnum_js_1.default.auto,
            method: 'GET',
            formData: null,
            isCurrent: true
        }
    ];
    /**
     * Constructor.
     *
     * @param page Page.
     * @param [window] Window.
     */
    constructor(page) {
        this.page = page;
        if (page.context.browser.contexts[0]?.pages[0]?.mainFrame) {
            this.window = new this.page.context.browser.windowClass(this);
            // Attach process level error capturing.
            if (page.context.browser[PropertySymbol.exceptionObserver]) {
                page.context.browser[PropertySymbol.exceptionObserver].observe(this.window);
            }
        }
    }
    /**
     * Returns the content.
     *
     * @returns Content.
     */
    get content() {
        if (!this.window) {
            throw new Error('The frame has been destroyed, the "window" property is not set.');
        }
        return this.window.document.documentElement.outerHTML;
    }
    /**
     * Sets the content.
     *
     * @param content Content.
     */
    set content(content) {
        if (!this.window) {
            throw new Error('The frame has been destroyed, the "window" property is not set.');
        }
        this.window.document[PropertySymbol.isFirstWrite] = true;
        this.window.document[PropertySymbol.isFirstWriteAfterOpen] = false;
        this.window.document.open();
        this.window.document.write(content);
    }
    /**
     * Returns the URL.
     *
     * @returns URL.
     */
    get url() {
        if (!this.window) {
            throw new Error('The frame has been destroyed, the "window" property is not set.');
        }
        return this.window.location.href;
    }
    /**
     * Sets the content.
     *
     * @param url URL.
     */
    set url(url) {
        if (!this.window) {
            throw new Error('The frame has been destroyed, the "window" property is not set.');
        }
        this.window[PropertySymbol.location][PropertySymbol.setURL](this, BrowserFrameURL_js_1.default.getRelativeURL(this, url).href);
    }
    /**
     * Returns document.
     *
     * @returns Document.
     */
    get document() {
        return this.window?.document ?? null;
    }
    /**
     * Returns a promise that is resolved when all resources has been loaded, fetch has completed, and all async tasks such as timers are complete.
     */
    async waitUntilComplete() {
        await Promise.all([
            this[PropertySymbol.asyncTaskManager].waitUntilComplete(),
            ...this.childFrames.map((frame) => frame.waitUntilComplete())
        ]);
    }
    /**
     * Returns a promise that is resolved when the frame has navigated and the response HTML has been written to the document.
     */
    waitForNavigation() {
        return new Promise((resolve) => this[PropertySymbol.listeners].navigation.push(resolve));
    }
    /**
     * Aborts all ongoing operations.
     */
    abort() {
        if (!this.childFrames.length) {
            return this[PropertySymbol.asyncTaskManager].abort();
        }
        return new Promise((resolve, reject) => {
            // Using Promise instead of async/await to prevent microtask
            Promise.all(this.childFrames
                .map((frame) => frame.abort())
                .concat([this[PropertySymbol.asyncTaskManager].abort()]))
                .then(() => resolve())
                .catch(reject);
        });
    }
    /**
     * Evaluates code or a VM Script in the page's context.
     *
     * @param script Script.
     * @returns Result.
     */
    evaluate(script) {
        return BrowserFrameScriptEvaluator_js_1.default.evaluate(this, script);
    }
    /**
     * Go to a page.
     *
     * @param url URL.
     * @param [options] Options.
     * @returns Response.
     */
    goto(url, options) {
        return BrowserFrameNavigator_js_1.default.navigate({
            windowClass: this.page.context.browser.windowClass,
            frame: this,
            url: url,
            goToOptions: options
        });
    }
    /**
     * Navigates back in history.
     *
     * @param [options] Options.
     */
    goBack(options) {
        return BrowserFrameNavigator_js_1.default.navigateBack({
            windowClass: this.page.context.browser.windowClass,
            frame: this,
            goToOptions: options
        });
    }
    /**
     * Navigates forward in history.
     *
     * @param [options] Options.
     */
    goForward(options) {
        return BrowserFrameNavigator_js_1.default.navigateForward({
            windowClass: this.page.context.browser.windowClass,
            frame: this,
            goToOptions: options
        });
    }
    /**
     * Navigates a delta in history.
     *
     * @param steps Steps.
     * @param [options] Options.
     */
    goSteps(steps, options) {
        return BrowserFrameNavigator_js_1.default.navigateSteps({
            windowClass: this.page.context.browser.windowClass,
            frame: this,
            steps: steps,
            goToOptions: options
        });
    }
    /**
     * Reloads the current frame.
     *
     * @param [options] Options.
     * @returns Response.
     */
    reload(options) {
        return BrowserFrameNavigator_js_1.default.reload({
            windowClass: this.page.context.browser.windowClass,
            frame: this,
            goToOptions: options
        });
    }
}
exports.default = DetachedBrowserFrame;
//# sourceMappingURL=DetachedBrowserFrame.cjs.map