import{d as n,o as a,a as o,e as t,t as d,b as _,_ as i}from"./index-1ed5a19c.js";const r={class:"loading-container"},p={key:0,class:"loading-message"},c=n({__name:"Loading",props:{message:{}},setup(l){return(e,s)=>(a(),o("div",r,[s[0]||(s[0]=t("div",{class:"loading-spinner"},null,-1)),e.message?(a(),o("p",p,d(e.message),1)):_("",!0)]))}});const m=i(c,[["__scopeId","data-v-df69371d"]]);export{m as L};
