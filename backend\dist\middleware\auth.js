"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.authenticateToken = void 0;
const auth_1 = require("../services/auth");
const errorHandler_1 = require("./errorHandler");
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = auth_1.AuthService.extractTokenFromHeader(authHeader);
        if (!token) {
            throw (0, errorHandler_1.createError)(401, 'Access token required', 'UNAUTHORIZED');
        }
        const result = await auth_1.AuthService.validateToken(token);
        if (!result.success || !result.user) {
            throw (0, errorHandler_1.createError)(403, result.message || 'Invalid or expired token', 'FORBIDDEN');
        }
        req.user = result.user;
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.authenticateToken = authenticateToken;
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = auth_1.AuthService.extractTokenFromHeader(authHeader);
        if (!token) {
            return next();
        }
        const result = await auth_1.AuthService.validateToken(token);
        if (result.success && result.user) {
            req.user = result.user;
        }
        next();
    }
    catch (error) {
        next();
    }
};
exports.optionalAuth = optionalAuth;
//# sourceMappingURL=auth.js.map