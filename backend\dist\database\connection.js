"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dbConnection = exports.DatabaseConnection = void 0;
const promise_1 = __importDefault(require("mysql2/promise"));
const database_1 = require("../config/database");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
class DatabaseConnection {
    constructor() { }
    static getInstance() {
        if (!DatabaseConnection.instance) {
            DatabaseConnection.instance = new DatabaseConnection();
        }
        return DatabaseConnection.instance;
    }
    async createDatabaseIfNotExists() {
        const { DB_HOST = 'localhost', DB_PORT = '3306', DB_NAME = 'person-blog', DB_USER = 'person-blog', DB_PASSWORD = '123456' } = process.env;
        let connection = null;
        try {
            connection = await promise_1.default.createConnection({
                host: DB_HOST,
                port: parseInt(DB_PORT),
                user: DB_USER,
                password: DB_PASSWORD
            });
            console.log('Connected to MySQL server');
            const [rows] = await connection.execute('SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?', [DB_NAME]);
            if (rows.length === 0) {
                await connection.execute(`CREATE DATABASE \`${DB_NAME}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
                console.log(`Database '${DB_NAME}' created successfully`);
                return true;
            }
            else {
                console.log(`Database '${DB_NAME}' already exists`);
                return true;
            }
        }
        catch (error) {
            console.error('Error creating database:', error);
            return false;
        }
        finally {
            if (connection) {
                await connection.end();
            }
        }
    }
    async testConnection() {
        try {
            await database_1.sequelize.authenticate();
            console.log('Database connection has been established successfully.');
            return true;
        }
        catch (error) {
            console.error('Unable to connect to the database:', error);
            return false;
        }
    }
    getConnectionInfo() {
        const { DB_HOST = 'localhost', DB_PORT = '3306', DB_NAME = 'person-blog', DB_USER = 'person-blog' } = process.env;
        return {
            host: DB_HOST,
            port: parseInt(DB_PORT),
            database: DB_NAME,
            username: DB_USER
        };
    }
    async closeConnection() {
        try {
            await database_1.sequelize.close();
            console.log('Database connection closed');
        }
        catch (error) {
            console.error('Error closing database connection:', error);
        }
    }
}
exports.DatabaseConnection = DatabaseConnection;
exports.dbConnection = DatabaseConnection.getInstance();
//# sourceMappingURL=connection.js.map