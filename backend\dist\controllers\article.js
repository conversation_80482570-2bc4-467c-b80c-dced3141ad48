"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getArticleNavigation = exports.unpublishArticle = exports.publishArticle = exports.deleteArticles = exports.deleteArticle = exports.updateArticle = exports.createArticle = exports.getArticle = exports.getArticles = void 0;
const Article_1 = require("../models/Article");
const Tag_1 = require("../models/Tag");
const User_1 = require("../models/User");
const Category_1 = require("../models/Category");
const sequelize_1 = require("sequelize");
const getArticles = async (req, res, next) => {
    try {
        const { page = '1', limit = '10', status, tag, category, search, author } = req.query;
        const pageNum = parseInt(page, 10);
        const limitNum = parseInt(limit, 10);
        const offset = (pageNum - 1) * limitNum;
        const whereClause = {};
        if (status && ['draft', 'published'].includes(status)) {
            whereClause.status = status;
        }
        if (search) {
            whereClause[sequelize_1.Op.or] = [
                { title: { [sequelize_1.Op.like]: `%${search}%` } },
                { content: { [sequelize_1.Op.like]: `%${search}%` } }
            ];
        }
        if (author) {
            whereClause.authorId = author;
        }
        if (category) {
            whereClause.categoryId = category;
        }
        const includeClause = [
            {
                model: User_1.User,
                as: 'author',
                attributes: ['id', 'username']
            },
            {
                model: Tag_1.Tag,
                as: 'tags',
                attributes: ['id', 'name', 'slug'],
                through: { attributes: [] }
            },
            {
                model: Category_1.Category,
                as: 'category',
                attributes: ['id', 'name', 'slug']
            }
        ];
        if (tag) {
            includeClause[1].where = { slug: tag };
            includeClause[1].required = true;
        }
        const { rows: articles, count } = await Article_1.Article.findAndCountAll({
            where: whereClause,
            include: includeClause,
            order: [['createdAt', 'DESC']],
            limit: limitNum,
            offset,
            distinct: true
        });
        const totalPages = Math.ceil(count / limitNum);
        const hasNextPage = pageNum < totalPages;
        const hasPrevPage = pageNum > 1;
        res.json({
            success: true,
            data: {
                articles,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: count,
                    itemsPerPage: limitNum,
                    hasNextPage,
                    hasPrevPage
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getArticles = getArticles;
const getArticle = async (req, res, next) => {
    try {
        const { id } = req.params;
        if (!id) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_ID',
                    message: 'Article ID is required'
                }
            });
            return;
        }
        let article = null;
        if (/^\d+$/.test(id)) {
            article = await Article_1.Article.findByPk(id, {
                include: [
                    {
                        model: User_1.User,
                        as: 'author',
                        attributes: ['id', 'username']
                    },
                    {
                        model: Tag_1.Tag,
                        as: 'tags',
                        attributes: ['id', 'name', 'slug'],
                        through: { attributes: [] }
                    },
                    {
                        model: Category_1.Category,
                        as: 'category',
                        attributes: ['id', 'name', 'slug']
                    }
                ]
            });
        }
        else {
            article = await Article_1.Article.findBySlug(id);
        }
        if (!article) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'ARTICLE_NOT_FOUND',
                    message: 'Article not found'
                }
            });
            return;
        }
        res.json({
            success: true,
            data: { article }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getArticle = getArticle;
const createArticle = async (req, res, next) => {
    try {
        const { title, content, excerpt, status = 'draft', tags = [], slug, categoryId } = req.body;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const articleData = {
            title,
            content,
            excerpt,
            status,
            authorId: req.user.id,
            categoryId: categoryId || null
        };
        if (slug && slug.trim()) {
            articleData.slug = slug.trim();
        }
        const article = await Article_1.Article.create(articleData);
        if (tags.length > 0) {
            const tagInstances = await Promise.all(tags.map(async (tagName) => {
                const [tag] = await Tag_1.Tag.findOrCreate({
                    where: { name: tagName.trim() },
                    defaults: {
                        name: tagName.trim(),
                        slug: tagName.trim().toLowerCase().replace(/\s+/g, '-')
                    }
                });
                return tag;
            }));
            await article.setTags(tagInstances);
        }
        const createdArticle = await Article_1.Article.findByPk(article.id, {
            include: [
                {
                    model: User_1.User,
                    as: 'author',
                    attributes: ['id', 'username']
                },
                {
                    model: Tag_1.Tag,
                    as: 'tags',
                    attributes: ['id', 'name', 'slug'],
                    through: { attributes: [] }
                },
                {
                    model: Category_1.Category,
                    as: 'category',
                    attributes: ['id', 'name', 'slug']
                }
            ]
        });
        res.status(201).json({
            success: true,
            data: { article: createdArticle }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.createArticle = createArticle;
const updateArticle = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { title, content, excerpt, status, tags, slug, categoryId } = req.body;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const article = await Article_1.Article.findByPk(id);
        if (!article) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'ARTICLE_NOT_FOUND',
                    message: 'Article not found'
                }
            });
            return;
        }
        if (article.authorId !== req.user.id) {
            res.status(403).json({
                success: false,
                error: {
                    code: 'FORBIDDEN',
                    message: 'You can only edit your own articles'
                }
            });
            return;
        }
        const updateData = {};
        if (title !== undefined)
            updateData.title = title;
        if (content !== undefined)
            updateData.content = content;
        if (excerpt !== undefined)
            updateData.excerpt = excerpt;
        if (status !== undefined)
            updateData.status = status;
        if (slug !== undefined && slug.trim())
            updateData.slug = slug.trim();
        if (categoryId !== undefined)
            updateData.categoryId = categoryId;
        await article.update(updateData);
        if (tags !== undefined) {
            if (tags.length > 0) {
                const tagInstances = await Promise.all(tags.map(async (tagName) => {
                    const [tag] = await Tag_1.Tag.findOrCreate({
                        where: { name: tagName.trim() },
                        defaults: {
                            name: tagName.trim(),
                            slug: tagName.trim().toLowerCase().replace(/\s+/g, '-')
                        }
                    });
                    return tag;
                }));
                await article.setTags(tagInstances);
            }
            else {
                await article.setTags([]);
            }
        }
        const updatedArticle = await Article_1.Article.findByPk(article.id, {
            include: [
                {
                    model: User_1.User,
                    as: 'author',
                    attributes: ['id', 'username']
                },
                {
                    model: Tag_1.Tag,
                    as: 'tags',
                    attributes: ['id', 'name', 'slug'],
                    through: { attributes: [] }
                },
                {
                    model: Category_1.Category,
                    as: 'category',
                    attributes: ['id', 'name', 'slug']
                }
            ]
        });
        res.json({
            success: true,
            data: { article: updatedArticle }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateArticle = updateArticle;
const deleteArticle = async (req, res, next) => {
    try {
        const { id } = req.params;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const article = await Article_1.Article.findByPk(id);
        if (!article) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'ARTICLE_NOT_FOUND',
                    message: 'Article not found'
                }
            });
            return;
        }
        if (article.authorId !== req.user.id) {
            res.status(403).json({
                success: false,
                error: {
                    code: 'FORBIDDEN',
                    message: 'You can only delete your own articles'
                }
            });
            return;
        }
        await article.destroy();
        res.json({
            success: true,
            message: 'Article deleted successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteArticle = deleteArticle;
const deleteArticles = async (req, res, next) => {
    try {
        const { ids } = req.body;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        if (!Array.isArray(ids) || ids.length === 0) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_INPUT',
                    message: 'IDs array is required and cannot be empty'
                }
            });
            return;
        }
        const articles = await Article_1.Article.findAll({
            where: {
                id: ids,
                authorId: req.user.id
            }
        });
        if (articles.length === 0) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'NO_ARTICLES_FOUND',
                    message: 'No articles found or you do not have permission to delete them'
                }
            });
            return;
        }
        await Article_1.Article.destroy({
            where: {
                id: articles.map(article => article.id)
            }
        });
        res.json({
            success: true,
            message: `${articles.length} articles deleted successfully`,
            data: {
                deletedCount: articles.length,
                deletedIds: articles.map(article => article.id)
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteArticles = deleteArticles;
const publishArticle = async (req, res, next) => {
    try {
        const { id } = req.params;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const article = await Article_1.Article.findByPk(id);
        if (!article) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'ARTICLE_NOT_FOUND',
                    message: 'Article not found'
                }
            });
            return;
        }
        if (article.authorId !== req.user.id) {
            res.status(403).json({
                success: false,
                error: {
                    code: 'FORBIDDEN',
                    message: 'You can only publish your own articles'
                }
            });
            return;
        }
        await article.publish();
        const updatedArticle = await Article_1.Article.findByPk(article.id, {
            include: [
                {
                    model: User_1.User,
                    as: 'author',
                    attributes: ['id', 'username']
                },
                {
                    model: Tag_1.Tag,
                    as: 'tags',
                    attributes: ['id', 'name', 'slug'],
                    through: { attributes: [] }
                },
                {
                    model: Category_1.Category,
                    as: 'category',
                    attributes: ['id', 'name', 'slug']
                }
            ]
        });
        res.json({
            success: true,
            data: { article: updatedArticle }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.publishArticle = publishArticle;
const unpublishArticle = async (req, res, next) => {
    try {
        const { id } = req.params;
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required'
                }
            });
            return;
        }
        const article = await Article_1.Article.findByPk(id);
        if (!article) {
            res.status(404).json({
                success: false,
                error: {
                    code: 'ARTICLE_NOT_FOUND',
                    message: 'Article not found'
                }
            });
            return;
        }
        if (article.authorId !== req.user.id) {
            res.status(403).json({
                success: false,
                error: {
                    code: 'FORBIDDEN',
                    message: 'You can only unpublish your own articles'
                }
            });
            return;
        }
        await article.unpublish();
        const updatedArticle = await Article_1.Article.findByPk(article.id, {
            include: [
                {
                    model: User_1.User,
                    as: 'author',
                    attributes: ['id', 'username']
                },
                {
                    model: Tag_1.Tag,
                    as: 'tags',
                    attributes: ['id', 'name', 'slug'],
                    through: { attributes: [] }
                },
                {
                    model: Category_1.Category,
                    as: 'category',
                    attributes: ['id', 'name', 'slug']
                }
            ]
        });
        res.json({
            success: true,
            data: { article: updatedArticle }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.unpublishArticle = unpublishArticle;
const getArticleNavigation = async (req, res, next) => {
    try {
        const { id } = req.params;
        if (!id || !/^\d+$/.test(id)) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_ID',
                    message: 'Valid article ID is required'
                }
            });
            return;
        }
        const articleId = parseInt(id, 10);
        const currentArticle = await Article_1.Article.findByPk(articleId);
        if (!currentArticle || currentArticle.status !== 'published') {
            res.status(404).json({
                success: false,
                error: {
                    code: 'ARTICLE_NOT_FOUND',
                    message: 'Published article not found'
                }
            });
            return;
        }
        const prevArticle = await Article_1.Article.findOne({
            where: {
                status: 'published',
                createdAt: { [sequelize_1.Op.gt]: currentArticle.createdAt }
            },
            order: [['createdAt', 'ASC']],
            attributes: ['id', 'title', 'slug'],
            limit: 1
        });
        const nextArticle = await Article_1.Article.findOne({
            where: {
                status: 'published',
                createdAt: { [sequelize_1.Op.lt]: currentArticle.createdAt }
            },
            order: [['createdAt', 'DESC']],
            attributes: ['id', 'title', 'slug'],
            limit: 1
        });
        res.json({
            success: true,
            data: {
                navigation: {
                    prev: prevArticle ? {
                        id: prevArticle.id,
                        title: prevArticle.title,
                        slug: prevArticle.slug
                    } : null,
                    next: nextArticle ? {
                        id: nextArticle.id,
                        title: nextArticle.title,
                        slug: nextArticle.slug
                    } : null
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getArticleNavigation = getArticleNavigation;
//# sourceMappingURL=article.js.map