import { Request, Response, NextFunction } from 'express'
import { AuthService, JWTPayload } from '../services/auth'
import { createError } from './errorHandler'

/**
 * 扩展Express的Request接口，添加用户信息属性
 */
export interface AuthenticatedRequest extends Request {
  user?: JWTPayload
}

/**
 * 身份验证中间件，用于验证JWT令牌
 * @param req - Express请求对象，包含授权头信息
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 * @returns Promise<void>
 */
export const authenticateToken = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // 从请求头中提取授权令牌
    const authHeader = req.headers['authorization']
    const token = AuthService.extractTokenFromHeader(authHeader)

    // 如果没有令牌，则抛出未授权错误
    if (!token) {
      throw createError(401, 'Access token required', 'UNAUTHORIZED')
    }

    // 验证令牌的有效性
    const result = await AuthService.validateToken(token)

    // 如果令牌无效或已过期，则抛出禁止访问错误
    if (!result.success || !result.user) {
      throw createError(403, result.message || 'Invalid or expired token', 'FORBIDDEN')
    }

    // 将用户信息附加到请求对象上
    req.user = result.user
    next()
  } catch (error) {
    next(error)
  }
}

/**
 * 可选身份验证中间件，即使没有有效令牌也允许继续执行
 * @param req - Express请求对象，包含授权头信息
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 * @returns Promise<void>
 */
export const optionalAuth = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // 从请求头中提取授权令牌
    const authHeader = req.headers['authorization']
    const token = AuthService.extractTokenFromHeader(authHeader)

    // 如果没有令牌，则直接继续执行下一个中间件
    if (!token) {
      return next()
    }

    // 验证令牌的有效性
    const result = await AuthService.validateToken(token)

    // 如果令牌有效，则将用户信息附加到请求对象上
    if (result.success && result.user) {
      req.user = result.user
    }

    next()
  } catch (error) {
    // 发生错误时也继续执行下一个中间件
    next()
  }
}