import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: number;
        username: string;
        email: string;
    };
}
export declare const postRateLimit: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const postContentSecurity: (req: Request, res: Response, next: NextFunction) => void;
export declare const postProfanityFilter: (req: Request, res: Response, next: NextFunction) => void;
export declare const checkPostAccess: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const checkPostOwnership: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const recordPostIP: (req: Request, res: Response, next: NextFunction) => void;
export declare const postSecurityMiddleware: ((req: Request, res: Response, next: NextFunction) => void)[];
export declare const createPostSecurity: ((req: AuthenticatedRequest, res: Response, next: NextFunction) => void)[];
export {};
//# sourceMappingURL=postSecurity.d.ts.map