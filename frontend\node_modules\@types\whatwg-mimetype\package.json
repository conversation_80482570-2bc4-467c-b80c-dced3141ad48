{"name": "@types/whatwg-mimetype", "version": "3.0.2", "description": "TypeScript definitions for whatwg-mimetype", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/whatwg-mimetype", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/petejohanson"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/whatwg-mimetype"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "f55ef59aeeef417baf384b11908ad22a3b3698127fb8227e258c1322eec339bf", "typeScriptVersion": "4.5"}