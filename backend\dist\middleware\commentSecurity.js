"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCommentSecurity = exports.commentSecurityMiddleware = exports.recordCommentIP = exports.checkCommentOwnership = exports.commentProfanityFilter = exports.commentContentSecurity = exports.commentRateLimit = void 0;
const errorHandler_1 = require("./errorHandler");
exports.commentRateLimit = (() => {
    const userLastCommentTime = new Map();
    const RATE_LIMIT_WINDOW = 30 * 1000;
    return (req, res, next) => {
        if (!req.user) {
            return next();
        }
        const userId = req.user.id;
        const now = Date.now();
        const lastCommentTime = userLastCommentTime.get(userId);
        if (lastCommentTime && (now - lastCommentTime) < RATE_LIMIT_WINDOW) {
            const remainingTime = Math.ceil((RATE_LIMIT_WINDOW - (now - lastCommentTime)) / 1000);
            throw (0, errorHandler_1.createError)(429, `Please wait ${remainingTime} seconds before posting another comment`, 'RATE_LIMIT_EXCEEDED');
        }
        userLastCommentTime.set(userId, now);
        if (userLastCommentTime.size > 1000) {
            const cutoffTime = now - RATE_LIMIT_WINDOW;
            for (const [id, time] of userLastCommentTime.entries()) {
                if (time < cutoffTime) {
                    userLastCommentTime.delete(id);
                }
            }
        }
        next();
    };
})();
const commentContentSecurity = (req, res, next) => {
    const { content } = req.body;
    if (!content) {
        return next();
    }
    const scriptPattern = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi;
    if (scriptPattern.test(content)) {
        throw (0, errorHandler_1.createError)(400, 'Comment content contains prohibited script tags', 'SECURITY_VIOLATION');
    }
    const maliciousLinkPattern = /javascript:|data:|vbscript:|on\w+\s*=/gi;
    if (maliciousLinkPattern.test(content)) {
        throw (0, errorHandler_1.createError)(400, 'Comment content contains prohibited links or events', 'SECURITY_VIOLATION');
    }
    if (content.length > 2000) {
        throw (0, errorHandler_1.createError)(400, 'Comment content is too long (max 2000 characters)', 'CONTENT_TOO_LONG');
    }
    if (!content.trim()) {
        throw (0, errorHandler_1.createError)(400, 'Comment content cannot be empty', 'EMPTY_CONTENT');
    }
    next();
};
exports.commentContentSecurity = commentContentSecurity;
const commentProfanityFilter = (req, res, next) => {
    const { content } = req.body;
    if (!content) {
        return next();
    }
    const profanityWords = [
        'spam', 'scam', 'fraud', 'hack', 'virus',
    ];
    const contentLower = content.toLowerCase();
    const foundProfanity = profanityWords.find(word => contentLower.includes(word));
    if (foundProfanity) {
        throw (0, errorHandler_1.createError)(400, 'Comment content contains inappropriate language', 'INAPPROPRIATE_CONTENT');
    }
    next();
};
exports.commentProfanityFilter = commentProfanityFilter;
const checkCommentOwnership = (req, res, next) => {
    next();
};
exports.checkCommentOwnership = checkCommentOwnership;
const recordCommentIP = (req, res, next) => {
    const getClientIP = () => {
        const forwardedFor = req.headers['x-forwarded-for'];
        const realIP = req.headers['x-real-ip'];
        if (typeof forwardedFor === 'string') {
            return forwardedFor.split(',')[0]?.trim() || 'unknown';
        }
        if (Array.isArray(forwardedFor) && forwardedFor[0]) {
            return forwardedFor[0];
        }
        if (typeof realIP === 'string') {
            return realIP;
        }
        return req.socket?.remoteAddress || 'unknown';
    };
    req.body.clientIP = getClientIP();
    next();
};
exports.recordCommentIP = recordCommentIP;
exports.commentSecurityMiddleware = [
    exports.commentContentSecurity,
    exports.commentProfanityFilter,
    exports.recordCommentIP
];
exports.createCommentSecurity = [
    exports.commentRateLimit,
    ...exports.commentSecurityMiddleware
];
//# sourceMappingURL=commentSecurity.js.map