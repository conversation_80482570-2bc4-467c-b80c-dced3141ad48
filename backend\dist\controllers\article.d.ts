import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: number;
        username: string;
    };
}
export declare const getArticles: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getArticle: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const createArticle: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const updateArticle: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const deleteArticle: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const deleteArticles: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const publishArticle: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const unpublishArticle: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getArticleNavigation: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export {};
//# sourceMappingURL=article.d.ts.map