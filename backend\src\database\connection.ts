import mysql from 'mysql2/promise'
import { sequelize } from '../config/database'
import dotenv from 'dotenv'

dotenv.config()

/**
 * 数据库连接管理工具
 * 提供数据库创建、连接测试等功能
 */
export class DatabaseConnection {
    private static instance: DatabaseConnection

    private constructor() { }

    /**
     * 获取单例实例
     */
    public static getInstance(): DatabaseConnection {
        if (!DatabaseConnection.instance) {
            DatabaseConnection.instance = new DatabaseConnection()
        }
        return DatabaseConnection.instance
    }

    /**
     * 创建数据库（如果不存在）
     * @returns Promise<boolean> - 创建成功返回true
     */
    async createDatabaseIfNotExists(): Promise<boolean> {
        const {
            DB_HOST = 'localhost',
            DB_PORT = '3306',
            DB_NAME = 'person-blog',
            DB_USER = 'person-blog',
            DB_PASSWORD = '123456'
        } = process.env

        let connection: mysql.Connection | null = null

        try {
            // 连接到MySQL服务器（不指定数据库）
            connection = await mysql.createConnection({
                host: DB_HOST,
                port: parseInt(DB_PORT),
                user: DB_USER,
                password: DB_PASSWORD
            })

            console.log('Connected to MySQL server')

            // 检查数据库是否存在
            const [rows] = await connection.execute(
                'SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?',
                [DB_NAME]
            )

            if ((rows as any[]).length === 0) {
                // 数据库不存在，创建它
                await connection.execute(`CREATE DATABASE \`${DB_NAME}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`)
                console.log(`Database '${DB_NAME}' created successfully`)
                return true
            } else {
                console.log(`Database '${DB_NAME}' already exists`)
                return true
            }
        } catch (error) {
            console.error('Error creating database:', error)
            return false
        } finally {
            if (connection) {
                await connection.end()
            }
        }
    }

    /**
     * 测试Sequelize数据库连接
     * @returns Promise<boolean> - 连接成功返回true
     */
    async testConnection(): Promise<boolean> {
        try {
            await sequelize.authenticate()
            console.log('Database connection has been established successfully.')
            return true
        } catch (error) {
            console.error('Unable to connect to the database:', error)
            return false
        }
    }

    /**
     * 获取数据库连接信息
     * @returns 数据库连接配置对象
     */
    getConnectionInfo() {
        const {
            DB_HOST = 'localhost',
            DB_PORT = '3306',
            DB_NAME = 'person-blog',
            DB_USER = 'person-blog'
        } = process.env

        return {
            host: DB_HOST,
            port: parseInt(DB_PORT),
            database: DB_NAME,
            username: DB_USER
        }
    }

    /**
     * 关闭数据库连接
     */
    async closeConnection(): Promise<void> {
        try {
            await sequelize.close()
            console.log('Database connection closed')
        } catch (error) {
            console.error('Error closing database connection:', error)
        }
    }
}

/**
 * 导出单例实例
 */
export const dbConnection = DatabaseConnection.getInstance()