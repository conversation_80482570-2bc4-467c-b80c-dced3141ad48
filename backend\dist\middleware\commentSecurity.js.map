{"version": 3, "file": "commentSecurity.js", "sourceRoot": "", "sources": ["../../src/middleware/commentSecurity.ts"], "names": [], "mappings": ";;;AACA,iDAA4C;AAkB/B,QAAA,gBAAgB,GAAG,CAAC,GAAG,EAAE;IAEpC,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAkB,CAAA;IACrD,MAAM,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAA;IAEnC,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC5E,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,IAAI,EAAE,CAAA;QACf,CAAC;QAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,eAAe,GAAG,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAEvD,IAAI,eAAe,IAAI,CAAC,GAAG,GAAG,eAAe,CAAC,GAAG,iBAAiB,EAAE,CAAC;YACnE,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,iBAAiB,GAAG,CAAC,GAAG,GAAG,eAAe,CAAC,CAAC,GAAG,IAAI,CAAC,CAAA;YACrF,MAAM,IAAA,0BAAW,EACf,GAAG,EACH,eAAe,aAAa,yCAAyC,EACrE,qBAAqB,CACtB,CAAA;QACH,CAAC;QAGD,mBAAmB,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;QAGpC,IAAI,mBAAmB,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC;YACpC,MAAM,UAAU,GAAG,GAAG,GAAG,iBAAiB,CAAA;YAC1C,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,mBAAmB,CAAC,OAAO,EAAE,EAAE,CAAC;gBACvD,IAAI,IAAI,GAAG,UAAU,EAAE,CAAC;oBACtB,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;gBAChC,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAA;IACR,CAAC,CAAA;AACH,CAAC,CAAC,EAAE,CAAA;AAOG,MAAM,sBAAsB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAC9F,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI,CAAA;IAE5B,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,IAAI,EAAE,CAAA;IACf,CAAC;IAGD,MAAM,aAAa,GAAG,qDAAqD,CAAA;IAC3E,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;QAChC,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,iDAAiD,EAAE,oBAAoB,CAAC,CAAA;IACjG,CAAC;IAGD,MAAM,oBAAoB,GAAG,yCAAyC,CAAA;IACtE,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;QACvC,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,qDAAqD,EAAE,oBAAoB,CAAC,CAAA;IACrG,CAAC;IAGD,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;QAC1B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,mDAAmD,EAAE,kBAAkB,CAAC,CAAA;IACjG,CAAC;IAGD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;QACpB,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,iCAAiC,EAAE,eAAe,CAAC,CAAA;IAC5E,CAAC;IAED,IAAI,EAAE,CAAA;AACR,CAAC,CAAA;AA9BY,QAAA,sBAAsB,0BA8BlC;AAOM,MAAM,sBAAsB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAC9F,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI,CAAA;IAE5B,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,IAAI,EAAE,CAAA;IACf,CAAC;IAGD,MAAM,cAAc,GAAG;QACrB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO;KAEzC,CAAA;IAED,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAA;IAC1C,MAAM,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;IAE/E,IAAI,cAAc,EAAE,CAAC;QACnB,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,iDAAiD,EAAE,uBAAuB,CAAC,CAAA;IACpG,CAAC;IAED,IAAI,EAAE,CAAA;AACR,CAAC,CAAA;AArBY,QAAA,sBAAsB,0BAqBlC;AAOM,MAAM,qBAAqB,GAAG,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAG1G,IAAI,EAAE,CAAA;AACR,CAAC,CAAA;AAJY,QAAA,qBAAqB,yBAIjC;AAOM,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAEvF,MAAM,WAAW,GAAG,GAAW,EAAE;QAC/B,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;QACnD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;QAEvC,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACrC,OAAO,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,SAAS,CAAA;QACxD,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;YACnD,OAAO,YAAY,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;QAED,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,MAAM,CAAA;QACf,CAAC;QAED,OAAO,GAAG,CAAC,MAAM,EAAE,aAAa,IAAI,SAAS,CAAA;IAC/C,CAAC,CAAA;IAGD,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,WAAW,EAAE,CAAA;IAEjC,IAAI,EAAE,CAAA;AACR,CAAC,CAAA;AAzBY,QAAA,eAAe,mBAyB3B;AAOY,QAAA,yBAAyB,GAAG;IACvC,8BAAsB;IACtB,8BAAsB;IACtB,uBAAe;CAChB,CAAA;AAMY,QAAA,qBAAqB,GAAG;IACnC,wBAAgB;IAChB,GAAG,iCAAyB;CAC7B,CAAA"}