"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('article_tags', {
        article_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            primaryKey: true,
            references: {
                model: 'articles',
                key: 'id'
            },
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        },
        tag_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            primaryKey: true,
            references: {
                model: 'tags',
                key: 'id'
            },
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        }
    });
    try {
        await queryInterface.addIndex('article_tags', ['article_id']);
    }
    catch {
        console.log('Index article_tags_article_id already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('article_tags', ['tag_id']);
    }
    catch {
        console.log('Index article_tags_tag_id already exists, skipping...');
    }
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('article_tags');
};
exports.down = down;
//# sourceMappingURL=004-create-article-tags.js.map