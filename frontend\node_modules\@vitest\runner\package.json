{"name": "@vitest/runner", "type": "module", "version": "0.34.6", "description": "V<PERSON>t test runner", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/runner"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": true, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}, "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "*.d.ts"], "dependencies": {"p-limit": "^4.0.0", "pathe": "^1.1.1", "@vitest/utils": "0.34.6"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c --watch"}}