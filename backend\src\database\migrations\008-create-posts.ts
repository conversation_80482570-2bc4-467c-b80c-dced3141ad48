import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 执行数据库迁移升级操作，创建说说表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 返回空的Promise，表示异步操作完成
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 创建说说表，包含说说内容、图片、可见性等字段
  await queryInterface.createTable('posts', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    images: {
      type: DataTypes.JSON,
      allowNull: true
    },
    visibility: {
      type: DataTypes.ENUM('public', 'private'),
      allowNull: false,
      defaultValue: 'public'
    },
    location: {
      type: DataTypes.STRING(200),
      allowNull: true
    },
    author_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  })

  // 为作者ID字段添加索引以提高查询性能
  try {
    await queryInterface.addIndex('posts', ['author_id'])
  } catch {
    console.log('Index posts_author_id already exists, skipping...')
  }

  // 为创建时间字段添加索引以提高时间线查询性能
  try {
    await queryInterface.addIndex('posts', ['created_at'])
  } catch {
    console.log('Index posts_created_at already exists, skipping...')
  }

  // 为可见性字段添加索引以提高过滤查询性能
  try {
    await queryInterface.addIndex('posts', ['visibility'])
  } catch {
    console.log('Index posts_visibility already exists, skipping...')
  }

  // 为作者ID和创建时间的组合添加索引以提高用户时间线查询性能
  try {
    await queryInterface.addIndex('posts', ['author_id', 'created_at'])
  } catch {
    console.log('Index posts_author_id_created_at already exists, skipping...')
  }
}

/**
 * 执行数据库迁移降级操作，删除说说表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 返回空的Promise，表示异步操作完成
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('posts')
}
